═══════════════════════════════════════════════════════════════
                    برنامج الأرشفة الإلكترونية
                        معلومات المشروع
═══════════════════════════════════════════════════════════════

📋 معلومات أساسية:
═══════════════════
اسم البرنامج: برنامج الأرشفة الإلكترونية
الإصدار: 1.0.0
المطور: المحاسب المبرمج علي عاجل خشان المحنة
تاريخ الإنشاء: يونيو 2025
الترخيص: MIT License

🛠️ التقنيات المستخدمة:
═══════════════════════
• Frontend: React 18.2.0 مع Vite
• Backend: Node.js مع Express
• Desktop: Electron 29.4.6
• Database: JSON File System
• Styling: Tailwind CSS مع تأثيرات مخصصة
• Animations: Framer Motion
• Icons: Lucide React

📁 هيكل المشروع:
═══════════════════
├── src/                          # ملفات React الأساسية
│   ├── components/               # مكونات الواجهة
│   │   ├── WelcomeScreen.jsx    # الشاشة الترحيبية
│   │   ├── DataEntry.jsx        # إدخال البيانات
│   │   ├── ReportView.jsx       # عرض التقارير
│   │   ├── ArchiveView.jsx      # الأرشيف العام
│   │   ├── FilterView.jsx       # التصفية المتقدمة
│   │   └── SettingsView.jsx     # الإعدادات
│   ├── styles/                  # ملفات التنسيق
│   │   └── globals.css          # الأنماط العامة
│   ├── App.jsx                  # المكون الرئيسي
│   └── main.jsx                 # نقطة البداية
├── server/                      # خادم Express
│   ├── routes/                  # مسارات API
│   │   └── records.js           # مسارات السجلات
│   ├── database.js              # إدارة قاعدة البيانات
│   └── server.js                # الخادم الرئيسي
├── dist/                        # ملفات البناء
├── dist-electron/               # الملفات التنفيذية
├── assets/                      # الأصول والأيقونات
├── electron.js                  # إعدادات Electron
├── package.json                 # إعدادات المشروع
├── vite.config.js              # إعدادات Vite
├── tailwind.config.js          # إعدادات Tailwind
└── postcss.config.js           # إعدادات PostCSS

🎨 مميزات التصميم:
═══════════════════
• واجهة مستخدم عصرية وجذابة
• ألوان زاهية ومتدرجة
• أزرار مشعة مع تأثيرات hover
• رسوم متحركة سلسة
• تصميم متجاوب
• دعم كامل للغة العربية (RTL)
• 4 ثيمات ألوان مختلفة

⚡ الوظائف الرئيسية:
═══════════════════
1. إدخال البيانات:
   • رقم الكتاب
   • التاريخ (يوم/شهر/سنة)
   • الموضوع
   • الفحوى أو اسم الموظف
   • الدائرة المعنية
   • الهامش (اختياري)

2. عرض التقارير:
   • جدول منظم لجميع السجلات
   • بحث وترتيب
   • تصدير CSV

3. الأرشيف العام:
   • تصفح مع pagination
   • تعديل وحذف السجلات
   • بحث سريع

4. التصفية المتقدمة:
   • حسب التاريخ
   • حسب الموضوع
   • حسب الموظف
   • حسب الدائرة
   • حسب وجود الهامش

5. الإعدادات:
   • تغيير الثيم
   • معلومات البرنامج

💾 قاعدة البيانات:
═══════════════════
• نوع: JSON File
• الموقع: server/archive.json
• المميزات:
  - سهولة النسخ الاحتياطي
  - لا تحتاج خادم منفصل
  - سرعة في الأداء
  - بساطة في الصيانة

🔧 متطلبات التشغيل:
═══════════════════
• نظام التشغيل: Windows 10 أو أحدث
• المعالج: Intel/AMD 64-bit
• الذاكرة: 4 GB RAM
• المساحة: 500 MB
• الشاشة: 1280x720 أو أعلى

📦 الملفات التنفيذية:
═══════════════════
• الملف الرئيسي: برنامج الأرشفة الإلكترونية Setup 1.0.0.exe
• الحجم: ~150 MB
• النوع: NSIS Installer
• التوقيع: غير موقع رقمياً

🚀 كيفية التشغيل:
═══════════════════
1. الطريقة الأولى (مستحسنة):
   - شغل ملف Setup للتثبيت
   - شغل البرنامج من قائمة ابدأ

2. الطريقة الثانية (للمطورين):
   - npm install
   - npm run electron

🔄 التحديثات المستقبلية:
═══════════════════════
• إضافة المزيد من الثيمات
• تحسين أداء البحث
• إضافة تصدير PDF
• إضافة النسخ الاحتياطي التلقائي
• دعم قواعد بيانات أخرى

📞 الدعم التقني:
═══════════════════
للحصول على الدعم أو الإبلاغ عن مشاكل:
المطور: المحاسب المبرمج علي عاجل خشان المحنة

═══════════════════════════════════════════════════════════════
                    تم إنشاء هذا البرنامج بعناية فائقة
                      لضمان أفضل تجربة مستخدم
═══════════════════════════════════════════════════════════════
