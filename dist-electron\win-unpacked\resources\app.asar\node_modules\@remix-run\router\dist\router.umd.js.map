{"version": 3, "file": "router.umd.js", "sources": ["../history.ts", "../utils.ts", "../router.ts"], "sourcesContent": ["////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Actions represent the type of change to a location value.\n */\nexport enum Action {\n  /**\n   * A POP indicates a change to an arbitrary index in the history stack, such\n   * as a back or forward navigation. It does not describe the direction of the\n   * navigation, only that the current index changed.\n   *\n   * Note: This is the default action for newly created history objects.\n   */\n  Pop = \"POP\",\n\n  /**\n   * A PUSH indicates a new entry being added to the history stack, such as when\n   * a link is clicked and a new page loads. When this happens, all subsequent\n   * entries in the stack are lost.\n   */\n  Push = \"PUSH\",\n\n  /**\n   * A REPLACE indicates the entry at the current index in the history stack\n   * being replaced by a new one.\n   */\n  Replace = \"REPLACE\",\n}\n\n/**\n * The pathname, search, and hash values of a URL.\n */\nexport interface Path {\n  /**\n   * A URL pathname, beginning with a /.\n   */\n  pathname: string;\n\n  /**\n   * A URL search string, beginning with a ?.\n   */\n  search: string;\n\n  /**\n   * A URL fragment identifier, beginning with a #.\n   */\n  hash: string;\n}\n\n// TODO: (v7) Change the Location generic default from `any` to `unknown` and\n// remove Remix `useLocation` wrapper.\n\n/**\n * An entry in a history stack. A location contains information about the\n * URL path, as well as possibly some arbitrary state and a key.\n */\nexport interface Location<State = any> extends Path {\n  /**\n   * A value of arbitrary data associated with this location.\n   */\n  state: State;\n\n  /**\n   * A unique string associated with this location. May be used to safely store\n   * and retrieve data in some other storage API, like `localStorage`.\n   *\n   * Note: This value is always \"default\" on the initial location.\n   */\n  key: string;\n}\n\n/**\n * A change to the current location.\n */\nexport interface Update {\n  /**\n   * The action that triggered the change.\n   */\n  action: Action;\n\n  /**\n   * The new location.\n   */\n  location: Location;\n\n  /**\n   * The delta between this location and the former location in the history stack\n   */\n  delta: number | null;\n}\n\n/**\n * A function that receives notifications about location changes.\n */\nexport interface Listener {\n  (update: Update): void;\n}\n\n/**\n * Describes a location that is the destination of some navigation, either via\n * `history.push` or `history.replace`. This may be either a URL or the pieces\n * of a URL path.\n */\nexport type To = string | Partial<Path>;\n\n/**\n * A history is an interface to the navigation stack. The history serves as the\n * source of truth for the current location, as well as provides a set of\n * methods that may be used to change it.\n *\n * It is similar to the DOM's `window.history` object, but with a smaller, more\n * focused API.\n */\nexport interface History {\n  /**\n   * The last action that modified the current location. This will always be\n   * Action.Pop when a history instance is first created. This value is mutable.\n   */\n  readonly action: Action;\n\n  /**\n   * The current location. This value is mutable.\n   */\n  readonly location: Location;\n\n  /**\n   * Returns a valid href for the given `to` value that may be used as\n   * the value of an <a href> attribute.\n   *\n   * @param to - The destination URL\n   */\n  createHref(to: To): string;\n\n  /**\n   * Returns a URL for the given `to` value\n   *\n   * @param to - The destination URL\n   */\n  createURL(to: To): URL;\n\n  /**\n   * Encode a location the same way window.history would do (no-op for memory\n   * history) so we ensure our PUSH/REPLACE navigations for data routers\n   * behave the same as POP\n   *\n   * @param to Unencoded path\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * Pushes a new location onto the history stack, increasing its length by one.\n   * If there were any entries in the stack after the current one, they are\n   * lost.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  push(to: To, state?: any): void;\n\n  /**\n   * Replaces the current location in the history stack with a new one.  The\n   * location that was replaced will no longer be available.\n   *\n   * @param to - The new URL\n   * @param state - Data to associate with the new location\n   */\n  replace(to: To, state?: any): void;\n\n  /**\n   * Navigates `n` entries backward/forward in the history stack relative to the\n   * current index. For example, a \"back\" navigation would use go(-1).\n   *\n   * @param delta - The delta in the stack index\n   */\n  go(delta: number): void;\n\n  /**\n   * Sets up a listener that will be called whenever the current location\n   * changes.\n   *\n   * @param listener - A function that will be called when the location changes\n   * @returns unlisten - A function that may be used to stop listening\n   */\n  listen(listener: Listener): () => void;\n}\n\ntype HistoryState = {\n  usr: any;\n  key?: string;\n  idx: number;\n};\n\nconst PopStateEventType = \"popstate\";\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Memory History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A user-supplied object that describes a location. Used when providing\n * entries to `createMemoryHistory` via its `initialEntries` option.\n */\nexport type InitialEntry = string | Partial<Location>;\n\nexport type MemoryHistoryOptions = {\n  initialEntries?: InitialEntry[];\n  initialIndex?: number;\n  v5Compat?: boolean;\n};\n\n/**\n * A memory history stores locations in memory. This is useful in stateful\n * environments where there is no web browser, such as node tests or React\n * Native.\n */\nexport interface MemoryHistory extends History {\n  /**\n   * The current index in the history stack.\n   */\n  readonly index: number;\n}\n\n/**\n * Memory history stores the current location in memory. It is designed for use\n * in stateful non-browser environments like tests and React Native.\n */\nexport function createMemoryHistory(\n  options: MemoryHistoryOptions = {}\n): MemoryHistory {\n  let { initialEntries = [\"/\"], initialIndex, v5Compat = false } = options;\n  let entries: Location[]; // Declare so we can access from createMemoryLocation\n  entries = initialEntries.map((entry, index) =>\n    createMemoryLocation(\n      entry,\n      typeof entry === \"string\" ? null : entry.state,\n      index === 0 ? \"default\" : undefined\n    )\n  );\n  let index = clampIndex(\n    initialIndex == null ? entries.length - 1 : initialIndex\n  );\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  function clampIndex(n: number): number {\n    return Math.min(Math.max(n, 0), entries.length - 1);\n  }\n  function getCurrentLocation(): Location {\n    return entries[index];\n  }\n  function createMemoryLocation(\n    to: To,\n    state: any = null,\n    key?: string\n  ): Location {\n    let location = createLocation(\n      entries ? getCurrentLocation().pathname : \"/\",\n      to,\n      state,\n      key\n    );\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in memory history: ${JSON.stringify(\n        to\n      )}`\n    );\n    return location;\n  }\n\n  function createHref(to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  let history: MemoryHistory = {\n    get index() {\n      return index;\n    },\n    get action() {\n      return action;\n    },\n    get location() {\n      return getCurrentLocation();\n    },\n    createHref,\n    createURL(to) {\n      return new URL(createHref(to), \"http://localhost\");\n    },\n    encodeLocation(to: To) {\n      let path = typeof to === \"string\" ? parsePath(to) : to;\n      return {\n        pathname: path.pathname || \"\",\n        search: path.search || \"\",\n        hash: path.hash || \"\",\n      };\n    },\n    push(to, state) {\n      action = Action.Push;\n      let nextLocation = createMemoryLocation(to, state);\n      index += 1;\n      entries.splice(index, entries.length, nextLocation);\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 1 });\n      }\n    },\n    replace(to, state) {\n      action = Action.Replace;\n      let nextLocation = createMemoryLocation(to, state);\n      entries[index] = nextLocation;\n      if (v5Compat && listener) {\n        listener({ action, location: nextLocation, delta: 0 });\n      }\n    },\n    go(delta) {\n      action = Action.Pop;\n      let nextIndex = clampIndex(index + delta);\n      let nextLocation = entries[nextIndex];\n      index = nextIndex;\n      if (listener) {\n        listener({ action, location: nextLocation, delta });\n      }\n    },\n    listen(fn: Listener) {\n      listener = fn;\n      return () => {\n        listener = null;\n      };\n    },\n  };\n\n  return history;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Browser History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A browser history stores the current location in regular URLs in a web\n * browser environment. This is the standard for most web apps and provides the\n * cleanest URLs the browser's address bar.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#browserhistory\n */\nexport interface BrowserHistory extends UrlHistory {}\n\nexport type BrowserHistoryOptions = UrlHistoryOptions;\n\n/**\n * Browser history stores the location in regular URLs. This is the standard for\n * most web apps, but it requires some configuration on the server to ensure you\n * serve the same app at multiple URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createbrowserhistory\n */\nexport function createBrowserHistory(\n  options: BrowserHistoryOptions = {}\n): BrowserHistory {\n  function createBrowserLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let { pathname, search, hash } = window.location;\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createBrowserHref(window: Window, to: To) {\n    return typeof to === \"string\" ? to : createPath(to);\n  }\n\n  return getUrlBasedHistory(\n    createBrowserLocation,\n    createBrowserHref,\n    null,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hash History\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A hash history stores the current location in the fragment identifier portion\n * of the URL in a web browser environment.\n *\n * This is ideal for apps that do not control the server for some reason\n * (because the fragment identifier is never sent to the server), including some\n * shared hosting environments that do not provide fine-grained controls over\n * which pages are served at which URLs.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#hashhistory\n */\nexport interface HashHistory extends UrlHistory {}\n\nexport type HashHistoryOptions = UrlHistoryOptions;\n\n/**\n * Hash history stores the location in window.location.hash. This makes it ideal\n * for situations where you don't want to send the location to the server for\n * some reason, either because you do cannot configure it or the URL space is\n * reserved for something else.\n *\n * @see https://github.com/remix-run/history/tree/main/docs/api-reference.md#createhashhistory\n */\nexport function createHashHistory(\n  options: HashHistoryOptions = {}\n): HashHistory {\n  function createHashLocation(\n    window: Window,\n    globalHistory: Window[\"history\"]\n  ) {\n    let {\n      pathname = \"/\",\n      search = \"\",\n      hash = \"\",\n    } = parsePath(window.location.hash.substr(1));\n\n    // Hash URL should always have a leading / just like window.location.pathname\n    // does, so if an app ends up at a route like /#something then we add a\n    // leading slash so all of our path-matching behaves the same as if it would\n    // in a browser router.  This is particularly important when there exists a\n    // root splat route (<Route path=\"*\">) since that matches internally against\n    // \"/*\" and we'd expect /#something to 404 in a hash router app.\n    if (!pathname.startsWith(\"/\") && !pathname.startsWith(\".\")) {\n      pathname = \"/\" + pathname;\n    }\n\n    return createLocation(\n      \"\",\n      { pathname, search, hash },\n      // state defaults to `null` because `window.history.state` does\n      (globalHistory.state && globalHistory.state.usr) || null,\n      (globalHistory.state && globalHistory.state.key) || \"default\"\n    );\n  }\n\n  function createHashHref(window: Window, to: To) {\n    let base = window.document.querySelector(\"base\");\n    let href = \"\";\n\n    if (base && base.getAttribute(\"href\")) {\n      let url = window.location.href;\n      let hashIndex = url.indexOf(\"#\");\n      href = hashIndex === -1 ? url : url.slice(0, hashIndex);\n    }\n\n    return href + \"#\" + (typeof to === \"string\" ? to : createPath(to));\n  }\n\n  function validateHashLocation(location: Location, to: To) {\n    warning(\n      location.pathname.charAt(0) === \"/\",\n      `relative pathnames are not supported in hash history.push(${JSON.stringify(\n        to\n      )})`\n    );\n  }\n\n  return getUrlBasedHistory(\n    createHashLocation,\n    createHashHref,\n    validateHashLocation,\n    options\n  );\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region UTILS\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * @private\n */\nexport function invariant(value: boolean, message?: string): asserts value;\nexport function invariant<T>(\n  value: T | null | undefined,\n  message?: string\n): asserts value is T;\nexport function invariant(value: any, message?: string) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    throw new Error(message);\n  }\n}\n\nexport function warning(cond: any, message: string) {\n  if (!cond) {\n    // eslint-disable-next-line no-console\n    if (typeof console !== \"undefined\") console.warn(message);\n\n    try {\n      // Welcome to debugging history!\n      //\n      // This error is thrown as a convenience, so you can more easily\n      // find the source for a warning that appears in the console by\n      // enabling \"pause on exceptions\" in your JavaScript debugger.\n      throw new Error(message);\n      // eslint-disable-next-line no-empty\n    } catch (e) {}\n  }\n}\n\nfunction createKey() {\n  return Math.random().toString(36).substr(2, 8);\n}\n\n/**\n * For browser-based histories, we combine the state and key into an object\n */\nfunction getHistoryState(location: Location, index: number): HistoryState {\n  return {\n    usr: location.state,\n    key: location.key,\n    idx: index,\n  };\n}\n\n/**\n * Creates a Location object with a unique key from the given Path\n */\nexport function createLocation(\n  current: string | Location,\n  to: To,\n  state: any = null,\n  key?: string\n): Readonly<Location> {\n  let location: Readonly<Location> = {\n    pathname: typeof current === \"string\" ? current : current.pathname,\n    search: \"\",\n    hash: \"\",\n    ...(typeof to === \"string\" ? parsePath(to) : to),\n    state,\n    // TODO: This could be cleaned up.  push/replace should probably just take\n    // full Locations now and avoid the need to run through this flow at all\n    // But that's a pretty big refactor to the current test suite so going to\n    // keep as is for the time being and just let any incoming keys take precedence\n    key: (to && (to as Location).key) || key || createKey(),\n  };\n  return location;\n}\n\n/**\n * Creates a string URL path from the given pathname, search, and hash components.\n */\nexport function createPath({\n  pathname = \"/\",\n  search = \"\",\n  hash = \"\",\n}: Partial<Path>) {\n  if (search && search !== \"?\")\n    pathname += search.charAt(0) === \"?\" ? search : \"?\" + search;\n  if (hash && hash !== \"#\")\n    pathname += hash.charAt(0) === \"#\" ? hash : \"#\" + hash;\n  return pathname;\n}\n\n/**\n * Parses a string URL path into its separate pathname, search, and hash components.\n */\nexport function parsePath(path: string): Partial<Path> {\n  let parsedPath: Partial<Path> = {};\n\n  if (path) {\n    let hashIndex = path.indexOf(\"#\");\n    if (hashIndex >= 0) {\n      parsedPath.hash = path.substr(hashIndex);\n      path = path.substr(0, hashIndex);\n    }\n\n    let searchIndex = path.indexOf(\"?\");\n    if (searchIndex >= 0) {\n      parsedPath.search = path.substr(searchIndex);\n      path = path.substr(0, searchIndex);\n    }\n\n    if (path) {\n      parsedPath.pathname = path;\n    }\n  }\n\n  return parsedPath;\n}\n\nexport interface UrlHistory extends History {}\n\nexport type UrlHistoryOptions = {\n  window?: Window;\n  v5Compat?: boolean;\n};\n\nfunction getUrlBasedHistory(\n  getLocation: (window: Window, globalHistory: Window[\"history\"]) => Location,\n  createHref: (window: Window, to: To) => string,\n  validateLocation: ((location: Location, to: To) => void) | null,\n  options: UrlHistoryOptions = {}\n): UrlHistory {\n  let { window = document.defaultView!, v5Compat = false } = options;\n  let globalHistory = window.history;\n  let action = Action.Pop;\n  let listener: Listener | null = null;\n\n  let index = getIndex()!;\n  // Index should only be null when we initialize. If not, it's because the\n  // user called history.pushState or history.replaceState directly, in which\n  // case we should log a warning as it will result in bugs.\n  if (index == null) {\n    index = 0;\n    globalHistory.replaceState({ ...globalHistory.state, idx: index }, \"\");\n  }\n\n  function getIndex(): number {\n    let state = globalHistory.state || { idx: null };\n    return state.idx;\n  }\n\n  function handlePop() {\n    action = Action.Pop;\n    let nextIndex = getIndex();\n    let delta = nextIndex == null ? null : nextIndex - index;\n    index = nextIndex;\n    if (listener) {\n      listener({ action, location: history.location, delta });\n    }\n  }\n\n  function push(to: To, state?: any) {\n    action = Action.Push;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex() + 1;\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n\n    // try...catch because iOS limits us to 100 pushState calls :/\n    try {\n      globalHistory.pushState(historyState, \"\", url);\n    } catch (error) {\n      // If the exception is because `state` can't be serialized, let that throw\n      // outwards just like a replace call would so the dev knows the cause\n      // https://html.spec.whatwg.org/multipage/nav-history-apis.html#shared-history-push/replace-state-steps\n      // https://html.spec.whatwg.org/multipage/structured-data.html#structuredserializeinternal\n      if (error instanceof DOMException && error.name === \"DataCloneError\") {\n        throw error;\n      }\n      // They are going to lose state here, but there is no real\n      // way to warn them about it since the page will refresh...\n      window.location.assign(url);\n    }\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 1 });\n    }\n  }\n\n  function replace(to: To, state?: any) {\n    action = Action.Replace;\n    let location = createLocation(history.location, to, state);\n    if (validateLocation) validateLocation(location, to);\n\n    index = getIndex();\n    let historyState = getHistoryState(location, index);\n    let url = history.createHref(location);\n    globalHistory.replaceState(historyState, \"\", url);\n\n    if (v5Compat && listener) {\n      listener({ action, location: history.location, delta: 0 });\n    }\n  }\n\n  function createURL(to: To): URL {\n    // window.location.origin is \"null\" (the literal string value) in Firefox\n    // under certain conditions, notably when serving from a local HTML file\n    // See https://bugzilla.mozilla.org/show_bug.cgi?id=878297\n    let base =\n      window.location.origin !== \"null\"\n        ? window.location.origin\n        : window.location.href;\n\n    let href = typeof to === \"string\" ? to : createPath(to);\n    // Treating this as a full URL will strip any trailing spaces so we need to\n    // pre-encode them since they might be part of a matching splat param from\n    // an ancestor route\n    href = href.replace(/ $/, \"%20\");\n    invariant(\n      base,\n      `No window.location.(origin|href) available to create URL for href: ${href}`\n    );\n    return new URL(href, base);\n  }\n\n  let history: History = {\n    get action() {\n      return action;\n    },\n    get location() {\n      return getLocation(window, globalHistory);\n    },\n    listen(fn: Listener) {\n      if (listener) {\n        throw new Error(\"A history only accepts one active listener\");\n      }\n      window.addEventListener(PopStateEventType, handlePop);\n      listener = fn;\n\n      return () => {\n        window.removeEventListener(PopStateEventType, handlePop);\n        listener = null;\n      };\n    },\n    createHref(to) {\n      return createHref(window, to);\n    },\n    createURL,\n    encodeLocation(to) {\n      // Encode a Location the same way window.location would\n      let url = createURL(to);\n      return {\n        pathname: url.pathname,\n        search: url.search,\n        hash: url.hash,\n      };\n    },\n    push,\n    replace,\n    go(n) {\n      return globalHistory.go(n);\n    },\n  };\n\n  return history;\n}\n\n//#endregion\n", "import type { Location, Path, To } from \"./history\";\nimport { invariant, parsePath, warning } from \"./history\";\n\n/**\n * Map of routeId -> data returned from a loader/action/error\n */\nexport interface RouteData {\n  [routeId: string]: any;\n}\n\nexport enum ResultType {\n  data = \"data\",\n  deferred = \"deferred\",\n  redirect = \"redirect\",\n  error = \"error\",\n}\n\n/**\n * Successful result from a loader or action\n */\nexport interface SuccessResult {\n  type: ResultType.data;\n  data: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Successful defer() result from a loader or action\n */\nexport interface DeferredResult {\n  type: ResultType.deferred;\n  deferredData: DeferredData;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Redirect result from a loader or action\n */\nexport interface RedirectResult {\n  type: ResultType.redirect;\n  // We keep the raw Response for redirects so we can return it verbatim\n  response: Response;\n}\n\n/**\n * Unsuccessful result from a loader or action\n */\nexport interface ErrorResult {\n  type: ResultType.error;\n  error: unknown;\n  statusCode?: number;\n  headers?: Headers;\n}\n\n/**\n * Result from a loader or action - potentially successful or unsuccessful\n */\nexport type DataResult =\n  | SuccessResult\n  | DeferredResult\n  | RedirectResult\n  | ErrorResult;\n\ntype LowerCaseFormMethod = \"get\" | \"post\" | \"put\" | \"patch\" | \"delete\";\ntype UpperCaseFormMethod = Uppercase<LowerCaseFormMethod>;\n\n/**\n * Users can specify either lowercase or uppercase form methods on `<Form>`,\n * useSubmit(), `<fetcher.Form>`, etc.\n */\nexport type HTMLFormMethod = LowerCaseFormMethod | UpperCaseFormMethod;\n\n/**\n * Active navigation/fetcher form methods are exposed in lowercase on the\n * RouterState\n */\nexport type FormMethod = LowerCaseFormMethod;\nexport type MutationFormMethod = Exclude<FormMethod, \"get\">;\n\n/**\n * In v7, active navigation/fetcher form methods are exposed in uppercase on the\n * RouterState.  This is to align with the normalization done via fetch().\n */\nexport type V7_FormMethod = UpperCaseFormMethod;\nexport type V7_MutationFormMethod = Exclude<V7_FormMethod, \"GET\">;\n\nexport type FormEncType =\n  | \"application/x-www-form-urlencoded\"\n  | \"multipart/form-data\"\n  | \"application/json\"\n  | \"text/plain\";\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\n/**\n * @private\n * Internal interface to pass around for action submissions, not intended for\n * external consumption\n */\nexport type Submission =\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: FormData;\n      json: undefined;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: JsonValue;\n      text: undefined;\n    }\n  | {\n      formMethod: FormMethod | V7_FormMethod;\n      formAction: string;\n      formEncType: FormEncType;\n      formData: undefined;\n      json: undefined;\n      text: string;\n    };\n\n/**\n * @private\n * Arguments passed to route loader/action functions.  Same for now but we keep\n * this as a private implementation detail in case they diverge in the future.\n */\ninterface DataFunctionArgs<Context> {\n  request: Request;\n  params: Params;\n  context?: Context;\n}\n\n// TODO: (v7) Change the defaults from any to unknown in and remove Remix wrappers:\n//   ActionFunction, ActionFunctionArgs, LoaderFunction, LoaderFunctionArgs\n//   Also, make them a type alias instead of an interface\n\n/**\n * Arguments passed to loader functions\n */\nexport interface LoaderFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Arguments passed to action functions\n */\nexport interface ActionFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {}\n\n/**\n * Loaders and actions can return anything except `undefined` (`null` is a\n * valid return value if there is no data to return).  Responses are preferred\n * and will ease any future migration to Remix\n */\ntype DataFunctionValue = Response | NonNullable<unknown> | null;\n\ntype DataFunctionReturnValue = Promise<DataFunctionValue> | DataFunctionValue;\n\n/**\n * Route loader function signature\n */\nexport type LoaderFunction<Context = any> = {\n  (\n    args: LoaderFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n} & { hydrate?: boolean };\n\n/**\n * Route action function signature\n */\nexport interface ActionFunction<Context = any> {\n  (\n    args: ActionFunctionArgs<Context>,\n    handlerCtx?: unknown\n  ): DataFunctionReturnValue;\n}\n\n/**\n * Arguments passed to shouldRevalidate function\n */\nexport interface ShouldRevalidateFunctionArgs {\n  currentUrl: URL;\n  currentParams: AgnosticDataRouteMatch[\"params\"];\n  nextUrl: URL;\n  nextParams: AgnosticDataRouteMatch[\"params\"];\n  formMethod?: Submission[\"formMethod\"];\n  formAction?: Submission[\"formAction\"];\n  formEncType?: Submission[\"formEncType\"];\n  text?: Submission[\"text\"];\n  formData?: Submission[\"formData\"];\n  json?: Submission[\"json\"];\n  actionStatus?: number;\n  actionResult?: any;\n  defaultShouldRevalidate: boolean;\n}\n\n/**\n * Route shouldRevalidate function signature.  This runs after any submission\n * (navigation or fetcher), so we flatten the navigation/fetcher submission\n * onto the arguments.  It shouldn't matter whether it came from a navigation\n * or a fetcher, what really matters is the URLs and the formData since loaders\n * have to re-run based on the data models that were potentially mutated.\n */\nexport interface ShouldRevalidateFunction {\n  (args: ShouldRevalidateFunctionArgs): boolean;\n}\n\n/**\n * Function provided by the framework-aware layers to set `hasErrorBoundary`\n * from the framework-aware `errorElement` prop\n *\n * @deprecated Use `mapRouteProperties` instead\n */\nexport interface DetectErrorBoundaryFunction {\n  (route: AgnosticRouteObject): boolean;\n}\n\nexport interface DataStrategyMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {\n  shouldLoad: boolean;\n  resolve: (\n    handlerOverride?: (\n      handler: (ctx?: unknown) => DataFunctionReturnValue\n    ) => DataFunctionReturnValue\n  ) => Promise<DataStrategyResult>;\n}\n\nexport interface DataStrategyFunctionArgs<Context = any>\n  extends DataFunctionArgs<Context> {\n  matches: DataStrategyMatch[];\n  fetcherKey: string | null;\n}\n\n/**\n * Result from a loader or action called via dataStrategy\n */\nexport interface DataStrategyResult {\n  type: \"data\" | \"error\";\n  result: unknown; // data, Error, Response, DeferredData, DataWithResponseInit\n}\n\nexport interface DataStrategyFunction {\n  (args: DataStrategyFunctionArgs): Promise<Record<string, DataStrategyResult>>;\n}\n\nexport type AgnosticPatchRoutesOnNavigationFunctionArgs<\n  O extends AgnosticRouteObject = AgnosticRouteObject,\n  M extends AgnosticRouteMatch = AgnosticRouteMatch\n> = {\n  signal: AbortSignal;\n  path: string;\n  matches: M[];\n  fetcherKey: string | undefined;\n  patch: (routeId: string | null, children: O[]) => void;\n};\n\nexport type AgnosticPatchRoutesOnNavigationFunction<\n  O extends AgnosticRouteObject = AgnosticRouteObject,\n  M extends AgnosticRouteMatch = AgnosticRouteMatch\n> = (\n  opts: AgnosticPatchRoutesOnNavigationFunctionArgs<O, M>\n) => void | Promise<void>;\n\n/**\n * Function provided by the framework-aware layers to set any framework-specific\n * properties from framework-agnostic properties\n */\nexport interface MapRoutePropertiesFunction {\n  (route: AgnosticRouteObject): {\n    hasErrorBoundary: boolean;\n  } & Record<string, any>;\n}\n\n/**\n * Keys we cannot change from within a lazy() function. We spread all other keys\n * onto the route. Either they're meaningful to the router, or they'll get\n * ignored.\n */\nexport type ImmutableRouteKey =\n  | \"lazy\"\n  | \"caseSensitive\"\n  | \"path\"\n  | \"id\"\n  | \"index\"\n  | \"children\";\n\nexport const immutableRouteKeys = new Set<ImmutableRouteKey>([\n  \"lazy\",\n  \"caseSensitive\",\n  \"path\",\n  \"id\",\n  \"index\",\n  \"children\",\n]);\n\ntype RequireOne<T, Key = keyof T> = Exclude<\n  {\n    [K in keyof T]: K extends Key ? Omit<T, K> & Required<Pick<T, K>> : never;\n  }[keyof T],\n  undefined\n>;\n\n/**\n * lazy() function to load a route definition, which can add non-matching\n * related properties to a route\n */\nexport interface LazyRouteFunction<R extends AgnosticRouteObject> {\n  (): Promise<RequireOne<Omit<R, ImmutableRouteKey>>>;\n}\n\n/**\n * Base RouteObject with common props shared by all types of routes\n */\ntype AgnosticBaseRouteObject = {\n  caseSensitive?: boolean;\n  path?: string;\n  id?: string;\n  loader?: LoaderFunction | boolean;\n  action?: ActionFunction | boolean;\n  hasErrorBoundary?: boolean;\n  shouldRevalidate?: ShouldRevalidateFunction;\n  handle?: any;\n  lazy?: LazyRouteFunction<AgnosticBaseRouteObject>;\n};\n\n/**\n * Index routes must not have children\n */\nexport type AgnosticIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: undefined;\n  index: true;\n};\n\n/**\n * Non-index routes may have children, but cannot have index\n */\nexport type AgnosticNonIndexRouteObject = AgnosticBaseRouteObject & {\n  children?: AgnosticRouteObject[];\n  index?: false;\n};\n\n/**\n * A route object represents a logical route, with (optionally) its child\n * routes organized in a tree-like structure.\n */\nexport type AgnosticRouteObject =\n  | AgnosticIndexRouteObject\n  | AgnosticNonIndexRouteObject;\n\nexport type AgnosticDataIndexRouteObject = AgnosticIndexRouteObject & {\n  id: string;\n};\n\nexport type AgnosticDataNonIndexRouteObject = AgnosticNonIndexRouteObject & {\n  children?: AgnosticDataRouteObject[];\n  id: string;\n};\n\n/**\n * A data route object, which is just a RouteObject with a required unique ID\n */\nexport type AgnosticDataRouteObject =\n  | AgnosticDataIndexRouteObject\n  | AgnosticDataNonIndexRouteObject;\n\nexport type RouteManifest = Record<string, AgnosticDataRouteObject | undefined>;\n\n// Recursive helper for finding path parameters in the absence of wildcards\ntype _PathParam<Path extends string> =\n  // split path into individual path segments\n  Path extends `${infer L}/${infer R}`\n    ? _PathParam<L> | _PathParam<R>\n    : // find params after `:`\n    Path extends `:${infer Param}`\n    ? Param extends `${infer Optional}?`\n      ? Optional\n      : Param\n    : // otherwise, there aren't any params present\n      never;\n\n/**\n * Examples:\n * \"/a/b/*\" -> \"*\"\n * \":a\" -> \"a\"\n * \"/a/:b\" -> \"b\"\n * \"/a/blahblahblah:b\" -> \"b\"\n * \"/:a/:b\" -> \"a\" | \"b\"\n * \"/:a/b/:c/*\" -> \"a\" | \"c\" | \"*\"\n */\nexport type PathParam<Path extends string> =\n  // check if path is just a wildcard\n  Path extends \"*\" | \"/*\"\n    ? \"*\"\n    : // look for wildcard at the end of the path\n    Path extends `${infer Rest}/*`\n    ? \"*\" | _PathParam<Rest>\n    : // look for params in the absence of wildcards\n      _PathParam<Path>;\n\n// Attempt to parse the given string segment. If it fails, then just return the\n// plain string type as a default fallback. Otherwise, return the union of the\n// parsed string literals that were referenced as dynamic segments in the route.\nexport type ParamParseKey<Segment extends string> =\n  // if you could not find path params, fallback to `string`\n  [PathParam<Segment>] extends [never] ? string : PathParam<Segment>;\n\n/**\n * The parameters that were parsed from the URL path.\n */\nexport type Params<Key extends string = string> = {\n  readonly [key in Key]: string | undefined;\n};\n\n/**\n * A RouteMatch contains info about how a route matched a URL.\n */\nexport interface AgnosticRouteMatch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The route object that was used to match.\n   */\n  route: RouteObjectType;\n}\n\nexport interface AgnosticDataRouteMatch\n  extends AgnosticRouteMatch<string, AgnosticDataRouteObject> {}\n\nfunction isIndexRoute(\n  route: AgnosticRouteObject\n): route is AgnosticIndexRouteObject {\n  return route.index === true;\n}\n\n// Walk the route tree generating unique IDs where necessary, so we are working\n// solely with AgnosticDataRouteObject's within the Router\nexport function convertRoutesToDataRoutes(\n  routes: AgnosticRouteObject[],\n  mapRouteProperties: MapRoutePropertiesFunction,\n  parentPath: string[] = [],\n  manifest: RouteManifest = {}\n): AgnosticDataRouteObject[] {\n  return routes.map((route, index) => {\n    let treePath = [...parentPath, String(index)];\n    let id = typeof route.id === \"string\" ? route.id : treePath.join(\"-\");\n    invariant(\n      route.index !== true || !route.children,\n      `Cannot specify children on an index route`\n    );\n    invariant(\n      !manifest[id],\n      `Found a route id collision on id \"${id}\".  Route ` +\n        \"id's must be globally unique within Data Router usages\"\n    );\n\n    if (isIndexRoute(route)) {\n      let indexRoute: AgnosticDataIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n      };\n      manifest[id] = indexRoute;\n      return indexRoute;\n    } else {\n      let pathOrLayoutRoute: AgnosticDataNonIndexRouteObject = {\n        ...route,\n        ...mapRouteProperties(route),\n        id,\n        children: undefined,\n      };\n      manifest[id] = pathOrLayoutRoute;\n\n      if (route.children) {\n        pathOrLayoutRoute.children = convertRoutesToDataRoutes(\n          route.children,\n          mapRouteProperties,\n          treePath,\n          manifest\n        );\n      }\n\n      return pathOrLayoutRoute;\n    }\n  });\n}\n\n/**\n * Matches the given routes to a location and returns the match data.\n *\n * @see https://reactrouter.com/v6/utils/match-routes\n */\nexport function matchRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename = \"/\"\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  return matchRoutesImpl(routes, locationArg, basename, false);\n}\n\nexport function matchRoutesImpl<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  locationArg: Partial<Location> | string,\n  basename: string,\n  allowPartial: boolean\n): AgnosticRouteMatch<string, RouteObjectType>[] | null {\n  let location =\n    typeof locationArg === \"string\" ? parsePath(locationArg) : locationArg;\n\n  let pathname = stripBasename(location.pathname || \"/\", basename);\n\n  if (pathname == null) {\n    return null;\n  }\n\n  let branches = flattenRoutes(routes);\n  rankRouteBranches(branches);\n\n  let matches = null;\n  for (let i = 0; matches == null && i < branches.length; ++i) {\n    // Incoming pathnames are generally encoded from either window.location\n    // or from router.navigate, but we want to match against the unencoded\n    // paths in the route definitions.  Memory router locations won't be\n    // encoded here but there also shouldn't be anything to decode so this\n    // should be a safe operation.  This avoids needing matchRoutes to be\n    // history-aware.\n    let decoded = decodePath(pathname);\n    matches = matchRouteBranch<string, RouteObjectType>(\n      branches[i],\n      decoded,\n      allowPartial\n    );\n  }\n\n  return matches;\n}\n\nexport interface UIMatch<Data = unknown, Handle = unknown> {\n  id: string;\n  pathname: string;\n  params: AgnosticRouteMatch[\"params\"];\n  data: Data;\n  handle: Handle;\n}\n\nexport function convertRouteMatchToUiMatch(\n  match: AgnosticDataRouteMatch,\n  loaderData: RouteData\n): UIMatch {\n  let { route, pathname, params } = match;\n  return {\n    id: route.id,\n    pathname,\n    params,\n    data: loaderData[route.id],\n    handle: route.handle,\n  };\n}\n\ninterface RouteMeta<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  relativePath: string;\n  caseSensitive: boolean;\n  childrenIndex: number;\n  route: RouteObjectType;\n}\n\ninterface RouteBranch<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n> {\n  path: string;\n  score: number;\n  routesMeta: RouteMeta<RouteObjectType>[];\n}\n\nfunction flattenRoutes<\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  routes: RouteObjectType[],\n  branches: RouteBranch<RouteObjectType>[] = [],\n  parentsMeta: RouteMeta<RouteObjectType>[] = [],\n  parentPath = \"\"\n): RouteBranch<RouteObjectType>[] {\n  let flattenRoute = (\n    route: RouteObjectType,\n    index: number,\n    relativePath?: string\n  ) => {\n    let meta: RouteMeta<RouteObjectType> = {\n      relativePath:\n        relativePath === undefined ? route.path || \"\" : relativePath,\n      caseSensitive: route.caseSensitive === true,\n      childrenIndex: index,\n      route,\n    };\n\n    if (meta.relativePath.startsWith(\"/\")) {\n      invariant(\n        meta.relativePath.startsWith(parentPath),\n        `Absolute route path \"${meta.relativePath}\" nested under path ` +\n          `\"${parentPath}\" is not valid. An absolute child route path ` +\n          `must start with the combined path of all its parent routes.`\n      );\n\n      meta.relativePath = meta.relativePath.slice(parentPath.length);\n    }\n\n    let path = joinPaths([parentPath, meta.relativePath]);\n    let routesMeta = parentsMeta.concat(meta);\n\n    // Add the children before adding this route to the array, so we traverse the\n    // route tree depth-first and child routes appear before their parents in\n    // the \"flattened\" version.\n    if (route.children && route.children.length > 0) {\n      invariant(\n        // Our types know better, but runtime JS may not!\n        // @ts-expect-error\n        route.index !== true,\n        `Index routes must not have child routes. Please remove ` +\n          `all child routes from route path \"${path}\".`\n      );\n      flattenRoutes(route.children, branches, routesMeta, path);\n    }\n\n    // Routes without a path shouldn't ever match by themselves unless they are\n    // index routes, so don't add them to the list of possible branches.\n    if (route.path == null && !route.index) {\n      return;\n    }\n\n    branches.push({\n      path,\n      score: computeScore(path, route.index),\n      routesMeta,\n    });\n  };\n  routes.forEach((route, index) => {\n    // coarse-grain check for optional params\n    if (route.path === \"\" || !route.path?.includes(\"?\")) {\n      flattenRoute(route, index);\n    } else {\n      for (let exploded of explodeOptionalSegments(route.path)) {\n        flattenRoute(route, index, exploded);\n      }\n    }\n  });\n\n  return branches;\n}\n\n/**\n * Computes all combinations of optional path segments for a given path,\n * excluding combinations that are ambiguous and of lower priority.\n *\n * For example, `/one/:two?/three/:four?/:five?` explodes to:\n * - `/one/three`\n * - `/one/:two/three`\n * - `/one/three/:four`\n * - `/one/three/:five`\n * - `/one/:two/three/:four`\n * - `/one/:two/three/:five`\n * - `/one/three/:four/:five`\n * - `/one/:two/three/:four/:five`\n */\nfunction explodeOptionalSegments(path: string): string[] {\n  let segments = path.split(\"/\");\n  if (segments.length === 0) return [];\n\n  let [first, ...rest] = segments;\n\n  // Optional path segments are denoted by a trailing `?`\n  let isOptional = first.endsWith(\"?\");\n  // Compute the corresponding required segment: `foo?` -> `foo`\n  let required = first.replace(/\\?$/, \"\");\n\n  if (rest.length === 0) {\n    // Intepret empty string as omitting an optional segment\n    // `[\"one\", \"\", \"three\"]` corresponds to omitting `:two` from `/one/:two?/three` -> `/one/three`\n    return isOptional ? [required, \"\"] : [required];\n  }\n\n  let restExploded = explodeOptionalSegments(rest.join(\"/\"));\n\n  let result: string[] = [];\n\n  // All child paths with the prefix.  Do this for all children before the\n  // optional version for all children, so we get consistent ordering where the\n  // parent optional aspect is preferred as required.  Otherwise, we can get\n  // child sections interspersed where deeper optional segments are higher than\n  // parent optional segments, where for example, /:two would explode _earlier_\n  // then /:one.  By always including the parent as required _for all children_\n  // first, we avoid this issue\n  result.push(\n    ...restExploded.map((subpath) =>\n      subpath === \"\" ? required : [required, subpath].join(\"/\")\n    )\n  );\n\n  // Then, if this is an optional value, add all child versions without\n  if (isOptional) {\n    result.push(...restExploded);\n  }\n\n  // for absolute paths, ensure `/` instead of empty segment\n  return result.map((exploded) =>\n    path.startsWith(\"/\") && exploded === \"\" ? \"/\" : exploded\n  );\n}\n\nfunction rankRouteBranches(branches: RouteBranch[]): void {\n  branches.sort((a, b) =>\n    a.score !== b.score\n      ? b.score - a.score // Higher score first\n      : compareIndexes(\n          a.routesMeta.map((meta) => meta.childrenIndex),\n          b.routesMeta.map((meta) => meta.childrenIndex)\n        )\n  );\n}\n\nconst paramRe = /^:[\\w-]+$/;\nconst dynamicSegmentValue = 3;\nconst indexRouteValue = 2;\nconst emptySegmentValue = 1;\nconst staticSegmentValue = 10;\nconst splatPenalty = -2;\nconst isSplat = (s: string) => s === \"*\";\n\nfunction computeScore(path: string, index: boolean | undefined): number {\n  let segments = path.split(\"/\");\n  let initialScore = segments.length;\n  if (segments.some(isSplat)) {\n    initialScore += splatPenalty;\n  }\n\n  if (index) {\n    initialScore += indexRouteValue;\n  }\n\n  return segments\n    .filter((s) => !isSplat(s))\n    .reduce(\n      (score, segment) =>\n        score +\n        (paramRe.test(segment)\n          ? dynamicSegmentValue\n          : segment === \"\"\n          ? emptySegmentValue\n          : staticSegmentValue),\n      initialScore\n    );\n}\n\nfunction compareIndexes(a: number[], b: number[]): number {\n  let siblings =\n    a.length === b.length && a.slice(0, -1).every((n, i) => n === b[i]);\n\n  return siblings\n    ? // If two routes are siblings, we should try to match the earlier sibling\n      // first. This allows people to have fine-grained control over the matching\n      // behavior by simply putting routes with identical paths in the order they\n      // want them tried.\n      a[a.length - 1] - b[b.length - 1]\n    : // Otherwise, it doesn't really make sense to rank non-siblings by index,\n      // so they sort equally.\n      0;\n}\n\nfunction matchRouteBranch<\n  ParamKey extends string = string,\n  RouteObjectType extends AgnosticRouteObject = AgnosticRouteObject\n>(\n  branch: RouteBranch<RouteObjectType>,\n  pathname: string,\n  allowPartial = false\n): AgnosticRouteMatch<ParamKey, RouteObjectType>[] | null {\n  let { routesMeta } = branch;\n\n  let matchedParams = {};\n  let matchedPathname = \"/\";\n  let matches: AgnosticRouteMatch<ParamKey, RouteObjectType>[] = [];\n  for (let i = 0; i < routesMeta.length; ++i) {\n    let meta = routesMeta[i];\n    let end = i === routesMeta.length - 1;\n    let remainingPathname =\n      matchedPathname === \"/\"\n        ? pathname\n        : pathname.slice(matchedPathname.length) || \"/\";\n    let match = matchPath(\n      { path: meta.relativePath, caseSensitive: meta.caseSensitive, end },\n      remainingPathname\n    );\n\n    let route = meta.route;\n\n    if (\n      !match &&\n      end &&\n      allowPartial &&\n      !routesMeta[routesMeta.length - 1].route.index\n    ) {\n      match = matchPath(\n        {\n          path: meta.relativePath,\n          caseSensitive: meta.caseSensitive,\n          end: false,\n        },\n        remainingPathname\n      );\n    }\n\n    if (!match) {\n      return null;\n    }\n\n    Object.assign(matchedParams, match.params);\n\n    matches.push({\n      // TODO: Can this as be avoided?\n      params: matchedParams as Params<ParamKey>,\n      pathname: joinPaths([matchedPathname, match.pathname]),\n      pathnameBase: normalizePathname(\n        joinPaths([matchedPathname, match.pathnameBase])\n      ),\n      route,\n    });\n\n    if (match.pathnameBase !== \"/\") {\n      matchedPathname = joinPaths([matchedPathname, match.pathnameBase]);\n    }\n  }\n\n  return matches;\n}\n\n/**\n * Returns a path with params interpolated.\n *\n * @see https://reactrouter.com/v6/utils/generate-path\n */\nexport function generatePath<Path extends string>(\n  originalPath: Path,\n  params: {\n    [key in PathParam<Path>]: string | null;\n  } = {} as any\n): string {\n  let path: string = originalPath;\n  if (path.endsWith(\"*\") && path !== \"*\" && !path.endsWith(\"/*\")) {\n    warning(\n      false,\n      `Route path \"${path}\" will be treated as if it were ` +\n        `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n        `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n        `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n    );\n    path = path.replace(/\\*$/, \"/*\") as Path;\n  }\n\n  // ensure `/` is added at the beginning if the path is absolute\n  const prefix = path.startsWith(\"/\") ? \"/\" : \"\";\n\n  const stringify = (p: any) =>\n    p == null ? \"\" : typeof p === \"string\" ? p : String(p);\n\n  const segments = path\n    .split(/\\/+/)\n    .map((segment, index, array) => {\n      const isLastSegment = index === array.length - 1;\n\n      // only apply the splat if it's the last segment\n      if (isLastSegment && segment === \"*\") {\n        const star = \"*\" as PathParam<Path>;\n        // Apply the splat\n        return stringify(params[star]);\n      }\n\n      const keyMatch = segment.match(/^:([\\w-]+)(\\??)$/);\n      if (keyMatch) {\n        const [, key, optional] = keyMatch;\n        let param = params[key as PathParam<Path>];\n        invariant(optional === \"?\" || param != null, `Missing \":${key}\" param`);\n        return stringify(param);\n      }\n\n      // Remove any optional markers from optional static segments\n      return segment.replace(/\\?$/g, \"\");\n    })\n    // Remove empty segments\n    .filter((segment) => !!segment);\n\n  return prefix + segments.join(\"/\");\n}\n\n/**\n * A PathPattern is used to match on some portion of a URL pathname.\n */\nexport interface PathPattern<Path extends string = string> {\n  /**\n   * A string to match against a URL pathname. May contain `:id`-style segments\n   * to indicate placeholders for dynamic parameters. May also end with `/*` to\n   * indicate matching the rest of the URL pathname.\n   */\n  path: Path;\n  /**\n   * Should be `true` if the static portions of the `path` should be matched in\n   * the same case.\n   */\n  caseSensitive?: boolean;\n  /**\n   * Should be `true` if this pattern should match the entire URL pathname.\n   */\n  end?: boolean;\n}\n\n/**\n * A PathMatch contains info about how a PathPattern matched on a URL pathname.\n */\nexport interface PathMatch<ParamKey extends string = string> {\n  /**\n   * The names and values of dynamic parameters in the URL.\n   */\n  params: Params<ParamKey>;\n  /**\n   * The portion of the URL pathname that was matched.\n   */\n  pathname: string;\n  /**\n   * The portion of the URL pathname that was matched before child routes.\n   */\n  pathnameBase: string;\n  /**\n   * The pattern that was used to match.\n   */\n  pattern: PathPattern;\n}\n\ntype Mutable<T> = {\n  -readonly [P in keyof T]: T[P];\n};\n\n/**\n * Performs pattern matching on a URL pathname and returns information about\n * the match.\n *\n * @see https://reactrouter.com/v6/utils/match-path\n */\nexport function matchPath<\n  ParamKey extends ParamParseKey<Path>,\n  Path extends string\n>(\n  pattern: PathPattern<Path> | Path,\n  pathname: string\n): PathMatch<ParamKey> | null {\n  if (typeof pattern === \"string\") {\n    pattern = { path: pattern, caseSensitive: false, end: true };\n  }\n\n  let [matcher, compiledParams] = compilePath(\n    pattern.path,\n    pattern.caseSensitive,\n    pattern.end\n  );\n\n  let match = pathname.match(matcher);\n  if (!match) return null;\n\n  let matchedPathname = match[0];\n  let pathnameBase = matchedPathname.replace(/(.)\\/+$/, \"$1\");\n  let captureGroups = match.slice(1);\n  let params: Params = compiledParams.reduce<Mutable<Params>>(\n    (memo, { paramName, isOptional }, index) => {\n      // We need to compute the pathnameBase here using the raw splat value\n      // instead of using params[\"*\"] later because it will be decoded then\n      if (paramName === \"*\") {\n        let splatValue = captureGroups[index] || \"\";\n        pathnameBase = matchedPathname\n          .slice(0, matchedPathname.length - splatValue.length)\n          .replace(/(.)\\/+$/, \"$1\");\n      }\n\n      const value = captureGroups[index];\n      if (isOptional && !value) {\n        memo[paramName] = undefined;\n      } else {\n        memo[paramName] = (value || \"\").replace(/%2F/g, \"/\");\n      }\n      return memo;\n    },\n    {}\n  );\n\n  return {\n    params,\n    pathname: matchedPathname,\n    pathnameBase,\n    pattern,\n  };\n}\n\ntype CompiledPathParam = { paramName: string; isOptional?: boolean };\n\nfunction compilePath(\n  path: string,\n  caseSensitive = false,\n  end = true\n): [RegExp, CompiledPathParam[]] {\n  warning(\n    path === \"*\" || !path.endsWith(\"*\") || path.endsWith(\"/*\"),\n    `Route path \"${path}\" will be treated as if it were ` +\n      `\"${path.replace(/\\*$/, \"/*\")}\" because the \\`*\\` character must ` +\n      `always follow a \\`/\\` in the pattern. To get rid of this warning, ` +\n      `please change the route path to \"${path.replace(/\\*$/, \"/*\")}\".`\n  );\n\n  let params: CompiledPathParam[] = [];\n  let regexpSource =\n    \"^\" +\n    path\n      .replace(/\\/*\\*?$/, \"\") // Ignore trailing / and /*, we'll handle it below\n      .replace(/^\\/*/, \"/\") // Make sure it has a leading /\n      .replace(/[\\\\.*+^${}|()[\\]]/g, \"\\\\$&\") // Escape special regex chars\n      .replace(\n        /\\/:([\\w-]+)(\\?)?/g,\n        (_: string, paramName: string, isOptional) => {\n          params.push({ paramName, isOptional: isOptional != null });\n          return isOptional ? \"/?([^\\\\/]+)?\" : \"/([^\\\\/]+)\";\n        }\n      );\n\n  if (path.endsWith(\"*\")) {\n    params.push({ paramName: \"*\" });\n    regexpSource +=\n      path === \"*\" || path === \"/*\"\n        ? \"(.*)$\" // Already matched the initial /, just match the rest\n        : \"(?:\\\\/(.+)|\\\\/*)$\"; // Don't include the / in params[\"*\"]\n  } else if (end) {\n    // When matching to the end, ignore trailing slashes\n    regexpSource += \"\\\\/*$\";\n  } else if (path !== \"\" && path !== \"/\") {\n    // If our path is non-empty and contains anything beyond an initial slash,\n    // then we have _some_ form of path in our regex, so we should expect to\n    // match only if we find the end of this path segment.  Look for an optional\n    // non-captured trailing slash (to match a portion of the URL) or the end\n    // of the path (if we've matched to the end).  We used to do this with a\n    // word boundary but that gives false positives on routes like\n    // /user-preferences since `-` counts as a word boundary.\n    regexpSource += \"(?:(?=\\\\/|$))\";\n  } else {\n    // Nothing to match for \"\" or \"/\"\n  }\n\n  let matcher = new RegExp(regexpSource, caseSensitive ? undefined : \"i\");\n\n  return [matcher, params];\n}\n\nexport function decodePath(value: string) {\n  try {\n    return value\n      .split(\"/\")\n      .map((v) => decodeURIComponent(v).replace(/\\//g, \"%2F\"))\n      .join(\"/\");\n  } catch (error) {\n    warning(\n      false,\n      `The URL path \"${value}\" could not be decoded because it is is a ` +\n        `malformed URL segment. This is probably due to a bad percent ` +\n        `encoding (${error}).`\n    );\n\n    return value;\n  }\n}\n\n/**\n * @private\n */\nexport function stripBasename(\n  pathname: string,\n  basename: string\n): string | null {\n  if (basename === \"/\") return pathname;\n\n  if (!pathname.toLowerCase().startsWith(basename.toLowerCase())) {\n    return null;\n  }\n\n  // We want to leave trailing slash behavior in the user's control, so if they\n  // specify a basename with a trailing slash, we should support it\n  let startIndex = basename.endsWith(\"/\")\n    ? basename.length - 1\n    : basename.length;\n  let nextChar = pathname.charAt(startIndex);\n  if (nextChar && nextChar !== \"/\") {\n    // pathname does not start with basename/\n    return null;\n  }\n\n  return pathname.slice(startIndex) || \"/\";\n}\n\n/**\n * Returns a resolved path object relative to the given pathname.\n *\n * @see https://reactrouter.com/v6/utils/resolve-path\n */\nexport function resolvePath(to: To, fromPathname = \"/\"): Path {\n  let {\n    pathname: toPathname,\n    search = \"\",\n    hash = \"\",\n  } = typeof to === \"string\" ? parsePath(to) : to;\n\n  let pathname = toPathname\n    ? toPathname.startsWith(\"/\")\n      ? toPathname\n      : resolvePathname(toPathname, fromPathname)\n    : fromPathname;\n\n  return {\n    pathname,\n    search: normalizeSearch(search),\n    hash: normalizeHash(hash),\n  };\n}\n\nfunction resolvePathname(relativePath: string, fromPathname: string): string {\n  let segments = fromPathname.replace(/\\/+$/, \"\").split(\"/\");\n  let relativeSegments = relativePath.split(\"/\");\n\n  relativeSegments.forEach((segment) => {\n    if (segment === \"..\") {\n      // Keep the root \"\" segment so the pathname starts at /\n      if (segments.length > 1) segments.pop();\n    } else if (segment !== \".\") {\n      segments.push(segment);\n    }\n  });\n\n  return segments.length > 1 ? segments.join(\"/\") : \"/\";\n}\n\nfunction getInvalidPathError(\n  char: string,\n  field: string,\n  dest: string,\n  path: Partial<Path>\n) {\n  return (\n    `Cannot include a '${char}' character in a manually specified ` +\n    `\\`to.${field}\\` field [${JSON.stringify(\n      path\n    )}].  Please separate it out to the ` +\n    `\\`to.${dest}\\` field. Alternatively you may provide the full path as ` +\n    `a string in <Link to=\"...\"> and the router will parse it for you.`\n  );\n}\n\n/**\n * @private\n *\n * When processing relative navigation we want to ignore ancestor routes that\n * do not contribute to the path, such that index/pathless layout routes don't\n * interfere.\n *\n * For example, when moving a route element into an index route and/or a\n * pathless layout route, relative link behavior contained within should stay\n * the same.  Both of the following examples should link back to the root:\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\" element={<Link to=\"..\"}>\n *   </Route>\n *\n *   <Route path=\"/\">\n *     <Route path=\"accounts\">\n *       <Route element={<AccountsLayout />}>       // <-- Does not contribute\n *         <Route index element={<Link to=\"..\"} />  // <-- Does not contribute\n *       </Route\n *     </Route>\n *   </Route>\n */\nexport function getPathContributingMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[]) {\n  return matches.filter(\n    (match, index) =>\n      index === 0 || (match.route.path && match.route.path.length > 0)\n  );\n}\n\n// Return the array of pathnames for the current route matches - used to\n// generate the routePathnames input for resolveTo()\nexport function getResolveToMatches<\n  T extends AgnosticRouteMatch = AgnosticRouteMatch\n>(matches: T[], v7_relativeSplatPath: boolean) {\n  let pathMatches = getPathContributingMatches(matches);\n\n  // When v7_relativeSplatPath is enabled, use the full pathname for the leaf\n  // match so we include splat values for \".\" links.  See:\n  // https://github.com/remix-run/react-router/issues/11052#issuecomment-**********\n  if (v7_relativeSplatPath) {\n    return pathMatches.map((match, idx) =>\n      idx === pathMatches.length - 1 ? match.pathname : match.pathnameBase\n    );\n  }\n\n  return pathMatches.map((match) => match.pathnameBase);\n}\n\n/**\n * @private\n */\nexport function resolveTo(\n  toArg: To,\n  routePathnames: string[],\n  locationPathname: string,\n  isPathRelative = false\n): Path {\n  let to: Partial<Path>;\n  if (typeof toArg === \"string\") {\n    to = parsePath(toArg);\n  } else {\n    to = { ...toArg };\n\n    invariant(\n      !to.pathname || !to.pathname.includes(\"?\"),\n      getInvalidPathError(\"?\", \"pathname\", \"search\", to)\n    );\n    invariant(\n      !to.pathname || !to.pathname.includes(\"#\"),\n      getInvalidPathError(\"#\", \"pathname\", \"hash\", to)\n    );\n    invariant(\n      !to.search || !to.search.includes(\"#\"),\n      getInvalidPathError(\"#\", \"search\", \"hash\", to)\n    );\n  }\n\n  let isEmptyPath = toArg === \"\" || to.pathname === \"\";\n  let toPathname = isEmptyPath ? \"/\" : to.pathname;\n\n  let from: string;\n\n  // Routing is relative to the current pathname if explicitly requested.\n  //\n  // If a pathname is explicitly provided in `to`, it should be relative to the\n  // route context. This is explained in `Note on `<Link to>` values` in our\n  // migration guide from v5 as a means of disambiguation between `to` values\n  // that begin with `/` and those that do not. However, this is problematic for\n  // `to` values that do not provide a pathname. `to` can simply be a search or\n  // hash string, in which case we should assume that the navigation is relative\n  // to the current location's pathname and *not* the route pathname.\n  if (toPathname == null) {\n    from = locationPathname;\n  } else {\n    let routePathnameIndex = routePathnames.length - 1;\n\n    // With relative=\"route\" (the default), each leading .. segment means\n    // \"go up one route\" instead of \"go up one URL segment\".  This is a key\n    // difference from how <a href> works and a major reason we call this a\n    // \"to\" value instead of a \"href\".\n    if (!isPathRelative && toPathname.startsWith(\"..\")) {\n      let toSegments = toPathname.split(\"/\");\n\n      while (toSegments[0] === \"..\") {\n        toSegments.shift();\n        routePathnameIndex -= 1;\n      }\n\n      to.pathname = toSegments.join(\"/\");\n    }\n\n    from = routePathnameIndex >= 0 ? routePathnames[routePathnameIndex] : \"/\";\n  }\n\n  let path = resolvePath(to, from);\n\n  // Ensure the pathname has a trailing slash if the original \"to\" had one\n  let hasExplicitTrailingSlash =\n    toPathname && toPathname !== \"/\" && toPathname.endsWith(\"/\");\n  // Or if this was a link to the current path which has a trailing slash\n  let hasCurrentTrailingSlash =\n    (isEmptyPath || toPathname === \".\") && locationPathname.endsWith(\"/\");\n  if (\n    !path.pathname.endsWith(\"/\") &&\n    (hasExplicitTrailingSlash || hasCurrentTrailingSlash)\n  ) {\n    path.pathname += \"/\";\n  }\n\n  return path;\n}\n\n/**\n * @private\n */\nexport function getToPathname(to: To): string | undefined {\n  // Empty strings should be treated the same as / paths\n  return to === \"\" || (to as Path).pathname === \"\"\n    ? \"/\"\n    : typeof to === \"string\"\n    ? parsePath(to).pathname\n    : to.pathname;\n}\n\n/**\n * @private\n */\nexport const joinPaths = (paths: string[]): string =>\n  paths.join(\"/\").replace(/\\/\\/+/g, \"/\");\n\n/**\n * @private\n */\nexport const normalizePathname = (pathname: string): string =>\n  pathname.replace(/\\/+$/, \"\").replace(/^\\/*/, \"/\");\n\n/**\n * @private\n */\nexport const normalizeSearch = (search: string): string =>\n  !search || search === \"?\"\n    ? \"\"\n    : search.startsWith(\"?\")\n    ? search\n    : \"?\" + search;\n\n/**\n * @private\n */\nexport const normalizeHash = (hash: string): string =>\n  !hash || hash === \"#\" ? \"\" : hash.startsWith(\"#\") ? hash : \"#\" + hash;\n\nexport type JsonFunction = <Data>(\n  data: Data,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * This is a shortcut for creating `application/json` responses. Converts `data`\n * to JSON and sets the `Content-Type` header.\n *\n * @deprecated The `json` method is deprecated in favor of returning raw objects.\n * This method will be removed in v7.\n */\nexport const json: JsonFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  let headers = new Headers(responseInit.headers);\n  if (!headers.has(\"Content-Type\")) {\n    headers.set(\"Content-Type\", \"application/json; charset=utf-8\");\n  }\n\n  return new Response(JSON.stringify(data), {\n    ...responseInit,\n    headers,\n  });\n};\n\nexport class DataWithResponseInit<D> {\n  type: string = \"DataWithResponseInit\";\n  data: D;\n  init: ResponseInit | null;\n\n  constructor(data: D, init?: ResponseInit) {\n    this.data = data;\n    this.init = init || null;\n  }\n}\n\n/**\n * Create \"responses\" that contain `status`/`headers` without forcing\n * serialization into an actual `Response` - used by Remix single fetch\n */\nexport function data<D>(data: D, init?: number | ResponseInit) {\n  return new DataWithResponseInit(\n    data,\n    typeof init === \"number\" ? { status: init } : init\n  );\n}\n\nexport interface TrackedPromise extends Promise<any> {\n  _tracked?: boolean;\n  _data?: any;\n  _error?: any;\n}\n\nexport class AbortedDeferredError extends Error {}\n\nexport class DeferredData {\n  private pendingKeysSet: Set<string> = new Set<string>();\n  private controller: AbortController;\n  private abortPromise: Promise<void>;\n  private unlistenAbortSignal: () => void;\n  private subscribers: Set<(aborted: boolean, settledKey?: string) => void> =\n    new Set();\n  data: Record<string, unknown>;\n  init?: ResponseInit;\n  deferredKeys: string[] = [];\n\n  constructor(data: Record<string, unknown>, responseInit?: ResponseInit) {\n    invariant(\n      data && typeof data === \"object\" && !Array.isArray(data),\n      \"defer() only accepts plain objects\"\n    );\n\n    // Set up an AbortController + Promise we can race against to exit early\n    // cancellation\n    let reject: (e: AbortedDeferredError) => void;\n    this.abortPromise = new Promise((_, r) => (reject = r));\n    this.controller = new AbortController();\n    let onAbort = () =>\n      reject(new AbortedDeferredError(\"Deferred data aborted\"));\n    this.unlistenAbortSignal = () =>\n      this.controller.signal.removeEventListener(\"abort\", onAbort);\n    this.controller.signal.addEventListener(\"abort\", onAbort);\n\n    this.data = Object.entries(data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: this.trackPromise(key, value),\n        }),\n      {}\n    );\n\n    if (this.done) {\n      // All incoming values were resolved\n      this.unlistenAbortSignal();\n    }\n\n    this.init = responseInit;\n  }\n\n  private trackPromise(\n    key: string,\n    value: Promise<unknown> | unknown\n  ): TrackedPromise | unknown {\n    if (!(value instanceof Promise)) {\n      return value;\n    }\n\n    this.deferredKeys.push(key);\n    this.pendingKeysSet.add(key);\n\n    // We store a little wrapper promise that will be extended with\n    // _data/_error props upon resolve/reject\n    let promise: TrackedPromise = Promise.race([value, this.abortPromise]).then(\n      (data) => this.onSettle(promise, key, undefined, data as unknown),\n      (error) => this.onSettle(promise, key, error as unknown)\n    );\n\n    // Register rejection listeners to avoid uncaught promise rejections on\n    // errors or aborted deferred values\n    promise.catch(() => {});\n\n    Object.defineProperty(promise, \"_tracked\", { get: () => true });\n    return promise;\n  }\n\n  private onSettle(\n    promise: TrackedPromise,\n    key: string,\n    error: unknown,\n    data?: unknown\n  ): unknown {\n    if (\n      this.controller.signal.aborted &&\n      error instanceof AbortedDeferredError\n    ) {\n      this.unlistenAbortSignal();\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      return Promise.reject(error);\n    }\n\n    this.pendingKeysSet.delete(key);\n\n    if (this.done) {\n      // Nothing left to abort!\n      this.unlistenAbortSignal();\n    }\n\n    // If the promise was resolved/rejected with undefined, we'll throw an error as you\n    // should always resolve with a value or null\n    if (error === undefined && data === undefined) {\n      let undefinedError = new Error(\n        `Deferred data for key \"${key}\" resolved/rejected with \\`undefined\\`, ` +\n          `you must resolve/reject with a value or \\`null\\`.`\n      );\n      Object.defineProperty(promise, \"_error\", { get: () => undefinedError });\n      this.emit(false, key);\n      return Promise.reject(undefinedError);\n    }\n\n    if (data === undefined) {\n      Object.defineProperty(promise, \"_error\", { get: () => error });\n      this.emit(false, key);\n      return Promise.reject(error);\n    }\n\n    Object.defineProperty(promise, \"_data\", { get: () => data });\n    this.emit(false, key);\n    return data;\n  }\n\n  private emit(aborted: boolean, settledKey?: string) {\n    this.subscribers.forEach((subscriber) => subscriber(aborted, settledKey));\n  }\n\n  subscribe(fn: (aborted: boolean, settledKey?: string) => void) {\n    this.subscribers.add(fn);\n    return () => this.subscribers.delete(fn);\n  }\n\n  cancel() {\n    this.controller.abort();\n    this.pendingKeysSet.forEach((v, k) => this.pendingKeysSet.delete(k));\n    this.emit(true);\n  }\n\n  async resolveData(signal: AbortSignal) {\n    let aborted = false;\n    if (!this.done) {\n      let onAbort = () => this.cancel();\n      signal.addEventListener(\"abort\", onAbort);\n      aborted = await new Promise((resolve) => {\n        this.subscribe((aborted) => {\n          signal.removeEventListener(\"abort\", onAbort);\n          if (aborted || this.done) {\n            resolve(aborted);\n          }\n        });\n      });\n    }\n    return aborted;\n  }\n\n  get done() {\n    return this.pendingKeysSet.size === 0;\n  }\n\n  get unwrappedData() {\n    invariant(\n      this.data !== null && this.done,\n      \"Can only unwrap data on initialized and settled deferreds\"\n    );\n\n    return Object.entries(this.data).reduce(\n      (acc, [key, value]) =>\n        Object.assign(acc, {\n          [key]: unwrapTrackedPromise(value),\n        }),\n      {}\n    );\n  }\n\n  get pendingKeys() {\n    return Array.from(this.pendingKeysSet);\n  }\n}\n\nfunction isTrackedPromise(value: any): value is TrackedPromise {\n  return (\n    value instanceof Promise && (value as TrackedPromise)._tracked === true\n  );\n}\n\nfunction unwrapTrackedPromise(value: any) {\n  if (!isTrackedPromise(value)) {\n    return value;\n  }\n\n  if (value._error) {\n    throw value._error;\n  }\n  return value._data;\n}\n\nexport type DeferFunction = (\n  data: Record<string, unknown>,\n  init?: number | ResponseInit\n) => DeferredData;\n\n/**\n * @deprecated The `defer` method is deprecated in favor of returning raw\n * objects. This method will be removed in v7.\n */\nexport const defer: DeferFunction = (data, init = {}) => {\n  let responseInit = typeof init === \"number\" ? { status: init } : init;\n\n  return new DeferredData(data, responseInit);\n};\n\nexport type RedirectFunction = (\n  url: string,\n  init?: number | ResponseInit\n) => Response;\n\n/**\n * A redirect response. Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirect: RedirectFunction = (url, init = 302) => {\n  let responseInit = init;\n  if (typeof responseInit === \"number\") {\n    responseInit = { status: responseInit };\n  } else if (typeof responseInit.status === \"undefined\") {\n    responseInit.status = 302;\n  }\n\n  let headers = new Headers(responseInit.headers);\n  headers.set(\"Location\", url);\n\n  return new Response(null, {\n    ...responseInit,\n    headers,\n  });\n};\n\n/**\n * A redirect response that will force a document reload to the new location.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const redirectDocument: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Reload-Document\", \"true\");\n  return response;\n};\n\n/**\n * A redirect response that will perform a `history.replaceState` instead of a\n * `history.pushState` for client-side navigation redirects.\n * Sets the status code and the `Location` header.\n * Defaults to \"302 Found\".\n */\nexport const replace: RedirectFunction = (url, init) => {\n  let response = redirect(url, init);\n  response.headers.set(\"X-Remix-Replace\", \"true\");\n  return response;\n};\n\nexport type ErrorResponse = {\n  status: number;\n  statusText: string;\n  data: any;\n};\n\n/**\n * @private\n * Utility class we use to hold auto-unwrapped 4xx/5xx Response bodies\n *\n * We don't export the class for public use since it's an implementation\n * detail, but we export the interface above so folks can build their own\n * abstractions around instances via isRouteErrorResponse()\n */\nexport class ErrorResponseImpl implements ErrorResponse {\n  status: number;\n  statusText: string;\n  data: any;\n  private error?: Error;\n  private internal: boolean;\n\n  constructor(\n    status: number,\n    statusText: string | undefined,\n    data: any,\n    internal = false\n  ) {\n    this.status = status;\n    this.statusText = statusText || \"\";\n    this.internal = internal;\n    if (data instanceof Error) {\n      this.data = data.toString();\n      this.error = data;\n    } else {\n      this.data = data;\n    }\n  }\n}\n\n/**\n * Check if the given error is an ErrorResponse generated from a 4xx/5xx\n * Response thrown from an action/loader\n */\nexport function isRouteErrorResponse(error: any): error is ErrorResponse {\n  return (\n    error != null &&\n    typeof error.status === \"number\" &&\n    typeof error.statusText === \"string\" &&\n    typeof error.internal === \"boolean\" &&\n    \"data\" in error\n  );\n}\n", "import type { History, Location, Path, To } from \"./history\";\nimport {\n  Action as HistoryAction,\n  createLocation,\n  createPath,\n  invariant,\n  parsePath,\n  warning,\n} from \"./history\";\nimport type {\n  AgnosticDataRouteMatch,\n  AgnosticDataRouteObject,\n  DataStrategyMatch,\n  AgnosticRouteObject,\n  DataResult,\n  DataStrategyFunction,\n  DataStrategyFunctionArgs,\n  DeferredData,\n  DeferredResult,\n  DetectErrorBoundaryFunction,\n  ErrorResult,\n  FormEncType,\n  FormMethod,\n  HTMLFormMethod,\n  DataStrategyResult,\n  ImmutableRouteKey,\n  MapRoutePropertiesFunction,\n  MutationFormMethod,\n  RedirectResult,\n  RouteData,\n  RouteManifest,\n  ShouldRevalidateFunctionArgs,\n  Submission,\n  SuccessResult,\n  UIMatch,\n  V7_FormMethod,\n  V7_MutationFormMethod,\n  AgnosticPatchRoutesOnNavigationFunction,\n  DataWithResponseInit,\n} from \"./utils\";\nimport {\n  ErrorResponseImpl,\n  ResultType,\n  convertRouteMatchToUiMatch,\n  convertRoutesToDataRoutes,\n  getPathContributingMatches,\n  getResolveToMatches,\n  immutableRouteKeys,\n  isRouteErrorResponse,\n  joinPaths,\n  matchRoutes,\n  matchRoutesImpl,\n  resolveTo,\n  stripBasename,\n} from \"./utils\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Types and Constants\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * A Router instance manages all navigation and data loading/mutations\n */\nexport interface Router {\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the basename for the router\n   */\n  get basename(): RouterInit[\"basename\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the future config for the router\n   */\n  get future(): FutureConfig;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the current state of the router\n   */\n  get state(): RouterState;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the routes for this router instance\n   */\n  get routes(): AgnosticDataRouteObject[];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Return the window associated with the router\n   */\n  get window(): RouterInit[\"window\"];\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Initialize the router, including adding history listeners and kicking off\n   * initial data fetches.  Returns a function to cleanup listeners and abort\n   * any in-progress loads\n   */\n  initialize(): Router;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Subscribe to router.state updates\n   *\n   * @param fn function to call with the new state\n   */\n  subscribe(fn: RouterSubscriber): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Enable scroll restoration behavior in the router\n   *\n   * @param savedScrollPositions Object that will manage positions, in case\n   *                             it's being restored from sessionStorage\n   * @param getScrollPosition    Function to get the active Y scroll position\n   * @param getKey               Function to get the key to use for restoration\n   */\n  enableScrollRestoration(\n    savedScrollPositions: Record<string, number>,\n    getScrollPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ): () => void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Navigate forward/backward in the history stack\n   * @param to Delta to move in the history stack\n   */\n  navigate(to: number): Promise<void>;\n\n  /**\n   * Navigate to the given path\n   * @param to Path to navigate to\n   * @param opts Navigation options (method, submission, etc.)\n   */\n  navigate(to: To | null, opts?: RouterNavigateOptions): Promise<void>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a fetcher load/submission\n   *\n   * @param key     Fetcher key\n   * @param routeId Route that owns the fetcher\n   * @param href    href to fetch\n   * @param opts    Fetcher options, (method, submission, etc.)\n   */\n  fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Trigger a revalidation of all current route loaders and fetcher loads\n   */\n  revalidate(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to create an href for the given location\n   * @param location\n   */\n  createHref(location: Location | URL): string;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Utility function to URL encode a destination path according to the internal\n   * history implementation\n   * @param to\n   */\n  encodeLocation(to: To): Path;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get/create a fetcher for the given key\n   * @param key\n   */\n  getFetcher<TData = any>(key: string): Fetcher<TData>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete the fetcher for a given key\n   * @param key\n   */\n  deleteFetcher(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Cleanup listeners and abort any in-progress loads\n   */\n  dispose(): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Get a navigation blocker\n   * @param key The identifier for the blocker\n   * @param fn The blocker function implementation\n   */\n  getBlocker(key: string, fn: BlockerFunction): Blocker;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Delete a navigation blocker\n   * @param key The identifier for the blocker\n   */\n  deleteBlocker(key: string): void;\n\n  /**\n   * @internal\n   * PRIVATE DO NOT USE\n   *\n   * Patch additional children routes into an existing parent route\n   * @param routeId The parent route id or a callback function accepting `patch`\n   *                to perform batch patching\n   * @param children The additional children routes\n   */\n  patchRoutes(routeId: string | null, children: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * HMR needs to pass in-flight route updates to React Router\n   * TODO: Replace this with granular route update APIs (addRoute, updateRoute, deleteRoute)\n   */\n  _internalSetRoutes(routes: AgnosticRouteObject[]): void;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal fetch AbortControllers accessed by unit tests\n   */\n  _internalFetchControllers: Map<string, AbortController>;\n\n  /**\n   * @internal\n   * PRIVATE - DO NOT USE\n   *\n   * Internal pending DeferredData instances accessed by unit tests\n   */\n  _internalActiveDeferreds: Map<string, DeferredData>;\n}\n\n/**\n * State maintained internally by the router.  During a navigation, all states\n * reflect the the \"old\" location unless otherwise noted.\n */\nexport interface RouterState {\n  /**\n   * The action of the most recent navigation\n   */\n  historyAction: HistoryAction;\n\n  /**\n   * The current location reflected by the router\n   */\n  location: Location;\n\n  /**\n   * The current set of route matches\n   */\n  matches: AgnosticDataRouteMatch[];\n\n  /**\n   * Tracks whether we've completed our initial data load\n   */\n  initialized: boolean;\n\n  /**\n   * Current scroll position we should start at for a new view\n   *  - number -> scroll position to restore to\n   *  - false -> do not restore scroll at all (used during submissions)\n   *  - null -> don't have a saved position, scroll to hash or top of page\n   */\n  restoreScrollPosition: number | false | null;\n\n  /**\n   * Indicate whether this navigation should skip resetting the scroll position\n   * if we are unable to restore the scroll position\n   */\n  preventScrollReset: boolean;\n\n  /**\n   * Tracks the state of the current navigation\n   */\n  navigation: Navigation;\n\n  /**\n   * Tracks any in-progress revalidations\n   */\n  revalidation: RevalidationState;\n\n  /**\n   * Data from the loaders for the current matches\n   */\n  loaderData: RouteData;\n\n  /**\n   * Data from the action for the current matches\n   */\n  actionData: RouteData | null;\n\n  /**\n   * Errors caught from loaders for the current matches\n   */\n  errors: RouteData | null;\n\n  /**\n   * Map of current fetchers\n   */\n  fetchers: Map<string, Fetcher>;\n\n  /**\n   * Map of current blockers\n   */\n  blockers: Map<string, Blocker>;\n}\n\n/**\n * Data that can be passed into hydrate a Router from SSR\n */\nexport type HydrationState = Partial<\n  Pick<RouterState, \"loaderData\" | \"actionData\" | \"errors\">\n>;\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface FutureConfig {\n  v7_fetcherPersist: boolean;\n  v7_normalizeFormMethod: boolean;\n  v7_partialHydration: boolean;\n  v7_prependBasename: boolean;\n  v7_relativeSplatPath: boolean;\n  v7_skipActionErrorRevalidation: boolean;\n}\n\n/**\n * Initialization options for createRouter\n */\nexport interface RouterInit {\n  routes: AgnosticRouteObject[];\n  history: History;\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<FutureConfig>;\n  hydrationData?: HydrationState;\n  window?: Window;\n  dataStrategy?: DataStrategyFunction;\n  patchRoutesOnNavigation?: AgnosticPatchRoutesOnNavigationFunction;\n}\n\n/**\n * State returned from a server-side query() call\n */\nexport interface StaticHandlerContext {\n  basename: Router[\"basename\"];\n  location: RouterState[\"location\"];\n  matches: RouterState[\"matches\"];\n  loaderData: RouterState[\"loaderData\"];\n  actionData: RouterState[\"actionData\"];\n  errors: RouterState[\"errors\"];\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n  actionHeaders: Record<string, Headers>;\n  activeDeferreds: Record<string, DeferredData> | null;\n  _deepestRenderedBoundaryId?: string | null;\n}\n\n/**\n * A StaticHandler instance manages a singular SSR navigation/fetch event\n */\nexport interface StaticHandler {\n  dataRoutes: AgnosticDataRouteObject[];\n  query(\n    request: Request,\n    opts?: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<StaticHandlerContext | Response>;\n  queryRoute(\n    request: Request,\n    opts?: {\n      routeId?: string;\n      requestContext?: unknown;\n      dataStrategy?: DataStrategyFunction;\n    }\n  ): Promise<any>;\n}\n\ntype ViewTransitionOpts = {\n  currentLocation: Location;\n  nextLocation: Location;\n};\n\n/**\n * Subscriber function signature for changes to router state\n */\nexport interface RouterSubscriber {\n  (\n    state: RouterState,\n    opts: {\n      deletedFetchers: string[];\n      viewTransitionOpts?: ViewTransitionOpts;\n      flushSync: boolean;\n    }\n  ): void;\n}\n\n/**\n * Function signature for determining the key to be used in scroll restoration\n * for a given location\n */\nexport interface GetScrollRestorationKeyFunction {\n  (location: Location, matches: UIMatch[]): string | null;\n}\n\n/**\n * Function signature for determining the current scroll position\n */\nexport interface GetScrollPositionFunction {\n  (): number;\n}\n\nexport type RelativeRoutingType = \"route\" | \"path\";\n\n// Allowed for any navigation or fetch\ntype BaseNavigateOrFetchOptions = {\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  flushSync?: boolean;\n};\n\n// Only allowed for navigations\ntype BaseNavigateOptions = BaseNavigateOrFetchOptions & {\n  replace?: boolean;\n  state?: any;\n  fromRouteId?: string;\n  viewTransition?: boolean;\n};\n\n// Only allowed for submission navigations\ntype BaseSubmissionOptions = {\n  formMethod?: HTMLFormMethod;\n  formEncType?: FormEncType;\n} & (\n  | { formData: FormData; body?: undefined }\n  | { formData?: undefined; body: any }\n);\n\n/**\n * Options for a navigate() call for a normal (non-submission) navigation\n */\ntype LinkNavigateOptions = BaseNavigateOptions;\n\n/**\n * Options for a navigate() call for a submission navigation\n */\ntype SubmissionNavigateOptions = BaseNavigateOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to navigate() for a navigation\n */\nexport type RouterNavigateOptions =\n  | LinkNavigateOptions\n  | SubmissionNavigateOptions;\n\n/**\n * Options for a fetch() load\n */\ntype LoadFetchOptions = BaseNavigateOrFetchOptions;\n\n/**\n * Options for a fetch() submission\n */\ntype SubmitFetchOptions = BaseNavigateOrFetchOptions & BaseSubmissionOptions;\n\n/**\n * Options to pass to fetch()\n */\nexport type RouterFetchOptions = LoadFetchOptions | SubmitFetchOptions;\n\n/**\n * Potential states for state.navigation\n */\nexport type NavigationStates = {\n  Idle: {\n    state: \"idle\";\n    location: undefined;\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    formData: undefined;\n    json: undefined;\n    text: undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    location: Location;\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    text: Submission[\"text\"];\n  };\n};\n\nexport type Navigation = NavigationStates[keyof NavigationStates];\n\nexport type RevalidationState = \"idle\" | \"loading\";\n\n/**\n * Potential states for fetchers\n */\ntype FetcherStates<TData = any> = {\n  Idle: {\n    state: \"idle\";\n    formMethod: undefined;\n    formAction: undefined;\n    formEncType: undefined;\n    text: undefined;\n    formData: undefined;\n    json: undefined;\n    data: TData | undefined;\n  };\n  Loading: {\n    state: \"loading\";\n    formMethod: Submission[\"formMethod\"] | undefined;\n    formAction: Submission[\"formAction\"] | undefined;\n    formEncType: Submission[\"formEncType\"] | undefined;\n    text: Submission[\"text\"] | undefined;\n    formData: Submission[\"formData\"] | undefined;\n    json: Submission[\"json\"] | undefined;\n    data: TData | undefined;\n  };\n  Submitting: {\n    state: \"submitting\";\n    formMethod: Submission[\"formMethod\"];\n    formAction: Submission[\"formAction\"];\n    formEncType: Submission[\"formEncType\"];\n    text: Submission[\"text\"];\n    formData: Submission[\"formData\"];\n    json: Submission[\"json\"];\n    data: TData | undefined;\n  };\n};\n\nexport type Fetcher<TData = any> =\n  FetcherStates<TData>[keyof FetcherStates<TData>];\n\ninterface BlockerBlocked {\n  state: \"blocked\";\n  reset(): void;\n  proceed(): void;\n  location: Location;\n}\n\ninterface BlockerUnblocked {\n  state: \"unblocked\";\n  reset: undefined;\n  proceed: undefined;\n  location: undefined;\n}\n\ninterface BlockerProceeding {\n  state: \"proceeding\";\n  reset: undefined;\n  proceed: undefined;\n  location: Location;\n}\n\nexport type Blocker = BlockerUnblocked | BlockerBlocked | BlockerProceeding;\n\nexport type BlockerFunction = (args: {\n  currentLocation: Location;\n  nextLocation: Location;\n  historyAction: HistoryAction;\n}) => boolean;\n\ninterface ShortCircuitable {\n  /**\n   * startNavigation does not need to complete the navigation because we\n   * redirected or got interrupted\n   */\n  shortCircuited?: boolean;\n}\n\ntype PendingActionResult = [string, SuccessResult | ErrorResult];\n\ninterface HandleActionResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * Tuple for the returned or thrown value from the current action.  The routeId\n   * is the action route for success and the bubbled boundary route for errors.\n   */\n  pendingActionResult?: PendingActionResult;\n}\n\ninterface HandleLoadersResult extends ShortCircuitable {\n  /**\n   * Route matches which may have been updated from fog of war discovery\n   */\n  matches?: RouterState[\"matches\"];\n  /**\n   * loaderData returned from the current set of loaders\n   */\n  loaderData?: RouterState[\"loaderData\"];\n  /**\n   * errors thrown from the current set of loaders\n   */\n  errors?: RouterState[\"errors\"];\n}\n\n/**\n * Cached info for active fetcher.load() instances so they can participate\n * in revalidation\n */\ninterface FetchLoadMatch {\n  routeId: string;\n  path: string;\n}\n\n/**\n * Identified fetcher.load() calls that need to be revalidated\n */\ninterface RevalidatingFetcher extends FetchLoadMatch {\n  key: string;\n  match: AgnosticDataRouteMatch | null;\n  matches: AgnosticDataRouteMatch[] | null;\n  controller: AbortController | null;\n}\n\nconst validMutationMethodsArr: MutationFormMethod[] = [\n  \"post\",\n  \"put\",\n  \"patch\",\n  \"delete\",\n];\nconst validMutationMethods = new Set<MutationFormMethod>(\n  validMutationMethodsArr\n);\n\nconst validRequestMethodsArr: FormMethod[] = [\n  \"get\",\n  ...validMutationMethodsArr,\n];\nconst validRequestMethods = new Set<FormMethod>(validRequestMethodsArr);\n\nconst redirectStatusCodes = new Set([301, 302, 303, 307, 308]);\nconst redirectPreserveMethodStatusCodes = new Set([307, 308]);\n\nexport const IDLE_NAVIGATION: NavigationStates[\"Idle\"] = {\n  state: \"idle\",\n  location: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_FETCHER: FetcherStates[\"Idle\"] = {\n  state: \"idle\",\n  data: undefined,\n  formMethod: undefined,\n  formAction: undefined,\n  formEncType: undefined,\n  formData: undefined,\n  json: undefined,\n  text: undefined,\n};\n\nexport const IDLE_BLOCKER: BlockerUnblocked = {\n  state: \"unblocked\",\n  proceed: undefined,\n  reset: undefined,\n  location: undefined,\n};\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\nconst defaultMapRouteProperties: MapRoutePropertiesFunction = (route) => ({\n  hasErrorBoundary: Boolean(route.hasErrorBoundary),\n});\n\nconst TRANSITIONS_STORAGE_KEY = \"remix-router-transitions\";\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createRouter\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Create a router and listen to history POP navigations\n */\nexport function createRouter(init: RouterInit): Router {\n  const routerWindow = init.window\n    ? init.window\n    : typeof window !== \"undefined\"\n    ? window\n    : undefined;\n  const isBrowser =\n    typeof routerWindow !== \"undefined\" &&\n    typeof routerWindow.document !== \"undefined\" &&\n    typeof routerWindow.document.createElement !== \"undefined\";\n  const isServer = !isBrowser;\n\n  invariant(\n    init.routes.length > 0,\n    \"You must provide a non-empty routes array to createRouter\"\n  );\n\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (init.mapRouteProperties) {\n    mapRouteProperties = init.mapRouteProperties;\n  } else if (init.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = init.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n\n  // Routes keyed by ID\n  let manifest: RouteManifest = {};\n  // Routes in tree format for matching\n  let dataRoutes = convertRoutesToDataRoutes(\n    init.routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n  let inFlightDataRoutes: AgnosticDataRouteObject[] | undefined;\n  let basename = init.basename || \"/\";\n  let dataStrategyImpl = init.dataStrategy || defaultDataStrategy;\n  let patchRoutesOnNavigationImpl = init.patchRoutesOnNavigation;\n\n  // Config driven behavior flags\n  let future: FutureConfig = {\n    v7_fetcherPersist: false,\n    v7_normalizeFormMethod: false,\n    v7_partialHydration: false,\n    v7_prependBasename: false,\n    v7_relativeSplatPath: false,\n    v7_skipActionErrorRevalidation: false,\n    ...init.future,\n  };\n  // Cleanup function for history\n  let unlistenHistory: (() => void) | null = null;\n  // Externally-provided functions to call on all state changes\n  let subscribers = new Set<RouterSubscriber>();\n  // Externally-provided object to hold scroll restoration locations during routing\n  let savedScrollPositions: Record<string, number> | null = null;\n  // Externally-provided function to get scroll restoration keys\n  let getScrollRestorationKey: GetScrollRestorationKeyFunction | null = null;\n  // Externally-provided function to get current scroll position\n  let getScrollPosition: GetScrollPositionFunction | null = null;\n  // One-time flag to control the initial hydration scroll restoration.  Because\n  // we don't get the saved positions from <ScrollRestoration /> until _after_\n  // the initial render, we need to manually trigger a separate updateState to\n  // send along the restoreScrollPosition\n  // Set to true if we have `hydrationData` since we assume we were SSR'd and that\n  // SSR did the initial scroll restoration.\n  let initialScrollRestored = init.hydrationData != null;\n\n  let initialMatches = matchRoutes(dataRoutes, init.history.location, basename);\n  let initialMatchesIsFOW = false;\n  let initialErrors: RouteData | null = null;\n\n  if (initialMatches == null && !patchRoutesOnNavigationImpl) {\n    // If we do not match a user-provided-route, fall back to the root\n    // to allow the error boundary to take over\n    let error = getInternalRouterError(404, {\n      pathname: init.history.location.pathname,\n    });\n    let { matches, route } = getShortCircuitMatches(dataRoutes);\n    initialMatches = matches;\n    initialErrors = { [route.id]: error };\n  }\n\n  // In SPA apps, if the user provided a patchRoutesOnNavigation implementation and\n  // our initial match is a splat route, clear them out so we run through lazy\n  // discovery on hydration in case there's a more accurate lazy route match.\n  // In SSR apps (with `hydrationData`), we expect that the server will send\n  // up the proper matched routes so we don't want to run lazy discovery on\n  // initial hydration and want to hydrate into the splat route.\n  if (initialMatches && !init.hydrationData) {\n    let fogOfWar = checkFogOfWar(\n      initialMatches,\n      dataRoutes,\n      init.history.location.pathname\n    );\n    if (fogOfWar.active) {\n      initialMatches = null;\n    }\n  }\n\n  let initialized: boolean;\n  if (!initialMatches) {\n    initialized = false;\n    initialMatches = [];\n\n    // If partial hydration and fog of war is enabled, we will be running\n    // `patchRoutesOnNavigation` during hydration so include any partial matches as\n    // the initial matches so we can properly render `HydrateFallback`'s\n    if (future.v7_partialHydration) {\n      let fogOfWar = checkFogOfWar(\n        null,\n        dataRoutes,\n        init.history.location.pathname\n      );\n      if (fogOfWar.active && fogOfWar.matches) {\n        initialMatchesIsFOW = true;\n        initialMatches = fogOfWar.matches;\n      }\n    }\n  } else if (initialMatches.some((m) => m.route.lazy)) {\n    // All initialMatches need to be loaded before we're ready.  If we have lazy\n    // functions around still then we'll need to run them in initialize()\n    initialized = false;\n  } else if (!initialMatches.some((m) => m.route.loader)) {\n    // If we've got no loaders to run, then we're good to go\n    initialized = true;\n  } else if (future.v7_partialHydration) {\n    // If partial hydration is enabled, we're initialized so long as we were\n    // provided with hydrationData for every route with a loader, and no loaders\n    // were marked for explicit hydration\n    let loaderData = init.hydrationData ? init.hydrationData.loaderData : null;\n    let errors = init.hydrationData ? init.hydrationData.errors : null;\n    // If errors exist, don't consider routes below the boundary\n    if (errors) {\n      let idx = initialMatches.findIndex(\n        (m) => errors![m.route.id] !== undefined\n      );\n      initialized = initialMatches\n        .slice(0, idx + 1)\n        .every((m) => !shouldLoadRouteOnHydration(m.route, loaderData, errors));\n    } else {\n      initialized = initialMatches.every(\n        (m) => !shouldLoadRouteOnHydration(m.route, loaderData, errors)\n      );\n    }\n  } else {\n    // Without partial hydration - we're initialized if we were provided any\n    // hydrationData - which is expected to be complete\n    initialized = init.hydrationData != null;\n  }\n\n  let router: Router;\n  let state: RouterState = {\n    historyAction: init.history.action,\n    location: init.history.location,\n    matches: initialMatches,\n    initialized,\n    navigation: IDLE_NAVIGATION,\n    // Don't restore on initial updateState() if we were SSR'd\n    restoreScrollPosition: init.hydrationData != null ? false : null,\n    preventScrollReset: false,\n    revalidation: \"idle\",\n    loaderData: (init.hydrationData && init.hydrationData.loaderData) || {},\n    actionData: (init.hydrationData && init.hydrationData.actionData) || null,\n    errors: (init.hydrationData && init.hydrationData.errors) || initialErrors,\n    fetchers: new Map(),\n    blockers: new Map(),\n  };\n\n  // -- Stateful internal variables to manage navigations --\n  // Current navigation in progress (to be committed in completeNavigation)\n  let pendingAction: HistoryAction = HistoryAction.Pop;\n\n  // Should the current navigation prevent the scroll reset if scroll cannot\n  // be restored?\n  let pendingPreventScrollReset = false;\n\n  // AbortController for the active navigation\n  let pendingNavigationController: AbortController | null;\n\n  // Should the current navigation enable document.startViewTransition?\n  let pendingViewTransitionEnabled = false;\n\n  // Store applied view transitions so we can apply them on POP\n  let appliedViewTransitions: Map<string, Set<string>> = new Map<\n    string,\n    Set<string>\n  >();\n\n  // Cleanup function for persisting applied transitions to sessionStorage\n  let removePageHideEventListener: (() => void) | null = null;\n\n  // We use this to avoid touching history in completeNavigation if a\n  // revalidation is entirely uninterrupted\n  let isUninterruptedRevalidation = false;\n\n  // Use this internal flag to force revalidation of all loaders:\n  //  - submissions (completed or interrupted)\n  //  - useRevalidator()\n  //  - X-Remix-Revalidate (from redirect)\n  let isRevalidationRequired = false;\n\n  // Use this internal array to capture routes that require revalidation due\n  // to a cancelled deferred on action submission\n  let cancelledDeferredRoutes: string[] = [];\n\n  // Use this internal array to capture fetcher loads that were cancelled by an\n  // action navigation and require revalidation\n  let cancelledFetcherLoads: Set<string> = new Set();\n\n  // AbortControllers for any in-flight fetchers\n  let fetchControllers = new Map<string, AbortController>();\n\n  // Track loads based on the order in which they started\n  let incrementingLoadId = 0;\n\n  // Track the outstanding pending navigation data load to be compared against\n  // the globally incrementing load when a fetcher load lands after a completed\n  // navigation\n  let pendingNavigationLoadId = -1;\n\n  // Fetchers that triggered data reloads as a result of their actions\n  let fetchReloadIds = new Map<string, number>();\n\n  // Fetchers that triggered redirect navigations\n  let fetchRedirectIds = new Set<string>();\n\n  // Most recent href/match for fetcher.load calls for fetchers\n  let fetchLoadMatches = new Map<string, FetchLoadMatch>();\n\n  // Ref-count mounted fetchers so we know when it's ok to clean them up\n  let activeFetchers = new Map<string, number>();\n\n  // Fetchers that have requested a delete when using v7_fetcherPersist,\n  // they'll be officially removed after they return to idle\n  let deletedFetchers = new Set<string>();\n\n  // Store DeferredData instances for active route matches.  When a\n  // route loader returns defer() we stick one in here.  Then, when a nested\n  // promise resolves we update loaderData.  If a new navigation starts we\n  // cancel active deferreds for eliminated routes.\n  let activeDeferreds = new Map<string, DeferredData>();\n\n  // Store blocker functions in a separate Map outside of router state since\n  // we don't need to update UI state if they change\n  let blockerFunctions = new Map<string, BlockerFunction>();\n\n  // Map of pending patchRoutesOnNavigation() promises (keyed by path/matches) so\n  // that we only kick them off once for a given combo\n  let pendingPatchRoutes = new Map<\n    string,\n    ReturnType<AgnosticPatchRoutesOnNavigationFunction>\n  >();\n\n  // Flag to ignore the next history update, so we can revert the URL change on\n  // a POP navigation that was blocked by the user without touching router state\n  let unblockBlockerHistoryUpdate: (() => void) | undefined = undefined;\n\n  // Initialize the router, all side effects should be kicked off from here.\n  // Implemented as a Fluent API for ease of:\n  //   let router = createRouter(init).initialize();\n  function initialize() {\n    // If history informs us of a POP navigation, start the navigation but do not update\n    // state.  We'll update our own state once the navigation completes\n    unlistenHistory = init.history.listen(\n      ({ action: historyAction, location, delta }) => {\n        // Ignore this event if it was just us resetting the URL from a\n        // blocked POP navigation\n        if (unblockBlockerHistoryUpdate) {\n          unblockBlockerHistoryUpdate();\n          unblockBlockerHistoryUpdate = undefined;\n          return;\n        }\n\n        warning(\n          blockerFunctions.size === 0 || delta != null,\n          \"You are trying to use a blocker on a POP navigation to a location \" +\n            \"that was not created by @remix-run/router. This will fail silently in \" +\n            \"production. This can happen if you are navigating outside the router \" +\n            \"via `window.history.pushState`/`window.location.hash` instead of using \" +\n            \"router navigation APIs.  This can also happen if you are using \" +\n            \"createHashRouter and the user manually changes the URL.\"\n        );\n\n        let blockerKey = shouldBlockNavigation({\n          currentLocation: state.location,\n          nextLocation: location,\n          historyAction,\n        });\n\n        if (blockerKey && delta != null) {\n          // Restore the URL to match the current UI, but don't update router state\n          let nextHistoryUpdatePromise = new Promise<void>((resolve) => {\n            unblockBlockerHistoryUpdate = resolve;\n          });\n          init.history.go(delta * -1);\n\n          // Put the blocker into a blocked state\n          updateBlocker(blockerKey, {\n            state: \"blocked\",\n            location,\n            proceed() {\n              updateBlocker(blockerKey!, {\n                state: \"proceeding\",\n                proceed: undefined,\n                reset: undefined,\n                location,\n              });\n              // Re-do the same POP navigation we just blocked, after the url\n              // restoration is also complete.  See:\n              // https://github.com/remix-run/react-router/issues/11613\n              nextHistoryUpdatePromise.then(() => init.history.go(delta));\n            },\n            reset() {\n              let blockers = new Map(state.blockers);\n              blockers.set(blockerKey!, IDLE_BLOCKER);\n              updateState({ blockers });\n            },\n          });\n          return;\n        }\n\n        return startNavigation(historyAction, location);\n      }\n    );\n\n    if (isBrowser) {\n      // FIXME: This feels gross.  How can we cleanup the lines between\n      // scrollRestoration/appliedTransitions persistance?\n      restoreAppliedTransitions(routerWindow, appliedViewTransitions);\n      let _saveAppliedTransitions = () =>\n        persistAppliedTransitions(routerWindow, appliedViewTransitions);\n      routerWindow.addEventListener(\"pagehide\", _saveAppliedTransitions);\n      removePageHideEventListener = () =>\n        routerWindow.removeEventListener(\"pagehide\", _saveAppliedTransitions);\n    }\n\n    // Kick off initial data load if needed.  Use Pop to avoid modifying history\n    // Note we don't do any handling of lazy here.  For SPA's it'll get handled\n    // in the normal navigation flow.  For SSR it's expected that lazy modules are\n    // resolved prior to router creation since we can't go into a fallbackElement\n    // UI for SSR'd apps\n    if (!state.initialized) {\n      startNavigation(HistoryAction.Pop, state.location, {\n        initialHydration: true,\n      });\n    }\n\n    return router;\n  }\n\n  // Clean up a router and it's side effects\n  function dispose() {\n    if (unlistenHistory) {\n      unlistenHistory();\n    }\n    if (removePageHideEventListener) {\n      removePageHideEventListener();\n    }\n    subscribers.clear();\n    pendingNavigationController && pendingNavigationController.abort();\n    state.fetchers.forEach((_, key) => deleteFetcher(key));\n    state.blockers.forEach((_, key) => deleteBlocker(key));\n  }\n\n  // Subscribe to state updates for the router\n  function subscribe(fn: RouterSubscriber) {\n    subscribers.add(fn);\n    return () => subscribers.delete(fn);\n  }\n\n  // Update our state and notify the calling context of the change\n  function updateState(\n    newState: Partial<RouterState>,\n    opts: {\n      flushSync?: boolean;\n      viewTransitionOpts?: ViewTransitionOpts;\n    } = {}\n  ): void {\n    state = {\n      ...state,\n      ...newState,\n    };\n\n    // Prep fetcher cleanup so we can tell the UI which fetcher data entries\n    // can be removed\n    let completedFetchers: string[] = [];\n    let deletedFetchersKeys: string[] = [];\n\n    if (future.v7_fetcherPersist) {\n      state.fetchers.forEach((fetcher, key) => {\n        if (fetcher.state === \"idle\") {\n          if (deletedFetchers.has(key)) {\n            // Unmounted from the UI and can be totally removed\n            deletedFetchersKeys.push(key);\n          } else {\n            // Returned to idle but still mounted in the UI, so semi-remains for\n            // revalidations and such\n            completedFetchers.push(key);\n          }\n        }\n      });\n    }\n\n    // Remove any lingering deleted fetchers that have already been removed\n    // from state.fetchers\n    deletedFetchers.forEach((key) => {\n      if (!state.fetchers.has(key) && !fetchControllers.has(key)) {\n        deletedFetchersKeys.push(key);\n      }\n    });\n\n    // Iterate over a local copy so that if flushSync is used and we end up\n    // removing and adding a new subscriber due to the useCallback dependencies,\n    // we don't get ourselves into a loop calling the new subscriber immediately\n    [...subscribers].forEach((subscriber) =>\n      subscriber(state, {\n        deletedFetchers: deletedFetchersKeys,\n        viewTransitionOpts: opts.viewTransitionOpts,\n        flushSync: opts.flushSync === true,\n      })\n    );\n\n    // Remove idle fetchers from state since we only care about in-flight fetchers.\n    if (future.v7_fetcherPersist) {\n      completedFetchers.forEach((key) => state.fetchers.delete(key));\n      deletedFetchersKeys.forEach((key) => deleteFetcher(key));\n    } else {\n      // We already called deleteFetcher() on these, can remove them from this\n      // Set now that we've handed the keys off to the data layer\n      deletedFetchersKeys.forEach((key) => deletedFetchers.delete(key));\n    }\n  }\n\n  // Complete a navigation returning the state.navigation back to the IDLE_NAVIGATION\n  // and setting state.[historyAction/location/matches] to the new route.\n  // - Location is a required param\n  // - Navigation will always be set to IDLE_NAVIGATION\n  // - Can pass any other state in newState\n  function completeNavigation(\n    location: Location,\n    newState: Partial<Omit<RouterState, \"action\" | \"location\" | \"navigation\">>,\n    { flushSync }: { flushSync?: boolean } = {}\n  ): void {\n    // Deduce if we're in a loading/actionReload state:\n    // - We have committed actionData in the store\n    // - The current navigation was a mutation submission\n    // - We're past the submitting state and into the loading state\n    // - The location being loaded is not the result of a redirect\n    let isActionReload =\n      state.actionData != null &&\n      state.navigation.formMethod != null &&\n      isMutationMethod(state.navigation.formMethod) &&\n      state.navigation.state === \"loading\" &&\n      location.state?._isRedirect !== true;\n\n    let actionData: RouteData | null;\n    if (newState.actionData) {\n      if (Object.keys(newState.actionData).length > 0) {\n        actionData = newState.actionData;\n      } else {\n        // Empty actionData -> clear prior actionData due to an action error\n        actionData = null;\n      }\n    } else if (isActionReload) {\n      // Keep the current data if we're wrapping up the action reload\n      actionData = state.actionData;\n    } else {\n      // Clear actionData on any other completed navigations\n      actionData = null;\n    }\n\n    // Always preserve any existing loaderData from re-used routes\n    let loaderData = newState.loaderData\n      ? mergeLoaderData(\n          state.loaderData,\n          newState.loaderData,\n          newState.matches || [],\n          newState.errors\n        )\n      : state.loaderData;\n\n    // On a successful navigation we can assume we got through all blockers\n    // so we can start fresh\n    let blockers = state.blockers;\n    if (blockers.size > 0) {\n      blockers = new Map(blockers);\n      blockers.forEach((_, k) => blockers.set(k, IDLE_BLOCKER));\n    }\n\n    // Always respect the user flag.  Otherwise don't reset on mutation\n    // submission navigations unless they redirect\n    let preventScrollReset =\n      pendingPreventScrollReset === true ||\n      (state.navigation.formMethod != null &&\n        isMutationMethod(state.navigation.formMethod) &&\n        location.state?._isRedirect !== true);\n\n    // Commit any in-flight routes at the end of the HMR revalidation \"navigation\"\n    if (inFlightDataRoutes) {\n      dataRoutes = inFlightDataRoutes;\n      inFlightDataRoutes = undefined;\n    }\n\n    if (isUninterruptedRevalidation) {\n      // If this was an uninterrupted revalidation then do not touch history\n    } else if (pendingAction === HistoryAction.Pop) {\n      // Do nothing for POP - URL has already been updated\n    } else if (pendingAction === HistoryAction.Push) {\n      init.history.push(location, location.state);\n    } else if (pendingAction === HistoryAction.Replace) {\n      init.history.replace(location, location.state);\n    }\n\n    let viewTransitionOpts: ViewTransitionOpts | undefined;\n\n    // On POP, enable transitions if they were enabled on the original navigation\n    if (pendingAction === HistoryAction.Pop) {\n      // Forward takes precedence so they behave like the original navigation\n      let priorPaths = appliedViewTransitions.get(state.location.pathname);\n      if (priorPaths && priorPaths.has(location.pathname)) {\n        viewTransitionOpts = {\n          currentLocation: state.location,\n          nextLocation: location,\n        };\n      } else if (appliedViewTransitions.has(location.pathname)) {\n        // If we don't have a previous forward nav, assume we're popping back to\n        // the new location and enable if that location previously enabled\n        viewTransitionOpts = {\n          currentLocation: location,\n          nextLocation: state.location,\n        };\n      }\n    } else if (pendingViewTransitionEnabled) {\n      // Store the applied transition on PUSH/REPLACE\n      let toPaths = appliedViewTransitions.get(state.location.pathname);\n      if (toPaths) {\n        toPaths.add(location.pathname);\n      } else {\n        toPaths = new Set<string>([location.pathname]);\n        appliedViewTransitions.set(state.location.pathname, toPaths);\n      }\n      viewTransitionOpts = {\n        currentLocation: state.location,\n        nextLocation: location,\n      };\n    }\n\n    updateState(\n      {\n        ...newState, // matches, errors, fetchers go through as-is\n        actionData,\n        loaderData,\n        historyAction: pendingAction,\n        location,\n        initialized: true,\n        navigation: IDLE_NAVIGATION,\n        revalidation: \"idle\",\n        restoreScrollPosition: getSavedScrollPosition(\n          location,\n          newState.matches || state.matches\n        ),\n        preventScrollReset,\n        blockers,\n      },\n      {\n        viewTransitionOpts,\n        flushSync: flushSync === true,\n      }\n    );\n\n    // Reset stateful navigation vars\n    pendingAction = HistoryAction.Pop;\n    pendingPreventScrollReset = false;\n    pendingViewTransitionEnabled = false;\n    isUninterruptedRevalidation = false;\n    isRevalidationRequired = false;\n    cancelledDeferredRoutes = [];\n  }\n\n  // Trigger a navigation event, which can either be a numerical POP or a PUSH\n  // replace with an optional submission\n  async function navigate(\n    to: number | To | null,\n    opts?: RouterNavigateOptions\n  ): Promise<void> {\n    if (typeof to === \"number\") {\n      init.history.go(to);\n      return;\n    }\n\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      to,\n      future.v7_relativeSplatPath,\n      opts?.fromRouteId,\n      opts?.relative\n    );\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      false,\n      normalizedPath,\n      opts\n    );\n\n    let currentLocation = state.location;\n    let nextLocation = createLocation(state.location, path, opts && opts.state);\n\n    // When using navigate as a PUSH/REPLACE we aren't reading an already-encoded\n    // URL from window.location, so we need to encode it here so the behavior\n    // remains the same as POP and non-data-router usages.  new URL() does all\n    // the same encoding we'd get from a history.pushState/window.location read\n    // without having to touch history\n    nextLocation = {\n      ...nextLocation,\n      ...init.history.encodeLocation(nextLocation),\n    };\n\n    let userReplace = opts && opts.replace != null ? opts.replace : undefined;\n\n    let historyAction = HistoryAction.Push;\n\n    if (userReplace === true) {\n      historyAction = HistoryAction.Replace;\n    } else if (userReplace === false) {\n      // no-op\n    } else if (\n      submission != null &&\n      isMutationMethod(submission.formMethod) &&\n      submission.formAction === state.location.pathname + state.location.search\n    ) {\n      // By default on submissions to the current location we REPLACE so that\n      // users don't have to double-click the back button to get to the prior\n      // location.  If the user redirects to a different location from the\n      // action/loader this will be ignored and the redirect will be a PUSH\n      historyAction = HistoryAction.Replace;\n    }\n\n    let preventScrollReset =\n      opts && \"preventScrollReset\" in opts\n        ? opts.preventScrollReset === true\n        : undefined;\n\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let blockerKey = shouldBlockNavigation({\n      currentLocation,\n      nextLocation,\n      historyAction,\n    });\n\n    if (blockerKey) {\n      // Put the blocker into a blocked state\n      updateBlocker(blockerKey, {\n        state: \"blocked\",\n        location: nextLocation,\n        proceed() {\n          updateBlocker(blockerKey!, {\n            state: \"proceeding\",\n            proceed: undefined,\n            reset: undefined,\n            location: nextLocation,\n          });\n          // Send the same navigation through\n          navigate(to, opts);\n        },\n        reset() {\n          let blockers = new Map(state.blockers);\n          blockers.set(blockerKey!, IDLE_BLOCKER);\n          updateState({ blockers });\n        },\n      });\n      return;\n    }\n\n    return await startNavigation(historyAction, nextLocation, {\n      submission,\n      // Send through the formData serialization error if we have one so we can\n      // render at the right error boundary after we match routes\n      pendingError: error,\n      preventScrollReset,\n      replace: opts && opts.replace,\n      enableViewTransition: opts && opts.viewTransition,\n      flushSync,\n    });\n  }\n\n  // Revalidate all current loaders.  If a navigation is in progress or if this\n  // is interrupted by a navigation, allow this to \"succeed\" by calling all\n  // loaders during the next loader round\n  function revalidate() {\n    interruptActiveLoads();\n    updateState({ revalidation: \"loading\" });\n\n    // If we're currently submitting an action, we don't need to start a new\n    // navigation, we'll just let the follow up loader execution call all loaders\n    if (state.navigation.state === \"submitting\") {\n      return;\n    }\n\n    // If we're currently in an idle state, start a new navigation for the current\n    // action/location and mark it as uninterrupted, which will skip the history\n    // update in completeNavigation\n    if (state.navigation.state === \"idle\") {\n      startNavigation(state.historyAction, state.location, {\n        startUninterruptedRevalidation: true,\n      });\n      return;\n    }\n\n    // Otherwise, if we're currently in a loading state, just start a new\n    // navigation to the navigation.location but do not trigger an uninterrupted\n    // revalidation so that history correctly updates once the navigation completes\n    startNavigation(\n      pendingAction || state.historyAction,\n      state.navigation.location,\n      {\n        overrideNavigation: state.navigation,\n        // Proxy through any rending view transition\n        enableViewTransition: pendingViewTransitionEnabled === true,\n      }\n    );\n  }\n\n  // Start a navigation to the given action/location.  Can optionally provide a\n  // overrideNavigation which will override the normalLoad in the case of a redirect\n  // navigation\n  async function startNavigation(\n    historyAction: HistoryAction,\n    location: Location,\n    opts?: {\n      initialHydration?: boolean;\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      overrideNavigation?: Navigation;\n      pendingError?: ErrorResponseImpl;\n      startUninterruptedRevalidation?: boolean;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n      enableViewTransition?: boolean;\n      flushSync?: boolean;\n    }\n  ): Promise<void> {\n    // Abort any in-progress navigations and start a new one. Unset any ongoing\n    // uninterrupted revalidations unless told otherwise, since we want this\n    // new navigation to update history normally\n    pendingNavigationController && pendingNavigationController.abort();\n    pendingNavigationController = null;\n    pendingAction = historyAction;\n    isUninterruptedRevalidation =\n      (opts && opts.startUninterruptedRevalidation) === true;\n\n    // Save the current scroll position every time we start a new navigation,\n    // and track whether we should reset scroll on completion\n    saveScrollPosition(state.location, state.matches);\n    pendingPreventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    pendingViewTransitionEnabled = (opts && opts.enableViewTransition) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let loadingNavigation = opts && opts.overrideNavigation;\n    let matches =\n      opts?.initialHydration &&\n      state.matches &&\n      state.matches.length > 0 &&\n      !initialMatchesIsFOW\n        ? // `matchRoutes()` has already been called if we're in here via `router.initialize()`\n          state.matches\n        : matchRoutes(routesToUse, location, basename);\n    let flushSync = (opts && opts.flushSync) === true;\n\n    // Short circuit if it's only a hash change and not a revalidation or\n    // mutation submission.\n    //\n    // Ignore on initial page loads because since the initial hydration will always\n    // be \"same hash\".  For example, on /page#hash and submit a <Form method=\"post\">\n    // which will default to a navigation to /page\n    if (\n      matches &&\n      state.initialized &&\n      !isRevalidationRequired &&\n      isHashChangeOnly(state.location, location) &&\n      !(opts && opts.submission && isMutationMethod(opts.submission.formMethod))\n    ) {\n      completeNavigation(location, { matches }, { flushSync });\n      return;\n    }\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, location.pathname);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    // Short circuit with a 404 on the root error boundary if we match nothing\n    if (!matches) {\n      let { error, notFoundMatches, route } = handleNavigational404(\n        location.pathname\n      );\n      completeNavigation(\n        location,\n        {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        },\n        { flushSync }\n      );\n      return;\n    }\n\n    // Create a controller/Request for this navigation\n    pendingNavigationController = new AbortController();\n    let request = createClientSideRequest(\n      init.history,\n      location,\n      pendingNavigationController.signal,\n      opts && opts.submission\n    );\n    let pendingActionResult: PendingActionResult | undefined;\n\n    if (opts && opts.pendingError) {\n      // If we have a pendingError, it means the user attempted a GET submission\n      // with binary FormData so assign here and skip to handleLoaders.  That\n      // way we handle calling loaders above the boundary etc.  It's not really\n      // different from an actionError in that sense.\n      pendingActionResult = [\n        findNearestBoundary(matches).route.id,\n        { type: ResultType.error, error: opts.pendingError },\n      ];\n    } else if (\n      opts &&\n      opts.submission &&\n      isMutationMethod(opts.submission.formMethod)\n    ) {\n      // Call action if we received an action submission\n      let actionResult = await handleAction(\n        request,\n        location,\n        opts.submission,\n        matches,\n        fogOfWar.active,\n        { replace: opts.replace, flushSync }\n      );\n\n      if (actionResult.shortCircuited) {\n        return;\n      }\n\n      // If we received a 404 from handleAction, it's because we couldn't lazily\n      // discover the destination route so we don't want to call loaders\n      if (actionResult.pendingActionResult) {\n        let [routeId, result] = actionResult.pendingActionResult;\n        if (\n          isErrorResult(result) &&\n          isRouteErrorResponse(result.error) &&\n          result.error.status === 404\n        ) {\n          pendingNavigationController = null;\n\n          completeNavigation(location, {\n            matches: actionResult.matches,\n            loaderData: {},\n            errors: {\n              [routeId]: result.error,\n            },\n          });\n          return;\n        }\n      }\n\n      matches = actionResult.matches || matches;\n      pendingActionResult = actionResult.pendingActionResult;\n      loadingNavigation = getLoadingNavigation(location, opts.submission);\n      flushSync = false;\n      // No need to do fog of war matching again on loader execution\n      fogOfWar.active = false;\n\n      // Create a GET request for the loaders\n      request = createClientSideRequest(\n        init.history,\n        request.url,\n        request.signal\n      );\n    }\n\n    // Call loaders\n    let {\n      shortCircuited,\n      matches: updatedMatches,\n      loaderData,\n      errors,\n    } = await handleLoaders(\n      request,\n      location,\n      matches,\n      fogOfWar.active,\n      loadingNavigation,\n      opts && opts.submission,\n      opts && opts.fetcherSubmission,\n      opts && opts.replace,\n      opts && opts.initialHydration === true,\n      flushSync,\n      pendingActionResult\n    );\n\n    if (shortCircuited) {\n      return;\n    }\n\n    // Clean up now that the action/loaders have completed.  Don't clean up if\n    // we short circuited because pendingNavigationController will have already\n    // been assigned to a new controller for the next navigation\n    pendingNavigationController = null;\n\n    completeNavigation(location, {\n      matches: updatedMatches || matches,\n      ...getActionDataForCommit(pendingActionResult),\n      loaderData,\n      errors,\n    });\n  }\n\n  // Call the action matched by the leaf route for this navigation and handle\n  // redirects/errors\n  async function handleAction(\n    request: Request,\n    location: Location,\n    submission: Submission,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    opts: { replace?: boolean; flushSync?: boolean } = {}\n  ): Promise<HandleActionResult> {\n    interruptActiveLoads();\n\n    // Put us in a submitting state\n    let navigation = getSubmittingNavigation(location, submission);\n    updateState({ navigation }, { flushSync: opts.flushSync === true });\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches)\n          .route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          pendingActionResult: [\n            boundaryId,\n            {\n              type: ResultType.error,\n              error: discoverResult.error,\n            },\n          ],\n        };\n      } else if (!discoverResult.matches) {\n        let { notFoundMatches, error, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          pendingActionResult: [\n            route.id,\n            {\n              type: ResultType.error,\n              error,\n            },\n          ],\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    // Call our action and get the result\n    let result: DataResult;\n    let actionMatch = getTargetMatch(matches, location);\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      result = {\n        type: ResultType.error,\n        error: getInternalRouterError(405, {\n          method: request.method,\n          pathname: location.pathname,\n          routeId: actionMatch.route.id,\n        }),\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        state,\n        request,\n        [actionMatch],\n        matches,\n        null\n      );\n      result = results[actionMatch.route.id];\n\n      if (request.signal.aborted) {\n        return { shortCircuited: true };\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      let replace: boolean;\n      if (opts && opts.replace != null) {\n        replace = opts.replace;\n      } else {\n        // If the user didn't explicity indicate replace behavior, replace if\n        // we redirected to the exact same location we're currently at to avoid\n        // double back-buttons\n        let location = normalizeRedirectLocation(\n          result.response.headers.get(\"Location\")!,\n          new URL(request.url),\n          basename\n        );\n        replace = location === state.location.pathname + state.location.search;\n      }\n      await startRedirectNavigation(request, result, true, {\n        submission,\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    if (isDeferredResult(result)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = findNearestBoundary(matches, actionMatch.route.id);\n\n      // By default, all submissions to the current location are REPLACE\n      // navigations, but if the action threw an error that'll be rendered in\n      // an errorElement, we fall back to PUSH so that the user can use the\n      // back button to get back to the pre-submission form location to try\n      // again\n      if ((opts && opts.replace) !== true) {\n        pendingAction = HistoryAction.Push;\n      }\n\n      return {\n        matches,\n        pendingActionResult: [boundaryMatch.route.id, result],\n      };\n    }\n\n    return {\n      matches,\n      pendingActionResult: [actionMatch.route.id, result],\n    };\n  }\n\n  // Call all applicable loaders for the given matches, handling redirects,\n  // errors, etc.\n  async function handleLoaders(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    overrideNavigation?: Navigation,\n    submission?: Submission,\n    fetcherSubmission?: Submission,\n    replace?: boolean,\n    initialHydration?: boolean,\n    flushSync?: boolean,\n    pendingActionResult?: PendingActionResult\n  ): Promise<HandleLoadersResult> {\n    // Figure out the right navigation we want to use for data loading\n    let loadingNavigation =\n      overrideNavigation || getLoadingNavigation(location, submission);\n\n    // If this was a redirect from an action we don't have a \"submission\" but\n    // we have it on the loading navigation so use that if available\n    let activeSubmission =\n      submission ||\n      fetcherSubmission ||\n      getSubmissionFromNavigation(loadingNavigation);\n\n    // If this is an uninterrupted revalidation, we remain in our current idle\n    // state.  If not, we need to switch to our loading state and load data,\n    // preserving any new action data or existing action data (in the case of\n    // a revalidation interrupting an actionReload)\n    // If we have partialHydration enabled, then don't update the state for the\n    // initial data load since it's not a \"navigation\"\n    let shouldUpdateNavigationState =\n      !isUninterruptedRevalidation &&\n      (!future.v7_partialHydration || !initialHydration);\n\n    // When fog of war is enabled, we enter our `loading` state earlier so we\n    // can discover new routes during the `loading` state.  We skip this if\n    // we've already run actions since we would have done our matching already.\n    // If the children() function threw then, we want to proceed with the\n    // partial matches it discovered.\n    if (isFogOfWar) {\n      if (shouldUpdateNavigationState) {\n        let actionData = getUpdatedActionData(pendingActionResult);\n        updateState(\n          {\n            navigation: loadingNavigation,\n            ...(actionData !== undefined ? { actionData } : {}),\n          },\n          {\n            flushSync,\n          }\n        );\n      }\n\n      let discoverResult = await discoverRoutes(\n        matches,\n        location.pathname,\n        request.signal\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return { shortCircuited: true };\n      } else if (discoverResult.type === \"error\") {\n        let boundaryId = findNearestBoundary(discoverResult.partialMatches)\n          .route.id;\n        return {\n          matches: discoverResult.partialMatches,\n          loaderData: {},\n          errors: {\n            [boundaryId]: discoverResult.error,\n          },\n        };\n      } else if (!discoverResult.matches) {\n        let { error, notFoundMatches, route } = handleNavigational404(\n          location.pathname\n        );\n        return {\n          matches: notFoundMatches,\n          loaderData: {},\n          errors: {\n            [route.id]: error,\n          },\n        };\n      } else {\n        matches = discoverResult.matches;\n      }\n    }\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      activeSubmission,\n      location,\n      future.v7_partialHydration && initialHydration === true,\n      future.v7_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      pendingActionResult\n    );\n\n    // Cancel pending deferreds for no-longer-matched routes or routes we're\n    // about to reload.  Note that if this is an action reload we would have\n    // already cancelled all pending deferreds so this would be a no-op\n    cancelActiveDeferreds(\n      (routeId) =>\n        !(matches && matches.some((m) => m.route.id === routeId)) ||\n        (matchesToLoad && matchesToLoad.some((m) => m.route.id === routeId))\n    );\n\n    pendingNavigationLoadId = ++incrementingLoadId;\n\n    // Short circuit if we have no loaders to run\n    if (matchesToLoad.length === 0 && revalidatingFetchers.length === 0) {\n      let updatedFetchers = markFetchRedirectsDone();\n      completeNavigation(\n        location,\n        {\n          matches,\n          loaderData: {},\n          // Commit pending error if we're short circuiting\n          errors:\n            pendingActionResult && isErrorResult(pendingActionResult[1])\n              ? { [pendingActionResult[0]]: pendingActionResult[1].error }\n              : null,\n          ...getActionDataForCommit(pendingActionResult),\n          ...(updatedFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n        },\n        { flushSync }\n      );\n      return { shortCircuited: true };\n    }\n\n    if (shouldUpdateNavigationState) {\n      let updates: Partial<RouterState> = {};\n      if (!isFogOfWar) {\n        // Only update navigation/actionNData if we didn't already do it above\n        updates.navigation = loadingNavigation;\n        let actionData = getUpdatedActionData(pendingActionResult);\n        if (actionData !== undefined) {\n          updates.actionData = actionData;\n        }\n      }\n      if (revalidatingFetchers.length > 0) {\n        updates.fetchers = getUpdatedRevalidatingFetchers(revalidatingFetchers);\n      }\n      updateState(updates, { flushSync });\n    }\n\n    revalidatingFetchers.forEach((rf) => {\n      abortFetcher(rf.key);\n      if (rf.controller) {\n        // Fetchers use an independent AbortController so that aborting a fetcher\n        // (via deleteFetcher) does not abort the triggering navigation that\n        // triggered the revalidation\n        fetchControllers.set(rf.key, rf.controller);\n      }\n    });\n\n    // Proxy navigation abort through to revalidation fetchers\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((f) => abortFetcher(f.key));\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.addEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        request\n      );\n\n    if (request.signal.aborted) {\n      return { shortCircuited: true };\n    }\n\n    // Clean up _after_ loaders have completed.  Don't clean up if we short\n    // circuited because fetchControllers would have been aborted and\n    // reassigned to new controllers for the next navigation\n    if (pendingNavigationController) {\n      pendingNavigationController.signal.removeEventListener(\n        \"abort\",\n        abortPendingFetchRevalidations\n      );\n    }\n\n    revalidatingFetchers.forEach((rf) => fetchControllers.delete(rf.key));\n\n    // If any loaders returned a redirect Response, start a new REPLACE navigation\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      await startRedirectNavigation(request, redirect.result, true, {\n        replace,\n      });\n      return { shortCircuited: true };\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      loaderResults,\n      pendingActionResult,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Wire up subscribers to update loaderData as promises settle\n    activeDeferreds.forEach((deferredData, routeId) => {\n      deferredData.subscribe((aborted) => {\n        // Note: No need to updateState here since the TrackedPromise on\n        // loaderData is stable across resolve/reject\n        // Remove this instance if we were aborted or if promises have settled\n        if (aborted || deferredData.done) {\n          activeDeferreds.delete(routeId);\n        }\n      });\n    });\n\n    // Preserve SSR errors during partial hydration\n    if (future.v7_partialHydration && initialHydration && state.errors) {\n      errors = { ...state.errors, ...errors };\n    }\n\n    let updatedFetchers = markFetchRedirectsDone();\n    let didAbortFetchLoads = abortStaleFetchLoads(pendingNavigationLoadId);\n    let shouldUpdateFetchers =\n      updatedFetchers || didAbortFetchLoads || revalidatingFetchers.length > 0;\n\n    return {\n      matches,\n      loaderData,\n      errors,\n      ...(shouldUpdateFetchers ? { fetchers: new Map(state.fetchers) } : {}),\n    };\n  }\n\n  function getUpdatedActionData(\n    pendingActionResult: PendingActionResult | undefined\n  ): Record<string, RouteData> | null | undefined {\n    if (pendingActionResult && !isErrorResult(pendingActionResult[1])) {\n      // This is cast to `any` currently because `RouteData`uses any and it\n      // would be a breaking change to use any.\n      // TODO: v7 - change `RouteData` to use `unknown` instead of `any`\n      return {\n        [pendingActionResult[0]]: pendingActionResult[1].data as any,\n      };\n    } else if (state.actionData) {\n      if (Object.keys(state.actionData).length === 0) {\n        return null;\n      } else {\n        return state.actionData;\n      }\n    }\n  }\n\n  function getUpdatedRevalidatingFetchers(\n    revalidatingFetchers: RevalidatingFetcher[]\n  ) {\n    revalidatingFetchers.forEach((rf) => {\n      let fetcher = state.fetchers.get(rf.key);\n      let revalidatingFetcher = getLoadingFetcher(\n        undefined,\n        fetcher ? fetcher.data : undefined\n      );\n      state.fetchers.set(rf.key, revalidatingFetcher);\n    });\n    return new Map(state.fetchers);\n  }\n\n  // Trigger a fetcher load/submit for the given fetcher key\n  function fetch(\n    key: string,\n    routeId: string,\n    href: string | null,\n    opts?: RouterFetchOptions\n  ) {\n    if (isServer) {\n      throw new Error(\n        \"router.fetch() was called during the server render, but it shouldn't be. \" +\n          \"You are likely calling a useFetcher() method in the body of your component. \" +\n          \"Try moving it to a useEffect or a callback.\"\n      );\n    }\n\n    abortFetcher(key);\n\n    let flushSync = (opts && opts.flushSync) === true;\n\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let normalizedPath = normalizeTo(\n      state.location,\n      state.matches,\n      basename,\n      future.v7_prependBasename,\n      href,\n      future.v7_relativeSplatPath,\n      routeId,\n      opts?.relative\n    );\n    let matches = matchRoutes(routesToUse, normalizedPath, basename);\n\n    let fogOfWar = checkFogOfWar(matches, routesToUse, normalizedPath);\n    if (fogOfWar.active && fogOfWar.matches) {\n      matches = fogOfWar.matches;\n    }\n\n    if (!matches) {\n      setFetcherError(\n        key,\n        routeId,\n        getInternalRouterError(404, { pathname: normalizedPath }),\n        { flushSync }\n      );\n      return;\n    }\n\n    let { path, submission, error } = normalizeNavigateOptions(\n      future.v7_normalizeFormMethod,\n      true,\n      normalizedPath,\n      opts\n    );\n\n    if (error) {\n      setFetcherError(key, routeId, error, { flushSync });\n      return;\n    }\n\n    let match = getTargetMatch(matches, path);\n\n    let preventScrollReset = (opts && opts.preventScrollReset) === true;\n\n    if (submission && isMutationMethod(submission.formMethod)) {\n      handleFetcherAction(\n        key,\n        routeId,\n        path,\n        match,\n        matches,\n        fogOfWar.active,\n        flushSync,\n        preventScrollReset,\n        submission\n      );\n      return;\n    }\n\n    // Store off the match so we can call it's shouldRevalidate on subsequent\n    // revalidations\n    fetchLoadMatches.set(key, { routeId, path });\n    handleFetcherLoader(\n      key,\n      routeId,\n      path,\n      match,\n      matches,\n      fogOfWar.active,\n      flushSync,\n      preventScrollReset,\n      submission\n    );\n  }\n\n  // Call the action for the matched fetcher.submit(), and then handle redirects,\n  // errors, and revalidation\n  async function handleFetcherAction(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    requestMatches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    preventScrollReset: boolean,\n    submission: Submission\n  ) {\n    interruptActiveLoads();\n    fetchLoadMatches.delete(key);\n\n    function detectAndHandle405Error(m: AgnosticDataRouteMatch) {\n      if (!m.route.action && !m.route.lazy) {\n        let error = getInternalRouterError(405, {\n          method: submission.formMethod,\n          pathname: path,\n          routeId: routeId,\n        });\n        setFetcherError(key, routeId, error, { flushSync });\n        return true;\n      }\n      return false;\n    }\n\n    if (!isFogOfWar && detectAndHandle405Error(match)) {\n      return;\n    }\n\n    // Put this fetcher into it's submitting state\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(key, getSubmittingFetcher(submission, existingFetcher), {\n      flushSync,\n    });\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal,\n      submission\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        requestMatches,\n        new URL(fetchRequest.url).pathname,\n        fetchRequest.signal,\n        key\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        requestMatches = discoverResult.matches;\n        match = getTargetMatch(requestMatches, path);\n\n        if (detectAndHandle405Error(match)) {\n          return;\n        }\n      }\n    }\n\n    // Call the action for the fetcher\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let actionResults = await callDataStrategy(\n      \"action\",\n      state,\n      fetchRequest,\n      [match],\n      requestMatches,\n      key\n    );\n    let actionResult = actionResults[match.route.id];\n\n    if (fetchRequest.signal.aborted) {\n      // We can delete this so long as we weren't aborted by our own fetcher\n      // re-submit which would have put _new_ controller is in fetchControllers\n      if (fetchControllers.get(key) === abortController) {\n        fetchControllers.delete(key);\n      }\n      return;\n    }\n\n    // When using v7_fetcherPersist, we don't want errors bubbling up to the UI\n    // or redirects processed for unmounted fetchers so we just revert them to\n    // idle\n    if (future.v7_fetcherPersist && deletedFetchers.has(key)) {\n      if (isRedirectResult(actionResult) || isErrorResult(actionResult)) {\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      }\n      // Let SuccessResult's fall through for revalidation\n    } else {\n      if (isRedirectResult(actionResult)) {\n        fetchControllers.delete(key);\n        if (pendingNavigationLoadId > originatingLoadId) {\n          // A new navigation was kicked off after our action started, so that\n          // should take precedence over this redirect navigation.  We already\n          // set isRevalidationRequired so all loaders for the new route should\n          // fire unless opted out via shouldRevalidate\n          updateFetcherState(key, getDoneFetcher(undefined));\n          return;\n        } else {\n          fetchRedirectIds.add(key);\n          updateFetcherState(key, getLoadingFetcher(submission));\n          return startRedirectNavigation(fetchRequest, actionResult, false, {\n            fetcherSubmission: submission,\n            preventScrollReset,\n          });\n        }\n      }\n\n      // Process any non-redirect errors thrown\n      if (isErrorResult(actionResult)) {\n        setFetcherError(key, routeId, actionResult.error);\n        return;\n      }\n    }\n\n    if (isDeferredResult(actionResult)) {\n      throw getInternalRouterError(400, { type: \"defer-action\" });\n    }\n\n    // Start the data load for current matches, or the next location if we're\n    // in the middle of a navigation\n    let nextLocation = state.navigation.location || state.location;\n    let revalidationRequest = createClientSideRequest(\n      init.history,\n      nextLocation,\n      abortController.signal\n    );\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let matches =\n      state.navigation.state !== \"idle\"\n        ? matchRoutes(routesToUse, state.navigation.location, basename)\n        : state.matches;\n\n    invariant(matches, \"Didn't find any matches after fetcher action\");\n\n    let loadId = ++incrementingLoadId;\n    fetchReloadIds.set(key, loadId);\n\n    let loadFetcher = getLoadingFetcher(submission, actionResult.data);\n    state.fetchers.set(key, loadFetcher);\n\n    let [matchesToLoad, revalidatingFetchers] = getMatchesToLoad(\n      init.history,\n      state,\n      matches,\n      submission,\n      nextLocation,\n      false,\n      future.v7_skipActionErrorRevalidation,\n      isRevalidationRequired,\n      cancelledDeferredRoutes,\n      cancelledFetcherLoads,\n      deletedFetchers,\n      fetchLoadMatches,\n      fetchRedirectIds,\n      routesToUse,\n      basename,\n      [match.route.id, actionResult]\n    );\n\n    // Put all revalidating fetchers into the loading state, except for the\n    // current fetcher which we want to keep in it's current loading state which\n    // contains it's action submission info + action data\n    revalidatingFetchers\n      .filter((rf) => rf.key !== key)\n      .forEach((rf) => {\n        let staleKey = rf.key;\n        let existingFetcher = state.fetchers.get(staleKey);\n        let revalidatingFetcher = getLoadingFetcher(\n          undefined,\n          existingFetcher ? existingFetcher.data : undefined\n        );\n        state.fetchers.set(staleKey, revalidatingFetcher);\n        abortFetcher(staleKey);\n        if (rf.controller) {\n          fetchControllers.set(staleKey, rf.controller);\n        }\n      });\n\n    updateState({ fetchers: new Map(state.fetchers) });\n\n    let abortPendingFetchRevalidations = () =>\n      revalidatingFetchers.forEach((rf) => abortFetcher(rf.key));\n\n    abortController.signal.addEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    let { loaderResults, fetcherResults } =\n      await callLoadersAndMaybeResolveData(\n        state,\n        matches,\n        matchesToLoad,\n        revalidatingFetchers,\n        revalidationRequest\n      );\n\n    if (abortController.signal.aborted) {\n      return;\n    }\n\n    abortController.signal.removeEventListener(\n      \"abort\",\n      abortPendingFetchRevalidations\n    );\n\n    fetchReloadIds.delete(key);\n    fetchControllers.delete(key);\n    revalidatingFetchers.forEach((r) => fetchControllers.delete(r.key));\n\n    let redirect = findRedirect(loaderResults);\n    if (redirect) {\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect.result,\n        false,\n        { preventScrollReset }\n      );\n    }\n\n    redirect = findRedirect(fetcherResults);\n    if (redirect) {\n      // If this redirect came from a fetcher make sure we mark it in\n      // fetchRedirectIds so it doesn't get revalidated on the next set of\n      // loader executions\n      fetchRedirectIds.add(redirect.key);\n      return startRedirectNavigation(\n        revalidationRequest,\n        redirect.result,\n        false,\n        { preventScrollReset }\n      );\n    }\n\n    // Process and commit output from loaders\n    let { loaderData, errors } = processLoaderData(\n      state,\n      matches,\n      loaderResults,\n      undefined,\n      revalidatingFetchers,\n      fetcherResults,\n      activeDeferreds\n    );\n\n    // Since we let revalidations complete even if the submitting fetcher was\n    // deleted, only put it back to idle if it hasn't been deleted\n    if (state.fetchers.has(key)) {\n      let doneFetcher = getDoneFetcher(actionResult.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n\n    abortStaleFetchLoads(loadId);\n\n    // If we are currently in a navigation loading state and this fetcher is\n    // more recent than the navigation, we want the newer data so abort the\n    // navigation and complete it with the fetcher data\n    if (\n      state.navigation.state === \"loading\" &&\n      loadId > pendingNavigationLoadId\n    ) {\n      invariant(pendingAction, \"Expected pending action\");\n      pendingNavigationController && pendingNavigationController.abort();\n\n      completeNavigation(state.navigation.location, {\n        matches,\n        loaderData,\n        errors,\n        fetchers: new Map(state.fetchers),\n      });\n    } else {\n      // otherwise just update with the fetcher data, preserving any existing\n      // loaderData for loaders that did not need to reload.  We have to\n      // manually merge here since we aren't going through completeNavigation\n      updateState({\n        errors,\n        loaderData: mergeLoaderData(\n          state.loaderData,\n          loaderData,\n          matches,\n          errors\n        ),\n        fetchers: new Map(state.fetchers),\n      });\n      isRevalidationRequired = false;\n    }\n  }\n\n  // Call the matched loader for fetcher.load(), handling redirects, errors, etc.\n  async function handleFetcherLoader(\n    key: string,\n    routeId: string,\n    path: string,\n    match: AgnosticDataRouteMatch,\n    matches: AgnosticDataRouteMatch[],\n    isFogOfWar: boolean,\n    flushSync: boolean,\n    preventScrollReset: boolean,\n    submission?: Submission\n  ) {\n    let existingFetcher = state.fetchers.get(key);\n    updateFetcherState(\n      key,\n      getLoadingFetcher(\n        submission,\n        existingFetcher ? existingFetcher.data : undefined\n      ),\n      { flushSync }\n    );\n\n    let abortController = new AbortController();\n    let fetchRequest = createClientSideRequest(\n      init.history,\n      path,\n      abortController.signal\n    );\n\n    if (isFogOfWar) {\n      let discoverResult = await discoverRoutes(\n        matches,\n        new URL(fetchRequest.url).pathname,\n        fetchRequest.signal,\n        key\n      );\n\n      if (discoverResult.type === \"aborted\") {\n        return;\n      } else if (discoverResult.type === \"error\") {\n        setFetcherError(key, routeId, discoverResult.error, { flushSync });\n        return;\n      } else if (!discoverResult.matches) {\n        setFetcherError(\n          key,\n          routeId,\n          getInternalRouterError(404, { pathname: path }),\n          { flushSync }\n        );\n        return;\n      } else {\n        matches = discoverResult.matches;\n        match = getTargetMatch(matches, path);\n      }\n    }\n\n    // Call the loader for this fetcher route match\n    fetchControllers.set(key, abortController);\n\n    let originatingLoadId = incrementingLoadId;\n    let results = await callDataStrategy(\n      \"loader\",\n      state,\n      fetchRequest,\n      [match],\n      matches,\n      key\n    );\n    let result = results[match.route.id];\n\n    // Deferred isn't supported for fetcher loads, await everything and treat it\n    // as a normal load.  resolveDeferredData will return undefined if this\n    // fetcher gets aborted, so we just leave result untouched and short circuit\n    // below if that happens\n    if (isDeferredResult(result)) {\n      result =\n        (await resolveDeferredData(result, fetchRequest.signal, true)) ||\n        result;\n    }\n\n    // We can delete this so long as we weren't aborted by our our own fetcher\n    // re-load which would have put _new_ controller is in fetchControllers\n    if (fetchControllers.get(key) === abortController) {\n      fetchControllers.delete(key);\n    }\n\n    if (fetchRequest.signal.aborted) {\n      return;\n    }\n\n    // We don't want errors bubbling up or redirects followed for unmounted\n    // fetchers, so short circuit here if it was removed from the UI\n    if (deletedFetchers.has(key)) {\n      updateFetcherState(key, getDoneFetcher(undefined));\n      return;\n    }\n\n    // If the loader threw a redirect Response, start a new REPLACE navigation\n    if (isRedirectResult(result)) {\n      if (pendingNavigationLoadId > originatingLoadId) {\n        // A new navigation was kicked off after our loader started, so that\n        // should take precedence over this redirect navigation\n        updateFetcherState(key, getDoneFetcher(undefined));\n        return;\n      } else {\n        fetchRedirectIds.add(key);\n        await startRedirectNavigation(fetchRequest, result, false, {\n          preventScrollReset,\n        });\n        return;\n      }\n    }\n\n    // Process any non-redirect errors thrown\n    if (isErrorResult(result)) {\n      setFetcherError(key, routeId, result.error);\n      return;\n    }\n\n    invariant(!isDeferredResult(result), \"Unhandled fetcher deferred data\");\n\n    // Put the fetcher back into an idle state\n    updateFetcherState(key, getDoneFetcher(result.data));\n  }\n\n  /**\n   * Utility function to handle redirects returned from an action or loader.\n   * Normally, a redirect \"replaces\" the navigation that triggered it.  So, for\n   * example:\n   *\n   *  - user is on /a\n   *  - user clicks a link to /b\n   *  - loader for /b redirects to /c\n   *\n   * In a non-JS app the browser would track the in-flight navigation to /b and\n   * then replace it with /c when it encountered the redirect response.  In\n   * the end it would only ever update the URL bar with /c.\n   *\n   * In client-side routing using pushState/replaceState, we aim to emulate\n   * this behavior and we also do not update history until the end of the\n   * navigation (including processed redirects).  This means that we never\n   * actually touch history until we've processed redirects, so we just use\n   * the history action from the original navigation (PUSH or REPLACE).\n   */\n  async function startRedirectNavigation(\n    request: Request,\n    redirect: RedirectResult,\n    isNavigation: boolean,\n    {\n      submission,\n      fetcherSubmission,\n      preventScrollReset,\n      replace,\n    }: {\n      submission?: Submission;\n      fetcherSubmission?: Submission;\n      preventScrollReset?: boolean;\n      replace?: boolean;\n    } = {}\n  ) {\n    if (redirect.response.headers.has(\"X-Remix-Revalidate\")) {\n      isRevalidationRequired = true;\n    }\n\n    let location = redirect.response.headers.get(\"Location\");\n    invariant(location, \"Expected a Location header on the redirect Response\");\n    location = normalizeRedirectLocation(\n      location,\n      new URL(request.url),\n      basename\n    );\n    let redirectLocation = createLocation(state.location, location, {\n      _isRedirect: true,\n    });\n\n    if (isBrowser) {\n      let isDocumentReload = false;\n\n      if (redirect.response.headers.has(\"X-Remix-Reload-Document\")) {\n        // Hard reload if the response contained X-Remix-Reload-Document\n        isDocumentReload = true;\n      } else if (ABSOLUTE_URL_REGEX.test(location)) {\n        const url = init.history.createURL(location);\n        isDocumentReload =\n          // Hard reload if it's an absolute URL to a new origin\n          url.origin !== routerWindow.location.origin ||\n          // Hard reload if it's an absolute URL that does not match our basename\n          stripBasename(url.pathname, basename) == null;\n      }\n\n      if (isDocumentReload) {\n        if (replace) {\n          routerWindow.location.replace(location);\n        } else {\n          routerWindow.location.assign(location);\n        }\n        return;\n      }\n    }\n\n    // There's no need to abort on redirects, since we don't detect the\n    // redirect until the action/loaders have settled\n    pendingNavigationController = null;\n\n    let redirectHistoryAction =\n      replace === true || redirect.response.headers.has(\"X-Remix-Replace\")\n        ? HistoryAction.Replace\n        : HistoryAction.Push;\n\n    // Use the incoming submission if provided, fallback on the active one in\n    // state.navigation\n    let { formMethod, formAction, formEncType } = state.navigation;\n    if (\n      !submission &&\n      !fetcherSubmission &&\n      formMethod &&\n      formAction &&\n      formEncType\n    ) {\n      submission = getSubmissionFromNavigation(state.navigation);\n    }\n\n    // If this was a 307/308 submission we want to preserve the HTTP method and\n    // re-submit the GET/POST/PUT/PATCH/DELETE as a submission navigation to the\n    // redirected location\n    let activeSubmission = submission || fetcherSubmission;\n    if (\n      redirectPreserveMethodStatusCodes.has(redirect.response.status) &&\n      activeSubmission &&\n      isMutationMethod(activeSubmission.formMethod)\n    ) {\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        submission: {\n          ...activeSubmission,\n          formAction: location,\n        },\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation\n          ? pendingViewTransitionEnabled\n          : undefined,\n      });\n    } else {\n      // If we have a navigation submission, we will preserve it through the\n      // redirect navigation\n      let overrideNavigation = getLoadingNavigation(\n        redirectLocation,\n        submission\n      );\n      await startNavigation(redirectHistoryAction, redirectLocation, {\n        overrideNavigation,\n        // Send fetcher submissions through for shouldRevalidate\n        fetcherSubmission,\n        // Preserve these flags across redirects\n        preventScrollReset: preventScrollReset || pendingPreventScrollReset,\n        enableViewTransition: isNavigation\n          ? pendingViewTransitionEnabled\n          : undefined,\n      });\n    }\n  }\n\n  // Utility wrapper for calling dataStrategy client-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    state: RouterState,\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    fetcherKey: string | null\n  ): Promise<Record<string, DataResult>> {\n    let results: Record<string, DataStrategyResult>;\n    let dataResults: Record<string, DataResult> = {};\n    try {\n      results = await callDataStrategyImpl(\n        dataStrategyImpl,\n        type,\n        state,\n        request,\n        matchesToLoad,\n        matches,\n        fetcherKey,\n        manifest,\n        mapRouteProperties\n      );\n    } catch (e) {\n      // If the outer dataStrategy method throws, just return the error for all\n      // matches - and it'll naturally bubble to the root\n      matchesToLoad.forEach((m) => {\n        dataResults[m.route.id] = {\n          type: ResultType.error,\n          error: e,\n        };\n      });\n      return dataResults;\n    }\n\n    for (let [routeId, result] of Object.entries(results)) {\n      if (isRedirectDataStrategyResultResult(result)) {\n        let response = result.result as Response;\n        dataResults[routeId] = {\n          type: ResultType.redirect,\n          response: normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            routeId,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          ),\n        };\n      } else {\n        dataResults[routeId] = await convertDataStrategyResultToDataResult(\n          result\n        );\n      }\n    }\n\n    return dataResults;\n  }\n\n  async function callLoadersAndMaybeResolveData(\n    state: RouterState,\n    matches: AgnosticDataRouteMatch[],\n    matchesToLoad: AgnosticDataRouteMatch[],\n    fetchersToLoad: RevalidatingFetcher[],\n    request: Request\n  ) {\n    let currentMatches = state.matches;\n\n    // Kick off loaders and fetchers in parallel\n    let loaderResultsPromise = callDataStrategy(\n      \"loader\",\n      state,\n      request,\n      matchesToLoad,\n      matches,\n      null\n    );\n\n    let fetcherResultsPromise = Promise.all(\n      fetchersToLoad.map(async (f) => {\n        if (f.matches && f.match && f.controller) {\n          let results = await callDataStrategy(\n            \"loader\",\n            state,\n            createClientSideRequest(init.history, f.path, f.controller.signal),\n            [f.match],\n            f.matches,\n            f.key\n          );\n          let result = results[f.match.route.id];\n          // Fetcher results are keyed by fetcher key from here on out, not routeId\n          return { [f.key]: result };\n        } else {\n          return Promise.resolve({\n            [f.key]: {\n              type: ResultType.error,\n              error: getInternalRouterError(404, {\n                pathname: f.path,\n              }),\n            } as ErrorResult,\n          });\n        }\n      })\n    );\n\n    let loaderResults = await loaderResultsPromise;\n    let fetcherResults = (await fetcherResultsPromise).reduce(\n      (acc, r) => Object.assign(acc, r),\n      {}\n    );\n\n    await Promise.all([\n      resolveNavigationDeferredResults(\n        matches,\n        loaderResults,\n        request.signal,\n        currentMatches,\n        state.loaderData\n      ),\n      resolveFetcherDeferredResults(matches, fetcherResults, fetchersToLoad),\n    ]);\n\n    return {\n      loaderResults,\n      fetcherResults,\n    };\n  }\n\n  function interruptActiveLoads() {\n    // Every interruption triggers a revalidation\n    isRevalidationRequired = true;\n\n    // Cancel pending route-level deferreds and mark cancelled routes for\n    // revalidation\n    cancelledDeferredRoutes.push(...cancelActiveDeferreds());\n\n    // Abort in-flight fetcher loads\n    fetchLoadMatches.forEach((_, key) => {\n      if (fetchControllers.has(key)) {\n        cancelledFetcherLoads.add(key);\n      }\n      abortFetcher(key);\n    });\n  }\n\n  function updateFetcherState(\n    key: string,\n    fetcher: Fetcher,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    state.fetchers.set(key, fetcher);\n    updateState(\n      { fetchers: new Map(state.fetchers) },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function setFetcherError(\n    key: string,\n    routeId: string,\n    error: any,\n    opts: { flushSync?: boolean } = {}\n  ) {\n    let boundaryMatch = findNearestBoundary(state.matches, routeId);\n    deleteFetcher(key);\n    updateState(\n      {\n        errors: {\n          [boundaryMatch.route.id]: error,\n        },\n        fetchers: new Map(state.fetchers),\n      },\n      { flushSync: (opts && opts.flushSync) === true }\n    );\n  }\n\n  function getFetcher<TData = any>(key: string): Fetcher<TData> {\n    activeFetchers.set(key, (activeFetchers.get(key) || 0) + 1);\n    // If this fetcher was previously marked for deletion, unmark it since we\n    // have a new instance\n    if (deletedFetchers.has(key)) {\n      deletedFetchers.delete(key);\n    }\n    return state.fetchers.get(key) || IDLE_FETCHER;\n  }\n\n  function deleteFetcher(key: string): void {\n    let fetcher = state.fetchers.get(key);\n    // Don't abort the controller if this is a deletion of a fetcher.submit()\n    // in it's loading phase since - we don't want to abort the corresponding\n    // revalidation and want them to complete and land\n    if (\n      fetchControllers.has(key) &&\n      !(fetcher && fetcher.state === \"loading\" && fetchReloadIds.has(key))\n    ) {\n      abortFetcher(key);\n    }\n    fetchLoadMatches.delete(key);\n    fetchReloadIds.delete(key);\n    fetchRedirectIds.delete(key);\n\n    // If we opted into the flag we can clear this now since we're calling\n    // deleteFetcher() at the end of updateState() and we've already handed the\n    // deleted fetcher keys off to the data layer.\n    // If not, we're eagerly calling deleteFetcher() and we need to keep this\n    // Set populated until the next updateState call, and we'll clear\n    // `deletedFetchers` then\n    if (future.v7_fetcherPersist) {\n      deletedFetchers.delete(key);\n    }\n\n    cancelledFetcherLoads.delete(key);\n    state.fetchers.delete(key);\n  }\n\n  function deleteFetcherAndUpdateState(key: string): void {\n    let count = (activeFetchers.get(key) || 0) - 1;\n    if (count <= 0) {\n      activeFetchers.delete(key);\n      deletedFetchers.add(key);\n      if (!future.v7_fetcherPersist) {\n        deleteFetcher(key);\n      }\n    } else {\n      activeFetchers.set(key, count);\n    }\n\n    updateState({ fetchers: new Map(state.fetchers) });\n  }\n\n  function abortFetcher(key: string) {\n    let controller = fetchControllers.get(key);\n    if (controller) {\n      controller.abort();\n      fetchControllers.delete(key);\n    }\n  }\n\n  function markFetchersDone(keys: string[]) {\n    for (let key of keys) {\n      let fetcher = getFetcher(key);\n      let doneFetcher = getDoneFetcher(fetcher.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  }\n\n  function markFetchRedirectsDone(): boolean {\n    let doneKeys = [];\n    let updatedFetchers = false;\n    for (let key of fetchRedirectIds) {\n      let fetcher = state.fetchers.get(key);\n      invariant(fetcher, `Expected fetcher: ${key}`);\n      if (fetcher.state === \"loading\") {\n        fetchRedirectIds.delete(key);\n        doneKeys.push(key);\n        updatedFetchers = true;\n      }\n    }\n    markFetchersDone(doneKeys);\n    return updatedFetchers;\n  }\n\n  function abortStaleFetchLoads(landedId: number): boolean {\n    let yeetedKeys = [];\n    for (let [key, id] of fetchReloadIds) {\n      if (id < landedId) {\n        let fetcher = state.fetchers.get(key);\n        invariant(fetcher, `Expected fetcher: ${key}`);\n        if (fetcher.state === \"loading\") {\n          abortFetcher(key);\n          fetchReloadIds.delete(key);\n          yeetedKeys.push(key);\n        }\n      }\n    }\n    markFetchersDone(yeetedKeys);\n    return yeetedKeys.length > 0;\n  }\n\n  function getBlocker(key: string, fn: BlockerFunction) {\n    let blocker: Blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    if (blockerFunctions.get(key) !== fn) {\n      blockerFunctions.set(key, fn);\n    }\n\n    return blocker;\n  }\n\n  function deleteBlocker(key: string) {\n    state.blockers.delete(key);\n    blockerFunctions.delete(key);\n  }\n\n  // Utility function to update blockers, ensuring valid state transitions\n  function updateBlocker(key: string, newBlocker: Blocker) {\n    let blocker = state.blockers.get(key) || IDLE_BLOCKER;\n\n    // Poor mans state machine :)\n    // https://mermaid.live/edit#pako:eNqVkc9OwzAMxl8l8nnjAYrEtDIOHEBIgwvKJTReGy3_lDpIqO27k6awMG0XcrLlnz87nwdonESogKXXBuE79rq75XZO3-yHds0RJVuv70YrPlUrCEe2HfrORS3rubqZfuhtpg5C9wk5tZ4VKcRUq88q9Z8RS0-48cE1iHJkL0ugbHuFLus9L6spZy8nX9MP2CNdomVaposqu3fGayT8T8-jJQwhepo_UtpgBQaDEUom04dZhAN1aJBDlUKJBxE1ceB2Smj0Mln-IBW5AFU2dwUiktt_2Qaq2dBfaKdEup85UV7Yd-dKjlnkabl2Pvr0DTkTreM\n    invariant(\n      (blocker.state === \"unblocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"blocked\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"proceeding\") ||\n        (blocker.state === \"blocked\" && newBlocker.state === \"unblocked\") ||\n        (blocker.state === \"proceeding\" && newBlocker.state === \"unblocked\"),\n      `Invalid blocker state transition: ${blocker.state} -> ${newBlocker.state}`\n    );\n\n    let blockers = new Map(state.blockers);\n    blockers.set(key, newBlocker);\n    updateState({ blockers });\n  }\n\n  function shouldBlockNavigation({\n    currentLocation,\n    nextLocation,\n    historyAction,\n  }: {\n    currentLocation: Location;\n    nextLocation: Location;\n    historyAction: HistoryAction;\n  }): string | undefined {\n    if (blockerFunctions.size === 0) {\n      return;\n    }\n\n    // We ony support a single active blocker at the moment since we don't have\n    // any compelling use cases for multi-blocker yet\n    if (blockerFunctions.size > 1) {\n      warning(false, \"A router only supports one blocker at a time\");\n    }\n\n    let entries = Array.from(blockerFunctions.entries());\n    let [blockerKey, blockerFunction] = entries[entries.length - 1];\n    let blocker = state.blockers.get(blockerKey);\n\n    if (blocker && blocker.state === \"proceeding\") {\n      // If the blocker is currently proceeding, we don't need to re-check\n      // it and can let this navigation continue\n      return;\n    }\n\n    // At this point, we know we're unblocked/blocked so we need to check the\n    // user-provided blocker function\n    if (blockerFunction({ currentLocation, nextLocation, historyAction })) {\n      return blockerKey;\n    }\n  }\n\n  function handleNavigational404(pathname: string) {\n    let error = getInternalRouterError(404, { pathname });\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    let { matches, route } = getShortCircuitMatches(routesToUse);\n\n    // Cancel all pending deferred on 404s since we don't keep any routes\n    cancelActiveDeferreds();\n\n    return { notFoundMatches: matches, route, error };\n  }\n\n  function cancelActiveDeferreds(\n    predicate?: (routeId: string) => boolean\n  ): string[] {\n    let cancelledRouteIds: string[] = [];\n    activeDeferreds.forEach((dfd, routeId) => {\n      if (!predicate || predicate(routeId)) {\n        // Cancel the deferred - but do not remove from activeDeferreds here -\n        // we rely on the subscribers to do that so our tests can assert proper\n        // cleanup via _internalActiveDeferreds\n        dfd.cancel();\n        cancelledRouteIds.push(routeId);\n        activeDeferreds.delete(routeId);\n      }\n    });\n    return cancelledRouteIds;\n  }\n\n  // Opt in to capturing and reporting scroll positions during navigations,\n  // used by the <ScrollRestoration> component\n  function enableScrollRestoration(\n    positions: Record<string, number>,\n    getPosition: GetScrollPositionFunction,\n    getKey?: GetScrollRestorationKeyFunction\n  ) {\n    savedScrollPositions = positions;\n    getScrollPosition = getPosition;\n    getScrollRestorationKey = getKey || null;\n\n    // Perform initial hydration scroll restoration, since we miss the boat on\n    // the initial updateState() because we've not yet rendered <ScrollRestoration/>\n    // and therefore have no savedScrollPositions available\n    if (!initialScrollRestored && state.navigation === IDLE_NAVIGATION) {\n      initialScrollRestored = true;\n      let y = getSavedScrollPosition(state.location, state.matches);\n      if (y != null) {\n        updateState({ restoreScrollPosition: y });\n      }\n    }\n\n    return () => {\n      savedScrollPositions = null;\n      getScrollPosition = null;\n      getScrollRestorationKey = null;\n    };\n  }\n\n  function getScrollKey(location: Location, matches: AgnosticDataRouteMatch[]) {\n    if (getScrollRestorationKey) {\n      let key = getScrollRestorationKey(\n        location,\n        matches.map((m) => convertRouteMatchToUiMatch(m, state.loaderData))\n      );\n      return key || location.key;\n    }\n    return location.key;\n  }\n\n  function saveScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): void {\n    if (savedScrollPositions && getScrollPosition) {\n      let key = getScrollKey(location, matches);\n      savedScrollPositions[key] = getScrollPosition();\n    }\n  }\n\n  function getSavedScrollPosition(\n    location: Location,\n    matches: AgnosticDataRouteMatch[]\n  ): number | null {\n    if (savedScrollPositions) {\n      let key = getScrollKey(location, matches);\n      let y = savedScrollPositions[key];\n      if (typeof y === \"number\") {\n        return y;\n      }\n    }\n    return null;\n  }\n\n  function checkFogOfWar(\n    matches: AgnosticDataRouteMatch[] | null,\n    routesToUse: AgnosticDataRouteObject[],\n    pathname: string\n  ): { active: boolean; matches: AgnosticDataRouteMatch[] | null } {\n    if (patchRoutesOnNavigationImpl) {\n      if (!matches) {\n        let fogMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n          routesToUse,\n          pathname,\n          basename,\n          true\n        );\n\n        return { active: true, matches: fogMatches || [] };\n      } else {\n        if (Object.keys(matches[0].params).length > 0) {\n          // If we matched a dynamic param or a splat, it might only be because\n          // we haven't yet discovered other routes that would match with a\n          // higher score.  Call patchRoutesOnNavigation just to be sure\n          let partialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n            routesToUse,\n            pathname,\n            basename,\n            true\n          );\n          return { active: true, matches: partialMatches };\n        }\n      }\n    }\n\n    return { active: false, matches: null };\n  }\n\n  type DiscoverRoutesSuccessResult = {\n    type: \"success\";\n    matches: AgnosticDataRouteMatch[] | null;\n  };\n  type DiscoverRoutesErrorResult = {\n    type: \"error\";\n    error: any;\n    partialMatches: AgnosticDataRouteMatch[];\n  };\n  type DiscoverRoutesAbortedResult = { type: \"aborted\" };\n  type DiscoverRoutesResult =\n    | DiscoverRoutesSuccessResult\n    | DiscoverRoutesErrorResult\n    | DiscoverRoutesAbortedResult;\n\n  async function discoverRoutes(\n    matches: AgnosticDataRouteMatch[],\n    pathname: string,\n    signal: AbortSignal,\n    fetcherKey?: string\n  ): Promise<DiscoverRoutesResult> {\n    if (!patchRoutesOnNavigationImpl) {\n      return { type: \"success\", matches };\n    }\n\n    let partialMatches: AgnosticDataRouteMatch[] | null = matches;\n    while (true) {\n      let isNonHMR = inFlightDataRoutes == null;\n      let routesToUse = inFlightDataRoutes || dataRoutes;\n      let localManifest = manifest;\n      try {\n        await patchRoutesOnNavigationImpl({\n          signal,\n          path: pathname,\n          matches: partialMatches,\n          fetcherKey,\n          patch: (routeId, children) => {\n            if (signal.aborted) return;\n            patchRoutesImpl(\n              routeId,\n              children,\n              routesToUse,\n              localManifest,\n              mapRouteProperties\n            );\n          },\n        });\n      } catch (e) {\n        return { type: \"error\", error: e, partialMatches };\n      } finally {\n        // If we are not in the middle of an HMR revalidation and we changed the\n        // routes, provide a new identity so when we `updateState` at the end of\n        // this navigation/fetch `router.routes` will be a new identity and\n        // trigger a re-run of memoized `router.routes` dependencies.\n        // HMR will already update the identity and reflow when it lands\n        // `inFlightDataRoutes` in `completeNavigation`\n        if (isNonHMR && !signal.aborted) {\n          dataRoutes = [...dataRoutes];\n        }\n      }\n\n      if (signal.aborted) {\n        return { type: \"aborted\" };\n      }\n\n      let newMatches = matchRoutes(routesToUse, pathname, basename);\n      if (newMatches) {\n        return { type: \"success\", matches: newMatches };\n      }\n\n      let newPartialMatches = matchRoutesImpl<AgnosticDataRouteObject>(\n        routesToUse,\n        pathname,\n        basename,\n        true\n      );\n\n      // Avoid loops if the second pass results in the same partial matches\n      if (\n        !newPartialMatches ||\n        (partialMatches.length === newPartialMatches.length &&\n          partialMatches.every(\n            (m, i) => m.route.id === newPartialMatches![i].route.id\n          ))\n      ) {\n        return { type: \"success\", matches: null };\n      }\n\n      partialMatches = newPartialMatches;\n    }\n  }\n\n  function _internalSetRoutes(newRoutes: AgnosticDataRouteObject[]) {\n    manifest = {};\n    inFlightDataRoutes = convertRoutesToDataRoutes(\n      newRoutes,\n      mapRouteProperties,\n      undefined,\n      manifest\n    );\n  }\n\n  function patchRoutes(\n    routeId: string | null,\n    children: AgnosticRouteObject[]\n  ): void {\n    let isNonHMR = inFlightDataRoutes == null;\n    let routesToUse = inFlightDataRoutes || dataRoutes;\n    patchRoutesImpl(\n      routeId,\n      children,\n      routesToUse,\n      manifest,\n      mapRouteProperties\n    );\n\n    // If we are not in the middle of an HMR revalidation and we changed the\n    // routes, provide a new identity and trigger a reflow via `updateState`\n    // to re-run memoized `router.routes` dependencies.\n    // HMR will already update the identity and reflow when it lands\n    // `inFlightDataRoutes` in `completeNavigation`\n    if (isNonHMR) {\n      dataRoutes = [...dataRoutes];\n      updateState({});\n    }\n  }\n\n  router = {\n    get basename() {\n      return basename;\n    },\n    get future() {\n      return future;\n    },\n    get state() {\n      return state;\n    },\n    get routes() {\n      return dataRoutes;\n    },\n    get window() {\n      return routerWindow;\n    },\n    initialize,\n    subscribe,\n    enableScrollRestoration,\n    navigate,\n    fetch,\n    revalidate,\n    // Passthrough to history-aware createHref used by useHref so we get proper\n    // hash-aware URLs in DOM paths\n    createHref: (to: To) => init.history.createHref(to),\n    encodeLocation: (to: To) => init.history.encodeLocation(to),\n    getFetcher,\n    deleteFetcher: deleteFetcherAndUpdateState,\n    dispose,\n    getBlocker,\n    deleteBlocker,\n    patchRoutes,\n    _internalFetchControllers: fetchControllers,\n    _internalActiveDeferreds: activeDeferreds,\n    // TODO: Remove setRoutes, it's temporary to avoid dealing with\n    // updating the tree while validating the update algorithm.\n    _internalSetRoutes,\n  };\n\n  return router;\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region createStaticHandler\n////////////////////////////////////////////////////////////////////////////////\n\nexport const UNSAFE_DEFERRED_SYMBOL = Symbol(\"deferred\");\n\n/**\n * Future flags to toggle new feature behavior\n */\nexport interface StaticHandlerFutureConfig {\n  v7_relativeSplatPath: boolean;\n  v7_throwAbortReason: boolean;\n}\n\nexport interface CreateStaticHandlerOptions {\n  basename?: string;\n  /**\n   * @deprecated Use `mapRouteProperties` instead\n   */\n  detectErrorBoundary?: DetectErrorBoundaryFunction;\n  mapRouteProperties?: MapRoutePropertiesFunction;\n  future?: Partial<StaticHandlerFutureConfig>;\n}\n\nexport function createStaticHandler(\n  routes: AgnosticRouteObject[],\n  opts?: CreateStaticHandlerOptions\n): StaticHandler {\n  invariant(\n    routes.length > 0,\n    \"You must provide a non-empty routes array to createStaticHandler\"\n  );\n\n  let manifest: RouteManifest = {};\n  let basename = (opts ? opts.basename : null) || \"/\";\n  let mapRouteProperties: MapRoutePropertiesFunction;\n  if (opts?.mapRouteProperties) {\n    mapRouteProperties = opts.mapRouteProperties;\n  } else if (opts?.detectErrorBoundary) {\n    // If they are still using the deprecated version, wrap it with the new API\n    let detectErrorBoundary = opts.detectErrorBoundary;\n    mapRouteProperties = (route) => ({\n      hasErrorBoundary: detectErrorBoundary(route),\n    });\n  } else {\n    mapRouteProperties = defaultMapRouteProperties;\n  }\n  // Config driven behavior flags\n  let future: StaticHandlerFutureConfig = {\n    v7_relativeSplatPath: false,\n    v7_throwAbortReason: false,\n    ...(opts ? opts.future : null),\n  };\n\n  let dataRoutes = convertRoutesToDataRoutes(\n    routes,\n    mapRouteProperties,\n    undefined,\n    manifest\n  );\n\n  /**\n   * The query() method is intended for document requests, in which we want to\n   * call an optional action and potentially multiple loaders for all nested\n   * routes.  It returns a StaticHandlerContext object, which is very similar\n   * to the router state (location, loaderData, actionData, errors, etc.) and\n   * also adds SSR-specific information such as the statusCode and headers\n   * from action/loaders Responses.\n   *\n   * It _should_ never throw and should report all errors through the\n   * returned context.errors object, properly associating errors to their error\n   * boundary.  Additionally, it tracks _deepestRenderedBoundaryId which can be\n   * used to emulate React error boundaries during SSr by performing a second\n   * pass only down to the boundaryId.\n   *\n   * The one exception where we do not return a StaticHandlerContext is when a\n   * redirect response is returned or thrown from any action/loader.  We\n   * propagate that out and return the raw Response so the HTTP server can\n   * return it directly.\n   *\n   * - `opts.requestContext` is an optional server context that will be passed\n   *   to actions/loaders in the `context` parameter\n   * - `opts.skipLoaderErrorBubbling` is an optional parameter that will prevent\n   *   the bubbling of errors which allows single-fetch-type implementations\n   *   where the client will handle the bubbling and we may need to return data\n   *   for the handling route\n   */\n  async function query(\n    request: Request,\n    {\n      requestContext,\n      skipLoaderErrorBubbling,\n      dataStrategy,\n    }: {\n      requestContext?: unknown;\n      skipLoaderErrorBubbling?: boolean;\n      dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<StaticHandlerContext | Response> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\") {\n      let error = getInternalRouterError(405, { method });\n      let { matches: methodNotAllowedMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: methodNotAllowedMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    } else if (!matches) {\n      let error = getInternalRouterError(404, { pathname: location.pathname });\n      let { matches: notFoundMatches, route } =\n        getShortCircuitMatches(dataRoutes);\n      return {\n        basename,\n        location,\n        matches: notFoundMatches,\n        loaderData: {},\n        actionData: null,\n        errors: {\n          [route.id]: error,\n        },\n        statusCode: error.status,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      dataStrategy || null,\n      skipLoaderErrorBubbling === true,\n      null\n    );\n    if (isResponse(result)) {\n      return result;\n    }\n\n    // When returning StaticHandlerContext, we patch back in the location here\n    // since we need it for React Context.  But this helps keep our submit and\n    // loadRouteData operating on a Request instead of a Location\n    return { location, basename, ...result };\n  }\n\n  /**\n   * The queryRoute() method is intended for targeted route requests, either\n   * for fetch ?_data requests or resource route requests.  In this case, we\n   * are only ever calling a single action or loader, and we are returning the\n   * returned value directly.  In most cases, this will be a Response returned\n   * from the action/loader, but it may be a primitive or other value as well -\n   * and in such cases the calling context should handle that accordingly.\n   *\n   * We do respect the throw/return differentiation, so if an action/loader\n   * throws, then this method will throw the value.  This is important so we\n   * can do proper boundary identification in Remix where a thrown Response\n   * must go to the Catch Boundary but a returned Response is happy-path.\n   *\n   * One thing to note is that any Router-initiated Errors that make sense\n   * to associate with a status code will be thrown as an ErrorResponse\n   * instance which include the raw Error, such that the calling context can\n   * serialize the error as they see fit while including the proper response\n   * code.  Examples here are 404 and 405 errors that occur prior to reaching\n   * any user-defined loaders.\n   *\n   * - `opts.routeId` allows you to specify the specific route handler to call.\n   *   If not provided the handler will determine the proper route by matching\n   *   against `request.url`\n   * - `opts.requestContext` is an optional server context that will be passed\n   *    to actions/loaders in the `context` parameter\n   */\n  async function queryRoute(\n    request: Request,\n    {\n      routeId,\n      requestContext,\n      dataStrategy,\n    }: {\n      requestContext?: unknown;\n      routeId?: string;\n      dataStrategy?: DataStrategyFunction;\n    } = {}\n  ): Promise<any> {\n    let url = new URL(request.url);\n    let method = request.method;\n    let location = createLocation(\"\", createPath(url), null, \"default\");\n    let matches = matchRoutes(dataRoutes, location, basename);\n\n    // SSR supports HEAD requests while SPA doesn't\n    if (!isValidMethod(method) && method !== \"HEAD\" && method !== \"OPTIONS\") {\n      throw getInternalRouterError(405, { method });\n    } else if (!matches) {\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let match = routeId\n      ? matches.find((m) => m.route.id === routeId)\n      : getTargetMatch(matches, location);\n\n    if (routeId && !match) {\n      throw getInternalRouterError(403, {\n        pathname: location.pathname,\n        routeId,\n      });\n    } else if (!match) {\n      // This should never hit I don't think?\n      throw getInternalRouterError(404, { pathname: location.pathname });\n    }\n\n    let result = await queryImpl(\n      request,\n      location,\n      matches,\n      requestContext,\n      dataStrategy || null,\n      false,\n      match\n    );\n\n    if (isResponse(result)) {\n      return result;\n    }\n\n    let error = result.errors ? Object.values(result.errors)[0] : undefined;\n    if (error !== undefined) {\n      // If we got back result.errors, that means the loader/action threw\n      // _something_ that wasn't a Response, but it's not guaranteed/required\n      // to be an `instanceof Error` either, so we have to use throw here to\n      // preserve the \"error\" state outside of queryImpl.\n      throw error;\n    }\n\n    // Pick off the right state value to return\n    if (result.actionData) {\n      return Object.values(result.actionData)[0];\n    }\n\n    if (result.loaderData) {\n      let data = Object.values(result.loaderData)[0];\n      if (result.activeDeferreds?.[match.route.id]) {\n        data[UNSAFE_DEFERRED_SYMBOL] = result.activeDeferreds[match.route.id];\n      }\n      return data;\n    }\n\n    return undefined;\n  }\n\n  async function queryImpl(\n    request: Request,\n    location: Location,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    invariant(\n      request.signal,\n      \"query()/queryRoute() requests must contain an AbortController signal\"\n    );\n\n    try {\n      if (isMutationMethod(request.method.toLowerCase())) {\n        let result = await submit(\n          request,\n          matches,\n          routeMatch || getTargetMatch(matches, location),\n          requestContext,\n          dataStrategy,\n          skipLoaderErrorBubbling,\n          routeMatch != null\n        );\n        return result;\n      }\n\n      let result = await loadRouteData(\n        request,\n        matches,\n        requestContext,\n        dataStrategy,\n        skipLoaderErrorBubbling,\n        routeMatch\n      );\n      return isResponse(result)\n        ? result\n        : {\n            ...result,\n            actionData: null,\n            actionHeaders: {},\n          };\n    } catch (e) {\n      // If the user threw/returned a Response in callLoaderOrAction for a\n      // `queryRoute` call, we throw the `DataStrategyResult` to bail out early\n      // and then return or throw the raw Response here accordingly\n      if (isDataStrategyResult(e) && isResponse(e.result)) {\n        if (e.type === ResultType.error) {\n          throw e.result;\n        }\n        return e.result;\n      }\n      // Redirects are always returned since they don't propagate to catch\n      // boundaries\n      if (isRedirectResponse(e)) {\n        return e;\n      }\n      throw e;\n    }\n  }\n\n  async function submit(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    actionMatch: AgnosticDataRouteMatch,\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    isRouteRequest: boolean\n  ): Promise<Omit<StaticHandlerContext, \"location\" | \"basename\"> | Response> {\n    let result: DataResult;\n\n    if (!actionMatch.route.action && !actionMatch.route.lazy) {\n      let error = getInternalRouterError(405, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: actionMatch.route.id,\n      });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    } else {\n      let results = await callDataStrategy(\n        \"action\",\n        request,\n        [actionMatch],\n        matches,\n        isRouteRequest,\n        requestContext,\n        dataStrategy\n      );\n      result = results[actionMatch.route.id];\n\n      if (request.signal.aborted) {\n        throwStaticHandlerAbortedError(request, isRouteRequest, future);\n      }\n    }\n\n    if (isRedirectResult(result)) {\n      // Uhhhh - this should never happen, we should always throw these from\n      // callLoaderOrAction, but the type narrowing here keeps TS happy and we\n      // can get back on the \"throw all redirect responses\" train here should\n      // this ever happen :/\n      throw new Response(null, {\n        status: result.response.status,\n        headers: {\n          Location: result.response.headers.get(\"Location\")!,\n        },\n      });\n    }\n\n    if (isDeferredResult(result)) {\n      let error = getInternalRouterError(400, { type: \"defer-action\" });\n      if (isRouteRequest) {\n        throw error;\n      }\n      result = {\n        type: ResultType.error,\n        error,\n      };\n    }\n\n    if (isRouteRequest) {\n      // Note: This should only be non-Response values if we get here, since\n      // isRouteRequest should throw any Response received in callLoaderOrAction\n      if (isErrorResult(result)) {\n        throw result.error;\n      }\n\n      return {\n        matches: [actionMatch],\n        loaderData: {},\n        actionData: { [actionMatch.route.id]: result.data },\n        errors: null,\n        // Note: statusCode + headers are unused here since queryRoute will\n        // return the raw Response or value\n        statusCode: 200,\n        loaderHeaders: {},\n        actionHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    // Create a GET request for the loaders\n    let loaderRequest = new Request(request.url, {\n      headers: request.headers,\n      redirect: request.redirect,\n      signal: request.signal,\n    });\n\n    if (isErrorResult(result)) {\n      // Store off the pending error - we use it to determine which loaders\n      // to call and will commit it when we complete the navigation\n      let boundaryMatch = skipLoaderErrorBubbling\n        ? actionMatch\n        : findNearestBoundary(matches, actionMatch.route.id);\n\n      let context = await loadRouteData(\n        loaderRequest,\n        matches,\n        requestContext,\n        dataStrategy,\n        skipLoaderErrorBubbling,\n        null,\n        [boundaryMatch.route.id, result]\n      );\n\n      // action status codes take precedence over loader status codes\n      return {\n        ...context,\n        statusCode: isRouteErrorResponse(result.error)\n          ? result.error.status\n          : result.statusCode != null\n          ? result.statusCode\n          : 500,\n        actionData: null,\n        actionHeaders: {\n          ...(result.headers ? { [actionMatch.route.id]: result.headers } : {}),\n        },\n      };\n    }\n\n    let context = await loadRouteData(\n      loaderRequest,\n      matches,\n      requestContext,\n      dataStrategy,\n      skipLoaderErrorBubbling,\n      null\n    );\n\n    return {\n      ...context,\n      actionData: {\n        [actionMatch.route.id]: result.data,\n      },\n      // action status codes take precedence over loader status codes\n      ...(result.statusCode ? { statusCode: result.statusCode } : {}),\n      actionHeaders: result.headers\n        ? { [actionMatch.route.id]: result.headers }\n        : {},\n    };\n  }\n\n  async function loadRouteData(\n    request: Request,\n    matches: AgnosticDataRouteMatch[],\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null,\n    skipLoaderErrorBubbling: boolean,\n    routeMatch: AgnosticDataRouteMatch | null,\n    pendingActionResult?: PendingActionResult\n  ): Promise<\n    | Omit<\n        StaticHandlerContext,\n        \"location\" | \"basename\" | \"actionData\" | \"actionHeaders\"\n      >\n    | Response\n  > {\n    let isRouteRequest = routeMatch != null;\n\n    // Short circuit if we have no loaders to run (queryRoute())\n    if (\n      isRouteRequest &&\n      !routeMatch?.route.loader &&\n      !routeMatch?.route.lazy\n    ) {\n      throw getInternalRouterError(400, {\n        method: request.method,\n        pathname: new URL(request.url).pathname,\n        routeId: routeMatch?.route.id,\n      });\n    }\n\n    let requestMatches = routeMatch\n      ? [routeMatch]\n      : pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? getLoaderMatchesUntilBoundary(matches, pendingActionResult[0])\n      : matches;\n    let matchesToLoad = requestMatches.filter(\n      (m) => m.route.loader || m.route.lazy\n    );\n\n    // Short circuit if we have no loaders to run (query())\n    if (matchesToLoad.length === 0) {\n      return {\n        matches,\n        // Add a null for all matched routes for proper revalidation on the client\n        loaderData: matches.reduce(\n          (acc, m) => Object.assign(acc, { [m.route.id]: null }),\n          {}\n        ),\n        errors:\n          pendingActionResult && isErrorResult(pendingActionResult[1])\n            ? {\n                [pendingActionResult[0]]: pendingActionResult[1].error,\n              }\n            : null,\n        statusCode: 200,\n        loaderHeaders: {},\n        activeDeferreds: null,\n      };\n    }\n\n    let results = await callDataStrategy(\n      \"loader\",\n      request,\n      matchesToLoad,\n      matches,\n      isRouteRequest,\n      requestContext,\n      dataStrategy\n    );\n\n    if (request.signal.aborted) {\n      throwStaticHandlerAbortedError(request, isRouteRequest, future);\n    }\n\n    // Process and commit output from loaders\n    let activeDeferreds = new Map<string, DeferredData>();\n    let context = processRouteLoaderData(\n      matches,\n      results,\n      pendingActionResult,\n      activeDeferreds,\n      skipLoaderErrorBubbling\n    );\n\n    // Add a null for any non-loader matches for proper revalidation on the client\n    let executedLoaders = new Set<string>(\n      matchesToLoad.map((match) => match.route.id)\n    );\n    matches.forEach((match) => {\n      if (!executedLoaders.has(match.route.id)) {\n        context.loaderData[match.route.id] = null;\n      }\n    });\n\n    return {\n      ...context,\n      matches,\n      activeDeferreds:\n        activeDeferreds.size > 0\n          ? Object.fromEntries(activeDeferreds.entries())\n          : null,\n    };\n  }\n\n  // Utility wrapper for calling dataStrategy server-side without having to\n  // pass around the manifest, mapRouteProperties, etc.\n  async function callDataStrategy(\n    type: \"loader\" | \"action\",\n    request: Request,\n    matchesToLoad: AgnosticDataRouteMatch[],\n    matches: AgnosticDataRouteMatch[],\n    isRouteRequest: boolean,\n    requestContext: unknown,\n    dataStrategy: DataStrategyFunction | null\n  ): Promise<Record<string, DataResult>> {\n    let results = await callDataStrategyImpl(\n      dataStrategy || defaultDataStrategy,\n      type,\n      null,\n      request,\n      matchesToLoad,\n      matches,\n      null,\n      manifest,\n      mapRouteProperties,\n      requestContext\n    );\n\n    let dataResults: Record<string, DataResult> = {};\n    await Promise.all(\n      matches.map(async (match) => {\n        if (!(match.route.id in results)) {\n          return;\n        }\n        let result = results[match.route.id];\n        if (isRedirectDataStrategyResultResult(result)) {\n          let response = result.result as Response;\n          // Throw redirects and let the server handle them with an HTTP redirect\n          throw normalizeRelativeRoutingRedirectResponse(\n            response,\n            request,\n            match.route.id,\n            matches,\n            basename,\n            future.v7_relativeSplatPath\n          );\n        }\n        if (isResponse(result.result) && isRouteRequest) {\n          // For SSR single-route requests, we want to hand Responses back\n          // directly without unwrapping\n          throw result;\n        }\n\n        dataResults[match.route.id] =\n          await convertDataStrategyResultToDataResult(result);\n      })\n    );\n    return dataResults;\n  }\n\n  return {\n    dataRoutes,\n    query,\n    queryRoute,\n  };\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Helpers\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n * Given an existing StaticHandlerContext and an error thrown at render time,\n * provide an updated StaticHandlerContext suitable for a second SSR render\n */\nexport function getStaticContextFromError(\n  routes: AgnosticDataRouteObject[],\n  context: StaticHandlerContext,\n  error: any\n) {\n  let newContext: StaticHandlerContext = {\n    ...context,\n    statusCode: isRouteErrorResponse(error) ? error.status : 500,\n    errors: {\n      [context._deepestRenderedBoundaryId || routes[0].id]: error,\n    },\n  };\n  return newContext;\n}\n\nfunction throwStaticHandlerAbortedError(\n  request: Request,\n  isRouteRequest: boolean,\n  future: StaticHandlerFutureConfig\n) {\n  if (future.v7_throwAbortReason && request.signal.reason !== undefined) {\n    throw request.signal.reason;\n  }\n\n  let method = isRouteRequest ? \"queryRoute\" : \"query\";\n  throw new Error(`${method}() call aborted: ${request.method} ${request.url}`);\n}\n\nfunction isSubmissionNavigation(\n  opts: BaseNavigateOrFetchOptions\n): opts is SubmissionNavigateOptions {\n  return (\n    opts != null &&\n    ((\"formData\" in opts && opts.formData != null) ||\n      (\"body\" in opts && opts.body !== undefined))\n  );\n}\n\nfunction normalizeTo(\n  location: Path,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  prependBasename: boolean,\n  to: To | null,\n  v7_relativeSplatPath: boolean,\n  fromRouteId?: string,\n  relative?: RelativeRoutingType\n) {\n  let contextualMatches: AgnosticDataRouteMatch[];\n  let activeRouteMatch: AgnosticDataRouteMatch | undefined;\n  if (fromRouteId) {\n    // Grab matches up to the calling route so our route-relative logic is\n    // relative to the correct source route\n    contextualMatches = [];\n    for (let match of matches) {\n      contextualMatches.push(match);\n      if (match.route.id === fromRouteId) {\n        activeRouteMatch = match;\n        break;\n      }\n    }\n  } else {\n    contextualMatches = matches;\n    activeRouteMatch = matches[matches.length - 1];\n  }\n\n  // Resolve the relative path\n  let path = resolveTo(\n    to ? to : \".\",\n    getResolveToMatches(contextualMatches, v7_relativeSplatPath),\n    stripBasename(location.pathname, basename) || location.pathname,\n    relative === \"path\"\n  );\n\n  // When `to` is not specified we inherit search/hash from the current\n  // location, unlike when to=\".\" and we just inherit the path.\n  // See https://github.com/remix-run/remix/issues/927\n  if (to == null) {\n    path.search = location.search;\n    path.hash = location.hash;\n  }\n\n  // Account for `?index` params when routing to the current location\n  if ((to == null || to === \"\" || to === \".\") && activeRouteMatch) {\n    let nakedIndex = hasNakedIndexQuery(path.search);\n    if (activeRouteMatch.route.index && !nakedIndex) {\n      // Add one when we're targeting an index route\n      path.search = path.search\n        ? path.search.replace(/^\\?/, \"?index&\")\n        : \"?index\";\n    } else if (!activeRouteMatch.route.index && nakedIndex) {\n      // Remove existing ones when we're not\n      let params = new URLSearchParams(path.search);\n      let indexValues = params.getAll(\"index\");\n      params.delete(\"index\");\n      indexValues.filter((v) => v).forEach((v) => params.append(\"index\", v));\n      let qs = params.toString();\n      path.search = qs ? `?${qs}` : \"\";\n    }\n  }\n\n  // If we're operating within a basename, prepend it to the pathname.  If\n  // this is a root navigation, then just use the raw basename which allows\n  // the basename to have full control over the presence of a trailing slash\n  // on root actions\n  if (prependBasename && basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\n// Normalize navigation options by converting formMethod=GET formData objects to\n// URLSearchParams so they behave identically to links with query params\nfunction normalizeNavigateOptions(\n  normalizeFormMethod: boolean,\n  isFetcher: boolean,\n  path: string,\n  opts?: BaseNavigateOrFetchOptions\n): {\n  path: string;\n  submission?: Submission;\n  error?: ErrorResponseImpl;\n} {\n  // Return location verbatim on non-submission navigations\n  if (!opts || !isSubmissionNavigation(opts)) {\n    return { path };\n  }\n\n  if (opts.formMethod && !isValidMethod(opts.formMethod)) {\n    return {\n      path,\n      error: getInternalRouterError(405, { method: opts.formMethod }),\n    };\n  }\n\n  let getInvalidBodyError = () => ({\n    path,\n    error: getInternalRouterError(400, { type: \"invalid-body\" }),\n  });\n\n  // Create a Submission on non-GET navigations\n  let rawFormMethod = opts.formMethod || \"get\";\n  let formMethod = normalizeFormMethod\n    ? (rawFormMethod.toUpperCase() as V7_FormMethod)\n    : (rawFormMethod.toLowerCase() as FormMethod);\n  let formAction = stripHashFromPath(path);\n\n  if (opts.body !== undefined) {\n    if (opts.formEncType === \"text/plain\") {\n      // text only support POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      let text =\n        typeof opts.body === \"string\"\n          ? opts.body\n          : opts.body instanceof FormData ||\n            opts.body instanceof URLSearchParams\n          ? // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#plain-text-form-data\n            Array.from(opts.body.entries()).reduce(\n              (acc, [name, value]) => `${acc}${name}=${value}\\n`,\n              \"\"\n            )\n          : String(opts.body);\n\n      return {\n        path,\n        submission: {\n          formMethod,\n          formAction,\n          formEncType: opts.formEncType,\n          formData: undefined,\n          json: undefined,\n          text,\n        },\n      };\n    } else if (opts.formEncType === \"application/json\") {\n      // json only supports POST/PUT/PATCH/DELETE submissions\n      if (!isMutationMethod(formMethod)) {\n        return getInvalidBodyError();\n      }\n\n      try {\n        let json =\n          typeof opts.body === \"string\" ? JSON.parse(opts.body) : opts.body;\n\n        return {\n          path,\n          submission: {\n            formMethod,\n            formAction,\n            formEncType: opts.formEncType,\n            formData: undefined,\n            json,\n            text: undefined,\n          },\n        };\n      } catch (e) {\n        return getInvalidBodyError();\n      }\n    }\n  }\n\n  invariant(\n    typeof FormData === \"function\",\n    \"FormData is not available in this environment\"\n  );\n\n  let searchParams: URLSearchParams;\n  let formData: FormData;\n\n  if (opts.formData) {\n    searchParams = convertFormDataToSearchParams(opts.formData);\n    formData = opts.formData;\n  } else if (opts.body instanceof FormData) {\n    searchParams = convertFormDataToSearchParams(opts.body);\n    formData = opts.body;\n  } else if (opts.body instanceof URLSearchParams) {\n    searchParams = opts.body;\n    formData = convertSearchParamsToFormData(searchParams);\n  } else if (opts.body == null) {\n    searchParams = new URLSearchParams();\n    formData = new FormData();\n  } else {\n    try {\n      searchParams = new URLSearchParams(opts.body);\n      formData = convertSearchParamsToFormData(searchParams);\n    } catch (e) {\n      return getInvalidBodyError();\n    }\n  }\n\n  let submission: Submission = {\n    formMethod,\n    formAction,\n    formEncType:\n      (opts && opts.formEncType) || \"application/x-www-form-urlencoded\",\n    formData,\n    json: undefined,\n    text: undefined,\n  };\n\n  if (isMutationMethod(submission.formMethod)) {\n    return { path, submission };\n  }\n\n  // Flatten submission onto URLSearchParams for GET submissions\n  let parsedPath = parsePath(path);\n  // On GET navigation submissions we can drop the ?index param from the\n  // resulting location since all loaders will run.  But fetcher GET submissions\n  // only run a single loader so we need to preserve any incoming ?index params\n  if (isFetcher && parsedPath.search && hasNakedIndexQuery(parsedPath.search)) {\n    searchParams.append(\"index\", \"\");\n  }\n  parsedPath.search = `?${searchParams}`;\n\n  return { path: createPath(parsedPath), submission };\n}\n\n// Filter out all routes at/below any caught error as they aren't going to\n// render so we don't need to load them\nfunction getLoaderMatchesUntilBoundary(\n  matches: AgnosticDataRouteMatch[],\n  boundaryId: string,\n  includeBoundary = false\n) {\n  let index = matches.findIndex((m) => m.route.id === boundaryId);\n  if (index >= 0) {\n    return matches.slice(0, includeBoundary ? index + 1 : index);\n  }\n  return matches;\n}\n\nfunction getMatchesToLoad(\n  history: History,\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  submission: Submission | undefined,\n  location: Location,\n  initialHydration: boolean,\n  skipActionErrorRevalidation: boolean,\n  isRevalidationRequired: boolean,\n  cancelledDeferredRoutes: string[],\n  cancelledFetcherLoads: Set<string>,\n  deletedFetchers: Set<string>,\n  fetchLoadMatches: Map<string, FetchLoadMatch>,\n  fetchRedirectIds: Set<string>,\n  routesToUse: AgnosticDataRouteObject[],\n  basename: string | undefined,\n  pendingActionResult?: PendingActionResult\n): [AgnosticDataRouteMatch[], RevalidatingFetcher[]] {\n  let actionResult = pendingActionResult\n    ? isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : pendingActionResult[1].data\n    : undefined;\n  let currentUrl = history.createURL(state.location);\n  let nextUrl = history.createURL(location);\n\n  // Pick navigation matches that are net-new or qualify for revalidation\n  let boundaryMatches = matches;\n  if (initialHydration && state.errors) {\n    // On initial hydration, only consider matches up to _and including_ the boundary.\n    // This is inclusive to handle cases where a server loader ran successfully,\n    // a child server loader bubbled up to this route, but this route has\n    // `clientLoader.hydrate` so we want to still run the `clientLoader` so that\n    // we have a complete version of `loaderData`\n    boundaryMatches = getLoaderMatchesUntilBoundary(\n      matches,\n      Object.keys(state.errors)[0],\n      true\n    );\n  } else if (pendingActionResult && isErrorResult(pendingActionResult[1])) {\n    // If an action threw an error, we call loaders up to, but not including the\n    // boundary\n    boundaryMatches = getLoaderMatchesUntilBoundary(\n      matches,\n      pendingActionResult[0]\n    );\n  }\n\n  // Don't revalidate loaders by default after action 4xx/5xx responses\n  // when the flag is enabled.  They can still opt-into revalidation via\n  // `shouldRevalidate` via `actionResult`\n  let actionStatus = pendingActionResult\n    ? pendingActionResult[1].statusCode\n    : undefined;\n  let shouldSkipRevalidation =\n    skipActionErrorRevalidation && actionStatus && actionStatus >= 400;\n\n  let navigationMatches = boundaryMatches.filter((match, index) => {\n    let { route } = match;\n    if (route.lazy) {\n      // We haven't loaded this route yet so we don't know if it's got a loader!\n      return true;\n    }\n\n    if (route.loader == null) {\n      return false;\n    }\n\n    if (initialHydration) {\n      return shouldLoadRouteOnHydration(route, state.loaderData, state.errors);\n    }\n\n    // Always call the loader on new route instances and pending defer cancellations\n    if (\n      isNewLoader(state.loaderData, state.matches[index], match) ||\n      cancelledDeferredRoutes.some((id) => id === match.route.id)\n    ) {\n      return true;\n    }\n\n    // This is the default implementation for when we revalidate.  If the route\n    // provides it's own implementation, then we give them full control but\n    // provide this value so they can leverage it if needed after they check\n    // their own specific use cases\n    let currentRouteMatch = state.matches[index];\n    let nextRouteMatch = match;\n\n    return shouldRevalidateLoader(match, {\n      currentUrl,\n      currentParams: currentRouteMatch.params,\n      nextUrl,\n      nextParams: nextRouteMatch.params,\n      ...submission,\n      actionResult,\n      actionStatus,\n      defaultShouldRevalidate: shouldSkipRevalidation\n        ? false\n        : // Forced revalidation due to submission, useRevalidator, or X-Remix-Revalidate\n          isRevalidationRequired ||\n          currentUrl.pathname + currentUrl.search ===\n            nextUrl.pathname + nextUrl.search ||\n          // Search params affect all loaders\n          currentUrl.search !== nextUrl.search ||\n          isNewRouteInstance(currentRouteMatch, nextRouteMatch),\n    });\n  });\n\n  // Pick fetcher.loads that need to be revalidated\n  let revalidatingFetchers: RevalidatingFetcher[] = [];\n  fetchLoadMatches.forEach((f, key) => {\n    // Don't revalidate:\n    //  - on initial hydration (shouldn't be any fetchers then anyway)\n    //  - if fetcher won't be present in the subsequent render\n    //    - no longer matches the URL (v7_fetcherPersist=false)\n    //    - was unmounted but persisted due to v7_fetcherPersist=true\n    if (\n      initialHydration ||\n      !matches.some((m) => m.route.id === f.routeId) ||\n      deletedFetchers.has(key)\n    ) {\n      return;\n    }\n\n    let fetcherMatches = matchRoutes(routesToUse, f.path, basename);\n\n    // If the fetcher path no longer matches, push it in with null matches so\n    // we can trigger a 404 in callLoadersAndMaybeResolveData.  Note this is\n    // currently only a use-case for Remix HMR where the route tree can change\n    // at runtime and remove a route previously loaded via a fetcher\n    if (!fetcherMatches) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: null,\n        match: null,\n        controller: null,\n      });\n      return;\n    }\n\n    // Revalidating fetchers are decoupled from the route matches since they\n    // load from a static href.  They revalidate based on explicit revalidation\n    // (submission, useRevalidator, or X-Remix-Revalidate)\n    let fetcher = state.fetchers.get(key);\n    let fetcherMatch = getTargetMatch(fetcherMatches, f.path);\n\n    let shouldRevalidate = false;\n    if (fetchRedirectIds.has(key)) {\n      // Never trigger a revalidation of an actively redirecting fetcher\n      shouldRevalidate = false;\n    } else if (cancelledFetcherLoads.has(key)) {\n      // Always mark for revalidation if the fetcher was cancelled\n      cancelledFetcherLoads.delete(key);\n      shouldRevalidate = true;\n    } else if (\n      fetcher &&\n      fetcher.state !== \"idle\" &&\n      fetcher.data === undefined\n    ) {\n      // If the fetcher hasn't ever completed loading yet, then this isn't a\n      // revalidation, it would just be a brand new load if an explicit\n      // revalidation is required\n      shouldRevalidate = isRevalidationRequired;\n    } else {\n      // Otherwise fall back on any user-defined shouldRevalidate, defaulting\n      // to explicit revalidations only\n      shouldRevalidate = shouldRevalidateLoader(fetcherMatch, {\n        currentUrl,\n        currentParams: state.matches[state.matches.length - 1].params,\n        nextUrl,\n        nextParams: matches[matches.length - 1].params,\n        ...submission,\n        actionResult,\n        actionStatus,\n        defaultShouldRevalidate: shouldSkipRevalidation\n          ? false\n          : isRevalidationRequired,\n      });\n    }\n\n    if (shouldRevalidate) {\n      revalidatingFetchers.push({\n        key,\n        routeId: f.routeId,\n        path: f.path,\n        matches: fetcherMatches,\n        match: fetcherMatch,\n        controller: new AbortController(),\n      });\n    }\n  });\n\n  return [navigationMatches, revalidatingFetchers];\n}\n\nfunction shouldLoadRouteOnHydration(\n  route: AgnosticDataRouteObject,\n  loaderData: RouteData | null | undefined,\n  errors: RouteData | null | undefined\n) {\n  // We dunno if we have a loader - gotta find out!\n  if (route.lazy) {\n    return true;\n  }\n\n  // No loader, nothing to initialize\n  if (!route.loader) {\n    return false;\n  }\n\n  let hasData = loaderData != null && loaderData[route.id] !== undefined;\n  let hasError = errors != null && errors[route.id] !== undefined;\n\n  // Don't run if we error'd during SSR\n  if (!hasData && hasError) {\n    return false;\n  }\n\n  // Explicitly opting-in to running on hydration\n  if (typeof route.loader === \"function\" && route.loader.hydrate === true) {\n    return true;\n  }\n\n  // Otherwise, run if we're not yet initialized with anything\n  return !hasData && !hasError;\n}\n\nfunction isNewLoader(\n  currentLoaderData: RouteData,\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let isNew =\n    // [a] -> [a, b]\n    !currentMatch ||\n    // [a, b] -> [a, c]\n    match.route.id !== currentMatch.route.id;\n\n  // Handle the case that we don't have data for a re-used route, potentially\n  // from a prior error or from a cancelled pending deferred\n  let isMissingData = currentLoaderData[match.route.id] === undefined;\n\n  // Always load if this is a net-new route or we don't yet have data\n  return isNew || isMissingData;\n}\n\nfunction isNewRouteInstance(\n  currentMatch: AgnosticDataRouteMatch,\n  match: AgnosticDataRouteMatch\n) {\n  let currentPath = currentMatch.route.path;\n  return (\n    // param change for this match, /users/123 -> /users/456\n    currentMatch.pathname !== match.pathname ||\n    // splat param changed, which is not present in match.path\n    // e.g. /files/images/avatar.jpg -> files/finances.xls\n    (currentPath != null &&\n      currentPath.endsWith(\"*\") &&\n      currentMatch.params[\"*\"] !== match.params[\"*\"])\n  );\n}\n\nfunction shouldRevalidateLoader(\n  loaderMatch: AgnosticDataRouteMatch,\n  arg: ShouldRevalidateFunctionArgs\n) {\n  if (loaderMatch.route.shouldRevalidate) {\n    let routeChoice = loaderMatch.route.shouldRevalidate(arg);\n    if (typeof routeChoice === \"boolean\") {\n      return routeChoice;\n    }\n  }\n\n  return arg.defaultShouldRevalidate;\n}\n\nfunction patchRoutesImpl(\n  routeId: string | null,\n  children: AgnosticRouteObject[],\n  routesToUse: AgnosticDataRouteObject[],\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction\n) {\n  let childrenToPatch: AgnosticDataRouteObject[];\n  if (routeId) {\n    let route = manifest[routeId];\n    invariant(\n      route,\n      `No route found to patch children into: routeId = ${routeId}`\n    );\n    if (!route.children) {\n      route.children = [];\n    }\n    childrenToPatch = route.children;\n  } else {\n    childrenToPatch = routesToUse;\n  }\n\n  // Don't patch in routes we already know about so that `patch` is idempotent\n  // to simplify user-land code. This is useful because we re-call the\n  // `patchRoutesOnNavigation` function for matched routes with params.\n  let uniqueChildren = children.filter(\n    (newRoute) =>\n      !childrenToPatch.some((existingRoute) =>\n        isSameRoute(newRoute, existingRoute)\n      )\n  );\n\n  let newRoutes = convertRoutesToDataRoutes(\n    uniqueChildren,\n    mapRouteProperties,\n    [routeId || \"_\", \"patch\", String(childrenToPatch?.length || \"0\")],\n    manifest\n  );\n\n  childrenToPatch.push(...newRoutes);\n}\n\nfunction isSameRoute(\n  newRoute: AgnosticRouteObject,\n  existingRoute: AgnosticRouteObject\n): boolean {\n  // Most optimal check is by id\n  if (\n    \"id\" in newRoute &&\n    \"id\" in existingRoute &&\n    newRoute.id === existingRoute.id\n  ) {\n    return true;\n  }\n\n  // Second is by pathing differences\n  if (\n    !(\n      newRoute.index === existingRoute.index &&\n      newRoute.path === existingRoute.path &&\n      newRoute.caseSensitive === existingRoute.caseSensitive\n    )\n  ) {\n    return false;\n  }\n\n  // Pathless layout routes are trickier since we need to check children.\n  // If they have no children then they're the same as far as we can tell\n  if (\n    (!newRoute.children || newRoute.children.length === 0) &&\n    (!existingRoute.children || existingRoute.children.length === 0)\n  ) {\n    return true;\n  }\n\n  // Otherwise, we look to see if every child in the new route is already\n  // represented in the existing route's children\n  return newRoute.children!.every((aChild, i) =>\n    existingRoute.children?.some((bChild) => isSameRoute(aChild, bChild))\n  );\n}\n\n/**\n * Execute route.lazy() methods to lazily load route modules (loader, action,\n * shouldRevalidate) and update the routeManifest in place which shares objects\n * with dataRoutes so those get updated as well.\n */\nasync function loadLazyRouteModule(\n  route: AgnosticDataRouteObject,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  manifest: RouteManifest\n) {\n  if (!route.lazy) {\n    return;\n  }\n\n  let lazyRoute = await route.lazy();\n\n  // If the lazy route function was executed and removed by another parallel\n  // call then we can return - first lazy() to finish wins because the return\n  // value of lazy is expected to be static\n  if (!route.lazy) {\n    return;\n  }\n\n  let routeToUpdate = manifest[route.id];\n  invariant(routeToUpdate, \"No route found in manifest\");\n\n  // Update the route in place.  This should be safe because there's no way\n  // we could yet be sitting on this route as we can't get there without\n  // resolving lazy() first.\n  //\n  // This is different than the HMR \"update\" use-case where we may actively be\n  // on the route being updated.  The main concern boils down to \"does this\n  // mutation affect any ongoing navigations or any current state.matches\n  // values?\".  If not, it should be safe to update in place.\n  let routeUpdates: Record<string, any> = {};\n  for (let lazyRouteProperty in lazyRoute) {\n    let staticRouteValue =\n      routeToUpdate[lazyRouteProperty as keyof typeof routeToUpdate];\n\n    let isPropertyStaticallyDefined =\n      staticRouteValue !== undefined &&\n      // This property isn't static since it should always be updated based\n      // on the route updates\n      lazyRouteProperty !== \"hasErrorBoundary\";\n\n    warning(\n      !isPropertyStaticallyDefined,\n      `Route \"${routeToUpdate.id}\" has a static property \"${lazyRouteProperty}\" ` +\n        `defined but its lazy function is also returning a value for this property. ` +\n        `The lazy route property \"${lazyRouteProperty}\" will be ignored.`\n    );\n\n    if (\n      !isPropertyStaticallyDefined &&\n      !immutableRouteKeys.has(lazyRouteProperty as ImmutableRouteKey)\n    ) {\n      routeUpdates[lazyRouteProperty] =\n        lazyRoute[lazyRouteProperty as keyof typeof lazyRoute];\n    }\n  }\n\n  // Mutate the route with the provided updates.  Do this first so we pass\n  // the updated version to mapRouteProperties\n  Object.assign(routeToUpdate, routeUpdates);\n\n  // Mutate the `hasErrorBoundary` property on the route based on the route\n  // updates and remove the `lazy` function so we don't resolve the lazy\n  // route again.\n  Object.assign(routeToUpdate, {\n    // To keep things framework agnostic, we use the provided\n    // `mapRouteProperties` (or wrapped `detectErrorBoundary`) function to\n    // set the framework-aware properties (`element`/`hasErrorBoundary`) since\n    // the logic will differ between frameworks.\n    ...mapRouteProperties(routeToUpdate),\n    lazy: undefined,\n  });\n}\n\n// Default implementation of `dataStrategy` which fetches all loaders in parallel\nasync function defaultDataStrategy({\n  matches,\n}: DataStrategyFunctionArgs): ReturnType<DataStrategyFunction> {\n  let matchesToLoad = matches.filter((m) => m.shouldLoad);\n  let results = await Promise.all(matchesToLoad.map((m) => m.resolve()));\n  return results.reduce(\n    (acc, result, i) =>\n      Object.assign(acc, { [matchesToLoad[i].route.id]: result }),\n    {}\n  );\n}\n\nasync function callDataStrategyImpl(\n  dataStrategyImpl: DataStrategyFunction,\n  type: \"loader\" | \"action\",\n  state: RouterState | null,\n  request: Request,\n  matchesToLoad: AgnosticDataRouteMatch[],\n  matches: AgnosticDataRouteMatch[],\n  fetcherKey: string | null,\n  manifest: RouteManifest,\n  mapRouteProperties: MapRoutePropertiesFunction,\n  requestContext?: unknown\n): Promise<Record<string, DataStrategyResult>> {\n  let loadRouteDefinitionsPromises = matches.map((m) =>\n    m.route.lazy\n      ? loadLazyRouteModule(m.route, mapRouteProperties, manifest)\n      : undefined\n  );\n\n  let dsMatches = matches.map((match, i) => {\n    let loadRoutePromise = loadRouteDefinitionsPromises[i];\n    let shouldLoad = matchesToLoad.some((m) => m.route.id === match.route.id);\n    // `resolve` encapsulates route.lazy(), executing the loader/action,\n    // and mapping return values/thrown errors to a `DataStrategyResult`.  Users\n    // can pass a callback to take fine-grained control over the execution\n    // of the loader/action\n    let resolve: DataStrategyMatch[\"resolve\"] = async (handlerOverride) => {\n      if (\n        handlerOverride &&\n        request.method === \"GET\" &&\n        (match.route.lazy || match.route.loader)\n      ) {\n        shouldLoad = true;\n      }\n      return shouldLoad\n        ? callLoaderOrAction(\n            type,\n            request,\n            match,\n            loadRoutePromise,\n            handlerOverride,\n            requestContext\n          )\n        : Promise.resolve({ type: ResultType.data, result: undefined });\n    };\n\n    return {\n      ...match,\n      shouldLoad,\n      resolve,\n    };\n  });\n\n  // Send all matches here to allow for a middleware-type implementation.\n  // handler will be a no-op for unneeded routes and we filter those results\n  // back out below.\n  let results = await dataStrategyImpl({\n    matches: dsMatches,\n    request,\n    params: matches[0].params,\n    fetcherKey,\n    context: requestContext,\n  });\n\n  // Wait for all routes to load here but 'swallow the error since we want\n  // it to bubble up from the `await loadRoutePromise` in `callLoaderOrAction` -\n  // called from `match.resolve()`\n  try {\n    await Promise.all(loadRouteDefinitionsPromises);\n  } catch (e) {\n    // No-op\n  }\n\n  return results;\n}\n\n// Default logic for calling a loader/action is the user has no specified a dataStrategy\nasync function callLoaderOrAction(\n  type: \"loader\" | \"action\",\n  request: Request,\n  match: AgnosticDataRouteMatch,\n  loadRoutePromise: Promise<void> | undefined,\n  handlerOverride: Parameters<DataStrategyMatch[\"resolve\"]>[0],\n  staticContext?: unknown\n): Promise<DataStrategyResult> {\n  let result: DataStrategyResult;\n  let onReject: (() => void) | undefined;\n\n  let runHandler = (\n    handler: AgnosticRouteObject[\"loader\"] | AgnosticRouteObject[\"action\"]\n  ): Promise<DataStrategyResult> => {\n    // Setup a promise we can race against so that abort signals short circuit\n    let reject: () => void;\n    // This will never resolve so safe to type it as Promise<DataStrategyResult> to\n    // satisfy the function return value\n    let abortPromise = new Promise<DataStrategyResult>((_, r) => (reject = r));\n    onReject = () => reject();\n    request.signal.addEventListener(\"abort\", onReject);\n\n    let actualHandler = (ctx?: unknown) => {\n      if (typeof handler !== \"function\") {\n        return Promise.reject(\n          new Error(\n            `You cannot call the handler for a route which defines a boolean ` +\n              `\"${type}\" [routeId: ${match.route.id}]`\n          )\n        );\n      }\n      return handler(\n        {\n          request,\n          params: match.params,\n          context: staticContext,\n        },\n        ...(ctx !== undefined ? [ctx] : [])\n      );\n    };\n\n    let handlerPromise: Promise<DataStrategyResult> = (async () => {\n      try {\n        let val = await (handlerOverride\n          ? handlerOverride((ctx: unknown) => actualHandler(ctx))\n          : actualHandler());\n        return { type: \"data\", result: val };\n      } catch (e) {\n        return { type: \"error\", result: e };\n      }\n    })();\n\n    return Promise.race([handlerPromise, abortPromise]);\n  };\n\n  try {\n    let handler = match.route[type];\n\n    // If we have a route.lazy promise, await that first\n    if (loadRoutePromise) {\n      if (handler) {\n        // Run statically defined handler in parallel with lazy()\n        let handlerError;\n        let [value] = await Promise.all([\n          // If the handler throws, don't let it immediately bubble out,\n          // since we need to let the lazy() execution finish so we know if this\n          // route has a boundary that can handle the error\n          runHandler(handler).catch((e) => {\n            handlerError = e;\n          }),\n          loadRoutePromise,\n        ]);\n        if (handlerError !== undefined) {\n          throw handlerError;\n        }\n        result = value!;\n      } else {\n        // Load lazy route module, then run any returned handler\n        await loadRoutePromise;\n\n        handler = match.route[type];\n        if (handler) {\n          // Handler still runs even if we got interrupted to maintain consistency\n          // with un-abortable behavior of handler execution on non-lazy or\n          // previously-lazy-loaded routes\n          result = await runHandler(handler);\n        } else if (type === \"action\") {\n          let url = new URL(request.url);\n          let pathname = url.pathname + url.search;\n          throw getInternalRouterError(405, {\n            method: request.method,\n            pathname,\n            routeId: match.route.id,\n          });\n        } else {\n          // lazy() route has no loader to run.  Short circuit here so we don't\n          // hit the invariant below that errors on returning undefined.\n          return { type: ResultType.data, result: undefined };\n        }\n      }\n    } else if (!handler) {\n      let url = new URL(request.url);\n      let pathname = url.pathname + url.search;\n      throw getInternalRouterError(404, {\n        pathname,\n      });\n    } else {\n      result = await runHandler(handler);\n    }\n\n    invariant(\n      result.result !== undefined,\n      `You defined ${type === \"action\" ? \"an action\" : \"a loader\"} for route ` +\n        `\"${match.route.id}\" but didn't return anything from your \\`${type}\\` ` +\n        `function. Please return a value or \\`null\\`.`\n    );\n  } catch (e) {\n    // We should already be catching and converting normal handler executions to\n    // DataStrategyResults and returning them, so anything that throws here is an\n    // unexpected error we still need to wrap\n    return { type: ResultType.error, result: e };\n  } finally {\n    if (onReject) {\n      request.signal.removeEventListener(\"abort\", onReject);\n    }\n  }\n\n  return result;\n}\n\nasync function convertDataStrategyResultToDataResult(\n  dataStrategyResult: DataStrategyResult\n): Promise<DataResult> {\n  let { result, type } = dataStrategyResult;\n\n  if (isResponse(result)) {\n    let data: any;\n\n    try {\n      let contentType = result.headers.get(\"Content-Type\");\n      // Check between word boundaries instead of startsWith() due to the last\n      // paragraph of https://httpwg.org/specs/rfc9110.html#field.content-type\n      if (contentType && /\\bapplication\\/json\\b/.test(contentType)) {\n        if (result.body == null) {\n          data = null;\n        } else {\n          data = await result.json();\n        }\n      } else {\n        data = await result.text();\n      }\n    } catch (e) {\n      return { type: ResultType.error, error: e };\n    }\n\n    if (type === ResultType.error) {\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(result.status, result.statusText, data),\n        statusCode: result.status,\n        headers: result.headers,\n      };\n    }\n\n    return {\n      type: ResultType.data,\n      data,\n      statusCode: result.status,\n      headers: result.headers,\n    };\n  }\n\n  if (type === ResultType.error) {\n    if (isDataWithResponseInit(result)) {\n      if (result.data instanceof Error) {\n        return {\n          type: ResultType.error,\n          error: result.data,\n          statusCode: result.init?.status,\n          headers: result.init?.headers\n            ? new Headers(result.init.headers)\n            : undefined,\n        };\n      }\n\n      // Convert thrown data() to ErrorResponse instances\n      return {\n        type: ResultType.error,\n        error: new ErrorResponseImpl(\n          result.init?.status || 500,\n          undefined,\n          result.data\n        ),\n        statusCode: isRouteErrorResponse(result) ? result.status : undefined,\n        headers: result.init?.headers\n          ? new Headers(result.init.headers)\n          : undefined,\n      };\n    }\n    return {\n      type: ResultType.error,\n      error: result,\n      statusCode: isRouteErrorResponse(result) ? result.status : undefined,\n    };\n  }\n\n  if (isDeferredData(result)) {\n    return {\n      type: ResultType.deferred,\n      deferredData: result,\n      statusCode: result.init?.status,\n      headers: result.init?.headers && new Headers(result.init.headers),\n    };\n  }\n\n  if (isDataWithResponseInit(result)) {\n    return {\n      type: ResultType.data,\n      data: result.data,\n      statusCode: result.init?.status,\n      headers: result.init?.headers\n        ? new Headers(result.init.headers)\n        : undefined,\n    };\n  }\n\n  return { type: ResultType.data, data: result };\n}\n\n// Support relative routing in internal redirects\nfunction normalizeRelativeRoutingRedirectResponse(\n  response: Response,\n  request: Request,\n  routeId: string,\n  matches: AgnosticDataRouteMatch[],\n  basename: string,\n  v7_relativeSplatPath: boolean\n) {\n  let location = response.headers.get(\"Location\");\n  invariant(\n    location,\n    \"Redirects returned/thrown from loaders/actions must have a Location header\"\n  );\n\n  if (!ABSOLUTE_URL_REGEX.test(location)) {\n    let trimmedMatches = matches.slice(\n      0,\n      matches.findIndex((m) => m.route.id === routeId) + 1\n    );\n    location = normalizeTo(\n      new URL(request.url),\n      trimmedMatches,\n      basename,\n      true,\n      location,\n      v7_relativeSplatPath\n    );\n    response.headers.set(\"Location\", location);\n  }\n\n  return response;\n}\n\nfunction normalizeRedirectLocation(\n  location: string,\n  currentUrl: URL,\n  basename: string\n): string {\n  if (ABSOLUTE_URL_REGEX.test(location)) {\n    // Strip off the protocol+origin for same-origin + same-basename absolute redirects\n    let normalizedLocation = location;\n    let url = normalizedLocation.startsWith(\"//\")\n      ? new URL(currentUrl.protocol + normalizedLocation)\n      : new URL(normalizedLocation);\n    let isSameBasename = stripBasename(url.pathname, basename) != null;\n    if (url.origin === currentUrl.origin && isSameBasename) {\n      return url.pathname + url.search + url.hash;\n    }\n  }\n  return location;\n}\n\n// Utility method for creating the Request instances for loaders/actions during\n// client-side navigations and fetches.  During SSR we will always have a\n// Request instance from the static handler (query/queryRoute)\nfunction createClientSideRequest(\n  history: History,\n  location: string | Location,\n  signal: AbortSignal,\n  submission?: Submission\n): Request {\n  let url = history.createURL(stripHashFromPath(location)).toString();\n  let init: RequestInit = { signal };\n\n  if (submission && isMutationMethod(submission.formMethod)) {\n    let { formMethod, formEncType } = submission;\n    // Didn't think we needed this but it turns out unlike other methods, patch\n    // won't be properly normalized to uppercase and results in a 405 error.\n    // See: https://fetch.spec.whatwg.org/#concept-method\n    init.method = formMethod.toUpperCase();\n\n    if (formEncType === \"application/json\") {\n      init.headers = new Headers({ \"Content-Type\": formEncType });\n      init.body = JSON.stringify(submission.json);\n    } else if (formEncType === \"text/plain\") {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.text;\n    } else if (\n      formEncType === \"application/x-www-form-urlencoded\" &&\n      submission.formData\n    ) {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = convertFormDataToSearchParams(submission.formData);\n    } else {\n      // Content-Type is inferred (https://fetch.spec.whatwg.org/#dom-request)\n      init.body = submission.formData;\n    }\n  }\n\n  return new Request(url, init);\n}\n\nfunction convertFormDataToSearchParams(formData: FormData): URLSearchParams {\n  let searchParams = new URLSearchParams();\n\n  for (let [key, value] of formData.entries()) {\n    // https://html.spec.whatwg.org/multipage/form-control-infrastructure.html#converting-an-entry-list-to-a-list-of-name-value-pairs\n    searchParams.append(key, typeof value === \"string\" ? value : value.name);\n  }\n\n  return searchParams;\n}\n\nfunction convertSearchParamsToFormData(\n  searchParams: URLSearchParams\n): FormData {\n  let formData = new FormData();\n  for (let [key, value] of searchParams.entries()) {\n    formData.append(key, value);\n  }\n  return formData;\n}\n\nfunction processRouteLoaderData(\n  matches: AgnosticDataRouteMatch[],\n  results: Record<string, DataResult>,\n  pendingActionResult: PendingActionResult | undefined,\n  activeDeferreds: Map<string, DeferredData>,\n  skipLoaderErrorBubbling: boolean\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors: RouterState[\"errors\"] | null;\n  statusCode: number;\n  loaderHeaders: Record<string, Headers>;\n} {\n  // Fill in loaderData/errors from our loaders\n  let loaderData: RouterState[\"loaderData\"] = {};\n  let errors: RouterState[\"errors\"] | null = null;\n  let statusCode: number | undefined;\n  let foundError = false;\n  let loaderHeaders: Record<string, Headers> = {};\n  let pendingError =\n    pendingActionResult && isErrorResult(pendingActionResult[1])\n      ? pendingActionResult[1].error\n      : undefined;\n\n  // Process loader results into state.loaderData/state.errors\n  matches.forEach((match) => {\n    if (!(match.route.id in results)) {\n      return;\n    }\n    let id = match.route.id;\n    let result = results[id];\n    invariant(\n      !isRedirectResult(result),\n      \"Cannot handle redirect results in processLoaderData\"\n    );\n    if (isErrorResult(result)) {\n      let error = result.error;\n      // If we have a pending action error, we report it at the highest-route\n      // that throws a loader error, and then clear it out to indicate that\n      // it was consumed\n      if (pendingError !== undefined) {\n        error = pendingError;\n        pendingError = undefined;\n      }\n\n      errors = errors || {};\n\n      if (skipLoaderErrorBubbling) {\n        errors[id] = error;\n      } else {\n        // Look upwards from the matched route for the closest ancestor error\n        // boundary, defaulting to the root match.  Prefer higher error values\n        // if lower errors bubble to the same boundary\n        let boundaryMatch = findNearestBoundary(matches, id);\n        if (errors[boundaryMatch.route.id] == null) {\n          errors[boundaryMatch.route.id] = error;\n        }\n      }\n\n      // Clear our any prior loaderData for the throwing route\n      loaderData[id] = undefined;\n\n      // Once we find our first (highest) error, we set the status code and\n      // prevent deeper status codes from overriding\n      if (!foundError) {\n        foundError = true;\n        statusCode = isRouteErrorResponse(result.error)\n          ? result.error.status\n          : 500;\n      }\n      if (result.headers) {\n        loaderHeaders[id] = result.headers;\n      }\n    } else {\n      if (isDeferredResult(result)) {\n        activeDeferreds.set(id, result.deferredData);\n        loaderData[id] = result.deferredData.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (\n          result.statusCode != null &&\n          result.statusCode !== 200 &&\n          !foundError\n        ) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      } else {\n        loaderData[id] = result.data;\n        // Error status codes always override success status codes, but if all\n        // loaders are successful we take the deepest status code.\n        if (result.statusCode && result.statusCode !== 200 && !foundError) {\n          statusCode = result.statusCode;\n        }\n        if (result.headers) {\n          loaderHeaders[id] = result.headers;\n        }\n      }\n    }\n  });\n\n  // If we didn't consume the pending action error (i.e., all loaders\n  // resolved), then consume it here.  Also clear out any loaderData for the\n  // throwing route\n  if (pendingError !== undefined && pendingActionResult) {\n    errors = { [pendingActionResult[0]]: pendingError };\n    loaderData[pendingActionResult[0]] = undefined;\n  }\n\n  return {\n    loaderData,\n    errors,\n    statusCode: statusCode || 200,\n    loaderHeaders,\n  };\n}\n\nfunction processLoaderData(\n  state: RouterState,\n  matches: AgnosticDataRouteMatch[],\n  results: Record<string, DataResult>,\n  pendingActionResult: PendingActionResult | undefined,\n  revalidatingFetchers: RevalidatingFetcher[],\n  fetcherResults: Record<string, DataResult>,\n  activeDeferreds: Map<string, DeferredData>\n): {\n  loaderData: RouterState[\"loaderData\"];\n  errors?: RouterState[\"errors\"];\n} {\n  let { loaderData, errors } = processRouteLoaderData(\n    matches,\n    results,\n    pendingActionResult,\n    activeDeferreds,\n    false // This method is only called client side so we always want to bubble\n  );\n\n  // Process results from our revalidating fetchers\n  revalidatingFetchers.forEach((rf) => {\n    let { key, match, controller } = rf;\n    let result = fetcherResults[key];\n    invariant(result, \"Did not find corresponding fetcher result\");\n\n    // Process fetcher non-redirect errors\n    if (controller && controller.signal.aborted) {\n      // Nothing to do for aborted fetchers\n      return;\n    } else if (isErrorResult(result)) {\n      let boundaryMatch = findNearestBoundary(state.matches, match?.route.id);\n      if (!(errors && errors[boundaryMatch.route.id])) {\n        errors = {\n          ...errors,\n          [boundaryMatch.route.id]: result.error,\n        };\n      }\n      state.fetchers.delete(key);\n    } else if (isRedirectResult(result)) {\n      // Should never get here, redirects should get processed above, but we\n      // keep this to type narrow to a success result in the else\n      invariant(false, \"Unhandled fetcher revalidation redirect\");\n    } else if (isDeferredResult(result)) {\n      // Should never get here, deferred data should be awaited for fetchers\n      // in resolveDeferredResults\n      invariant(false, \"Unhandled fetcher deferred data\");\n    } else {\n      let doneFetcher = getDoneFetcher(result.data);\n      state.fetchers.set(key, doneFetcher);\n    }\n  });\n\n  return { loaderData, errors };\n}\n\nfunction mergeLoaderData(\n  loaderData: RouteData,\n  newLoaderData: RouteData,\n  matches: AgnosticDataRouteMatch[],\n  errors: RouteData | null | undefined\n): RouteData {\n  let mergedLoaderData = { ...newLoaderData };\n  for (let match of matches) {\n    let id = match.route.id;\n    if (newLoaderData.hasOwnProperty(id)) {\n      if (newLoaderData[id] !== undefined) {\n        mergedLoaderData[id] = newLoaderData[id];\n      } else {\n        // No-op - this is so we ignore existing data if we have a key in the\n        // incoming object with an undefined value, which is how we unset a prior\n        // loaderData if we encounter a loader error\n      }\n    } else if (loaderData[id] !== undefined && match.route.loader) {\n      // Preserve existing keys not included in newLoaderData and where a loader\n      // wasn't removed by HMR\n      mergedLoaderData[id] = loaderData[id];\n    }\n\n    if (errors && errors.hasOwnProperty(id)) {\n      // Don't keep any loader data below the boundary\n      break;\n    }\n  }\n  return mergedLoaderData;\n}\n\nfunction getActionDataForCommit(\n  pendingActionResult: PendingActionResult | undefined\n) {\n  if (!pendingActionResult) {\n    return {};\n  }\n  return isErrorResult(pendingActionResult[1])\n    ? {\n        // Clear out prior actionData on errors\n        actionData: {},\n      }\n    : {\n        actionData: {\n          [pendingActionResult[0]]: pendingActionResult[1].data,\n        },\n      };\n}\n\n// Find the nearest error boundary, looking upwards from the leaf route (or the\n// route specified by routeId) for the closest ancestor error boundary,\n// defaulting to the root match\nfunction findNearestBoundary(\n  matches: AgnosticDataRouteMatch[],\n  routeId?: string\n): AgnosticDataRouteMatch {\n  let eligibleMatches = routeId\n    ? matches.slice(0, matches.findIndex((m) => m.route.id === routeId) + 1)\n    : [...matches];\n  return (\n    eligibleMatches.reverse().find((m) => m.route.hasErrorBoundary === true) ||\n    matches[0]\n  );\n}\n\nfunction getShortCircuitMatches(routes: AgnosticDataRouteObject[]): {\n  matches: AgnosticDataRouteMatch[];\n  route: AgnosticDataRouteObject;\n} {\n  // Prefer a root layout route if present, otherwise shim in a route object\n  let route =\n    routes.length === 1\n      ? routes[0]\n      : routes.find((r) => r.index || !r.path || r.path === \"/\") || {\n          id: `__shim-error-route__`,\n        };\n\n  return {\n    matches: [\n      {\n        params: {},\n        pathname: \"\",\n        pathnameBase: \"\",\n        route,\n      },\n    ],\n    route,\n  };\n}\n\nfunction getInternalRouterError(\n  status: number,\n  {\n    pathname,\n    routeId,\n    method,\n    type,\n    message,\n  }: {\n    pathname?: string;\n    routeId?: string;\n    method?: string;\n    type?: \"defer-action\" | \"invalid-body\";\n    message?: string;\n  } = {}\n) {\n  let statusText = \"Unknown Server Error\";\n  let errorMessage = \"Unknown @remix-run/router error\";\n\n  if (status === 400) {\n    statusText = \"Bad Request\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method} request to \"${pathname}\" but ` +\n        `did not provide a \\`loader\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (type === \"defer-action\") {\n      errorMessage = \"defer() is not supported in actions\";\n    } else if (type === \"invalid-body\") {\n      errorMessage = \"Unable to encode submission body\";\n    }\n  } else if (status === 403) {\n    statusText = \"Forbidden\";\n    errorMessage = `Route \"${routeId}\" does not match URL \"${pathname}\"`;\n  } else if (status === 404) {\n    statusText = \"Not Found\";\n    errorMessage = `No route matches URL \"${pathname}\"`;\n  } else if (status === 405) {\n    statusText = \"Method Not Allowed\";\n    if (method && pathname && routeId) {\n      errorMessage =\n        `You made a ${method.toUpperCase()} request to \"${pathname}\" but ` +\n        `did not provide an \\`action\\` for route \"${routeId}\", ` +\n        `so there is no way to handle the request.`;\n    } else if (method) {\n      errorMessage = `Invalid request method \"${method.toUpperCase()}\"`;\n    }\n  }\n\n  return new ErrorResponseImpl(\n    status || 500,\n    statusText,\n    new Error(errorMessage),\n    true\n  );\n}\n\n// Find any returned redirect errors, starting from the lowest match\nfunction findRedirect(\n  results: Record<string, DataResult>\n): { key: string; result: RedirectResult } | undefined {\n  let entries = Object.entries(results);\n  for (let i = entries.length - 1; i >= 0; i--) {\n    let [key, result] = entries[i];\n    if (isRedirectResult(result)) {\n      return { key, result };\n    }\n  }\n}\n\nfunction stripHashFromPath(path: To) {\n  let parsedPath = typeof path === \"string\" ? parsePath(path) : path;\n  return createPath({ ...parsedPath, hash: \"\" });\n}\n\nfunction isHashChangeOnly(a: Location, b: Location): boolean {\n  if (a.pathname !== b.pathname || a.search !== b.search) {\n    return false;\n  }\n\n  if (a.hash === \"\") {\n    // /page -> /page#hash\n    return b.hash !== \"\";\n  } else if (a.hash === b.hash) {\n    // /page#hash -> /page#hash\n    return true;\n  } else if (b.hash !== \"\") {\n    // /page#hash -> /page#other\n    return true;\n  }\n\n  // If the hash is removed the browser will re-perform a request to the server\n  // /page#hash -> /page\n  return false;\n}\n\nfunction isPromise<T = unknown>(val: unknown): val is Promise<T> {\n  return typeof val === \"object\" && val != null && \"then\" in val;\n}\n\nfunction isDataStrategyResult(result: unknown): result is DataStrategyResult {\n  return (\n    result != null &&\n    typeof result === \"object\" &&\n    \"type\" in result &&\n    \"result\" in result &&\n    (result.type === ResultType.data || result.type === ResultType.error)\n  );\n}\n\nfunction isRedirectDataStrategyResultResult(result: DataStrategyResult) {\n  return (\n    isResponse(result.result) && redirectStatusCodes.has(result.result.status)\n  );\n}\n\nfunction isDeferredResult(result: DataResult): result is DeferredResult {\n  return result.type === ResultType.deferred;\n}\n\nfunction isErrorResult(result: DataResult): result is ErrorResult {\n  return result.type === ResultType.error;\n}\n\nfunction isRedirectResult(result?: DataResult): result is RedirectResult {\n  return (result && result.type) === ResultType.redirect;\n}\n\nexport function isDataWithResponseInit(\n  value: any\n): value is DataWithResponseInit<unknown> {\n  return (\n    typeof value === \"object\" &&\n    value != null &&\n    \"type\" in value &&\n    \"data\" in value &&\n    \"init\" in value &&\n    value.type === \"DataWithResponseInit\"\n  );\n}\n\nexport function isDeferredData(value: any): value is DeferredData {\n  let deferred: DeferredData = value;\n  return (\n    deferred &&\n    typeof deferred === \"object\" &&\n    typeof deferred.data === \"object\" &&\n    typeof deferred.subscribe === \"function\" &&\n    typeof deferred.cancel === \"function\" &&\n    typeof deferred.resolveData === \"function\"\n  );\n}\n\nfunction isResponse(value: any): value is Response {\n  return (\n    value != null &&\n    typeof value.status === \"number\" &&\n    typeof value.statusText === \"string\" &&\n    typeof value.headers === \"object\" &&\n    typeof value.body !== \"undefined\"\n  );\n}\n\nfunction isRedirectResponse(result: any): result is Response {\n  if (!isResponse(result)) {\n    return false;\n  }\n\n  let status = result.status;\n  let location = result.headers.get(\"Location\");\n  return status >= 300 && status <= 399 && location != null;\n}\n\nfunction isValidMethod(method: string): method is FormMethod | V7_FormMethod {\n  return validRequestMethods.has(method.toLowerCase() as FormMethod);\n}\n\nfunction isMutationMethod(\n  method: string\n): method is MutationFormMethod | V7_MutationFormMethod {\n  return validMutationMethods.has(method.toLowerCase() as MutationFormMethod);\n}\n\nasync function resolveNavigationDeferredResults(\n  matches: (AgnosticDataRouteMatch | null)[],\n  results: Record<string, DataResult>,\n  signal: AbortSignal,\n  currentMatches: AgnosticDataRouteMatch[],\n  currentLoaderData: RouteData\n) {\n  let entries = Object.entries(results);\n  for (let index = 0; index < entries.length; index++) {\n    let [routeId, result] = entries[index];\n    let match = matches.find((m) => m?.route.id === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    let currentMatch = currentMatches.find(\n      (m) => m.route.id === match!.route.id\n    );\n    let isRevalidatingLoader =\n      currentMatch != null &&\n      !isNewRouteInstance(currentMatch, match) &&\n      (currentLoaderData && currentLoaderData[match.route.id]) !== undefined;\n\n    if (isDeferredResult(result) && isRevalidatingLoader) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      await resolveDeferredData(result, signal, false).then((result) => {\n        if (result) {\n          results[routeId] = result;\n        }\n      });\n    }\n  }\n}\n\nasync function resolveFetcherDeferredResults(\n  matches: (AgnosticDataRouteMatch | null)[],\n  results: Record<string, DataResult>,\n  revalidatingFetchers: RevalidatingFetcher[]\n) {\n  for (let index = 0; index < revalidatingFetchers.length; index++) {\n    let { key, routeId, controller } = revalidatingFetchers[index];\n    let result = results[key];\n    let match = matches.find((m) => m?.route.id === routeId);\n    // If we don't have a match, then we can have a deferred result to do\n    // anything with.  This is for revalidating fetchers where the route was\n    // removed during HMR\n    if (!match) {\n      continue;\n    }\n\n    if (isDeferredResult(result)) {\n      // Note: we do not have to touch activeDeferreds here since we race them\n      // against the signal in resolveDeferredData and they'll get aborted\n      // there if needed\n      invariant(\n        controller,\n        \"Expected an AbortController for revalidating fetcher deferred result\"\n      );\n      await resolveDeferredData(result, controller.signal, true).then(\n        (result) => {\n          if (result) {\n            results[key] = result;\n          }\n        }\n      );\n    }\n  }\n}\n\nasync function resolveDeferredData(\n  result: DeferredResult,\n  signal: AbortSignal,\n  unwrap = false\n): Promise<SuccessResult | ErrorResult | undefined> {\n  let aborted = await result.deferredData.resolveData(signal);\n  if (aborted) {\n    return;\n  }\n\n  if (unwrap) {\n    try {\n      return {\n        type: ResultType.data,\n        data: result.deferredData.unwrappedData,\n      };\n    } catch (e) {\n      // Handle any TrackedPromise._error values encountered while unwrapping\n      return {\n        type: ResultType.error,\n        error: e,\n      };\n    }\n  }\n\n  return {\n    type: ResultType.data,\n    data: result.deferredData.data,\n  };\n}\n\nfunction hasNakedIndexQuery(search: string): boolean {\n  return new URLSearchParams(search).getAll(\"index\").some((v) => v === \"\");\n}\n\nfunction getTargetMatch(\n  matches: AgnosticDataRouteMatch[],\n  location: Location | string\n) {\n  let search =\n    typeof location === \"string\" ? parsePath(location).search : location.search;\n  if (\n    matches[matches.length - 1].route.index &&\n    hasNakedIndexQuery(search || \"\")\n  ) {\n    // Return the leaf index route when index is present\n    return matches[matches.length - 1];\n  }\n  // Otherwise grab the deepest \"path contributing\" match (ignoring index and\n  // pathless layout routes)\n  let pathMatches = getPathContributingMatches(matches);\n  return pathMatches[pathMatches.length - 1];\n}\n\nfunction getSubmissionFromNavigation(\n  navigation: Navigation\n): Submission | undefined {\n  let { formMethod, formAction, formEncType, text, formData, json } =\n    navigation;\n  if (!formMethod || !formAction || !formEncType) {\n    return;\n  }\n\n  if (text != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json: undefined,\n      text,\n    };\n  } else if (formData != null) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData,\n      json: undefined,\n      text: undefined,\n    };\n  } else if (json !== undefined) {\n    return {\n      formMethod,\n      formAction,\n      formEncType,\n      formData: undefined,\n      json,\n      text: undefined,\n    };\n  }\n}\n\nfunction getLoadingNavigation(\n  location: Location,\n  submission?: Submission\n): NavigationStates[\"Loading\"] {\n  if (submission) {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n    };\n    return navigation;\n  } else {\n    let navigation: NavigationStates[\"Loading\"] = {\n      state: \"loading\",\n      location,\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n    };\n    return navigation;\n  }\n}\n\nfunction getSubmittingNavigation(\n  location: Location,\n  submission: Submission\n): NavigationStates[\"Submitting\"] {\n  let navigation: NavigationStates[\"Submitting\"] = {\n    state: \"submitting\",\n    location,\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n  };\n  return navigation;\n}\n\nfunction getLoadingFetcher(\n  submission?: Submission,\n  data?: Fetcher[\"data\"]\n): FetcherStates[\"Loading\"] {\n  if (submission) {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: submission.formMethod,\n      formAction: submission.formAction,\n      formEncType: submission.formEncType,\n      formData: submission.formData,\n      json: submission.json,\n      text: submission.text,\n      data,\n    };\n    return fetcher;\n  } else {\n    let fetcher: FetcherStates[\"Loading\"] = {\n      state: \"loading\",\n      formMethod: undefined,\n      formAction: undefined,\n      formEncType: undefined,\n      formData: undefined,\n      json: undefined,\n      text: undefined,\n      data,\n    };\n    return fetcher;\n  }\n}\n\nfunction getSubmittingFetcher(\n  submission: Submission,\n  existingFetcher?: Fetcher\n): FetcherStates[\"Submitting\"] {\n  let fetcher: FetcherStates[\"Submitting\"] = {\n    state: \"submitting\",\n    formMethod: submission.formMethod,\n    formAction: submission.formAction,\n    formEncType: submission.formEncType,\n    formData: submission.formData,\n    json: submission.json,\n    text: submission.text,\n    data: existingFetcher ? existingFetcher.data : undefined,\n  };\n  return fetcher;\n}\n\nfunction getDoneFetcher(data: Fetcher[\"data\"]): FetcherStates[\"Idle\"] {\n  let fetcher: FetcherStates[\"Idle\"] = {\n    state: \"idle\",\n    formMethod: undefined,\n    formAction: undefined,\n    formEncType: undefined,\n    formData: undefined,\n    json: undefined,\n    text: undefined,\n    data,\n  };\n  return fetcher;\n}\n\nfunction restoreAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  try {\n    let sessionPositions = _window.sessionStorage.getItem(\n      TRANSITIONS_STORAGE_KEY\n    );\n    if (sessionPositions) {\n      let json = JSON.parse(sessionPositions);\n      for (let [k, v] of Object.entries(json || {})) {\n        if (v && Array.isArray(v)) {\n          transitions.set(k, new Set(v || []));\n        }\n      }\n    }\n  } catch (e) {\n    // no-op, use default empty object\n  }\n}\n\nfunction persistAppliedTransitions(\n  _window: Window,\n  transitions: Map<string, Set<string>>\n) {\n  if (transitions.size > 0) {\n    let json: Record<string, string[]> = {};\n    for (let [k, v] of transitions) {\n      json[k] = [...v];\n    }\n    try {\n      _window.sessionStorage.setItem(\n        TRANSITIONS_STORAGE_KEY,\n        JSON.stringify(json)\n      );\n    } catch (error) {\n      warning(\n        false,\n        `Failed to save applied view transitions in sessionStorage (${error}).`\n      );\n    }\n  }\n}\n//#endregion\n"], "names": ["Action", "PopStateEventType", "createMemoryHistory", "options", "initialEntries", "initialIndex", "v5Compat", "entries", "map", "entry", "index", "createMemoryLocation", "state", "undefined", "clampIndex", "length", "action", "Pop", "listener", "n", "Math", "min", "max", "getCurrentLocation", "to", "key", "location", "createLocation", "pathname", "warning", "char<PERSON>t", "JSON", "stringify", "createHref", "createPath", "history", "createURL", "URL", "encodeLocation", "path", "parsePath", "search", "hash", "push", "<PERSON><PERSON>", "nextLocation", "splice", "delta", "replace", "Replace", "go", "nextIndex", "listen", "fn", "createBrowserHistory", "createBrowserLocation", "window", "globalHistory", "usr", "createBrowserHref", "getUrlBasedHistory", "createHashHistory", "createHashLocation", "substr", "startsWith", "createHashHref", "base", "document", "querySelector", "href", "getAttribute", "url", "hashIndex", "indexOf", "slice", "validateHashLocation", "invariant", "value", "message", "Error", "cond", "console", "warn", "e", "create<PERSON><PERSON>", "random", "toString", "getHistoryState", "idx", "current", "_extends", "_ref", "parsed<PERSON><PERSON>", "searchIndex", "getLocation", "validateLocation", "defaultView", "getIndex", "replaceState", "handlePop", "historyState", "pushState", "error", "DOMException", "name", "assign", "origin", "addEventListener", "removeEventListener", "ResultType", "immutableRouteKeys", "Set", "isIndexRoute", "route", "convertRoutesToDataRoutes", "routes", "mapRouteProperties", "parentPath", "manifest", "treePath", "String", "id", "join", "children", "indexRoute", "pathOrLayoutRoute", "matchRoutes", "locationArg", "basename", "matchRoutesImpl", "allowPartial", "stripBasename", "branches", "flattenRoutes", "rankRouteBranches", "matches", "i", "decoded", "decodePath", "matchRouteBranch", "convertRouteMatchToUiMatch", "match", "loaderData", "params", "data", "handle", "parents<PERSON>eta", "flattenRoute", "relativePath", "meta", "caseSensitive", "childrenIndex", "joinPaths", "routesMeta", "concat", "score", "computeScore", "for<PERSON>ach", "_route$path", "includes", "exploded", "explodeOptionalSegments", "segments", "split", "first", "rest", "isOptional", "endsWith", "required", "restExploded", "result", "subpath", "sort", "a", "b", "compareIndexes", "paramRe", "dynamicSegmentValue", "indexRouteValue", "emptySegmentValue", "staticSegmentValue", "splatPenalty", "isSplat", "s", "initialScore", "some", "filter", "reduce", "segment", "test", "siblings", "every", "branch", "matchedParams", "matchedPathname", "end", "remainingPathname", "matchPath", "Object", "pathnameBase", "normalizePathname", "generatePath", "originalPath", "prefix", "p", "array", "isLastSegment", "star", "keyMatch", "optional", "param", "pattern", "matcher", "compiledParams", "compilePath", "captureGroups", "memo", "paramName", "splatValue", "regexpSource", "_", "RegExp", "v", "decodeURIComponent", "toLowerCase", "startIndex", "nextChar", "<PERSON><PERSON><PERSON>", "fromPathname", "toPathname", "resolvePathname", "normalizeSearch", "normalizeHash", "relativeSegments", "pop", "getInvalidPathError", "char", "field", "dest", "getPathContributingMatches", "getResolveToMatches", "v7_relativeSplatPath", "pathMatches", "resolveTo", "to<PERSON><PERSON>", "routePathnames", "locationPathname", "isPathRelative", "isEmptyPath", "from", "routePathnameIndex", "toSegments", "shift", "hasExplicitTrailingSlash", "hasCurrentTrailingSlash", "getToPathname", "paths", "json", "init", "responseInit", "status", "headers", "Headers", "has", "set", "Response", "DataWithResponseInit", "constructor", "type", "Aborted<PERSON>eferredError", "DeferredData", "pendingKeysSet", "subscribers", "deferred<PERSON><PERSON><PERSON>", "Array", "isArray", "reject", "abortPromise", "Promise", "r", "controller", "AbortController", "onAbort", "unlistenAbortSignal", "signal", "acc", "_ref2", "trackPromise", "done", "add", "promise", "race", "then", "onSettle", "catch", "defineProperty", "get", "aborted", "delete", "undefinedError", "emit", "<PERSON><PERSON><PERSON>", "subscriber", "subscribe", "cancel", "abort", "k", "resolveData", "resolve", "size", "unwrappedData", "_ref3", "unwrapTrackedPromise", "<PERSON><PERSON><PERSON><PERSON>", "isTrackedPromise", "_tracked", "_error", "_data", "defer", "redirect", "redirectDocument", "response", "ErrorResponseImpl", "statusText", "internal", "isRouteErrorResponse", "validMutationMethodsArr", "validMutationMethods", "validRequestMethodsArr", "validRequestMethods", "redirectStatusCodes", "redirectPreserveMethodStatusCodes", "IDLE_NAVIGATION", "formMethod", "formAction", "formEncType", "formData", "text", "IDLE_FETCHER", "IDLE_BLOCKER", "proceed", "reset", "ABSOLUTE_URL_REGEX", "defaultMapRouteProperties", "hasErrorBou<PERSON>ry", "Boolean", "TRANSITIONS_STORAGE_KEY", "createRouter", "routerWindow", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "isServer", "detectErrorBoundary", "dataRoutes", "inFlightDataRoutes", "dataStrategyImpl", "dataStrategy", "defaultDataStrategy", "patchRoutesOnNavigationImpl", "patchRoutesOnNavigation", "future", "v7_fetcherPersist", "v7_normalizeFormMethod", "v7_partialHydration", "v7_prependBasename", "v7_skipActionErrorRevalidation", "unlistenHistory", "savedScrollPositions", "getScrollRestorationKey", "getScrollPosition", "initialScrollRestored", "hydrationData", "initialMatches", "initialMatchesIsFOW", "initialErrors", "getInternalRouterError", "getShortCircuitMatches", "fogOfWar", "checkFogOfWar", "active", "initialized", "m", "lazy", "loader", "errors", "findIndex", "shouldLoadRouteOnHydration", "router", "historyAction", "navigation", "restoreScrollPosition", "preventScrollReset", "revalidation", "actionData", "fetchers", "Map", "blockers", "pendingAction", "HistoryAction", "pendingPreventScrollReset", "pendingNavigationController", "pendingViewTransitionEnabled", "appliedViewTransitions", "removePageHideEventListener", "isUninterruptedRevalidation", "isRevalidationRequired", "cancelledDeferredRoutes", "cancelledFetcherLoads", "fetchControllers", "incrementingLoadId", "pendingNavigationLoadId", "fetchReloadIds", "fetchRedirectIds", "fetchLoadMatches", "activeFetchers", "deletedFetchers", "activeDeferreds", "blockerFunctions", "unblockBlockerHistoryUpdate", "initialize", "blockerKey", "shouldBlockNavigation", "currentLocation", "nextHistoryUpdatePromise", "updateBlocker", "updateState", "startNavigation", "restoreAppliedTransitions", "_saveAppliedTransitions", "persistAppliedTransitions", "initialHydration", "dispose", "clear", "deleteFetcher", "deleteBlocker", "newState", "opts", "completedFetchers", "deletedFetchersKeys", "fetcher", "viewTransitionOpts", "flushSync", "completeNavigation", "_temp", "_location$state", "_location$state2", "isActionReload", "isMutationMethod", "_isRedirect", "keys", "mergeLoaderData", "priorPaths", "toPaths", "getSavedScrollPosition", "navigate", "normalizedPath", "normalizeTo", "fromRouteId", "relative", "submission", "normalizeNavigateOptions", "userReplace", "pendingError", "enableViewTransition", "viewTransition", "revalidate", "interruptActiveLoads", "startUninterruptedRevalidation", "overrideNavigation", "saveScrollPosition", "routesToUse", "loadingNavigation", "isHashChangeOnly", "notFoundMatches", "handleNavigational404", "request", "createClientSideRequest", "pendingActionResult", "findNearestBoundary", "actionResult", "handleAction", "shortCircuited", "routeId", "isErrorResult", "getLoadingNavigation", "updatedMatches", "handleLoaders", "fetcherSubmission", "getActionDataForCommit", "isFogOfWar", "getSubmittingNavigation", "discoverResult", "discoverRoutes", "boundaryId", "partialMatch<PERSON>", "actionMatch", "getTargetMatch", "method", "results", "callDataStrategy", "isRedirectResult", "normalizeRedirectLocation", "startRedirectNavigation", "isDeferredResult", "boundaryMatch", "activeSubmission", "getSubmissionFromNavigation", "shouldUpdateNavigationState", "getUpdatedActionData", "matchesToLoad", "revalidatingFetchers", "getMatchesToLoad", "cancelActiveDeferreds", "updatedFetchers", "markFetchRedirectsDone", "updates", "getUpdatedRevalidatingFetchers", "rf", "abort<PERSON><PERSON><PERSON>", "abortPendingFetchRevalidations", "f", "loaderResults", "fetcherResults", "callLoadersAndMaybeResolveData", "findRedirect", "processLoaderData", "deferredData", "didAbortFetchLoads", "abortStaleFetchLoads", "shouldUpdateFetchers", "revalidatingFetcher", "getLoadingFetcher", "fetch", "setFetcherError", "handleFetcherAction", "handleFetcherLoader", "requestMatches", "detectAndHandle405Error", "existingFetcher", "updateFetcherState", "getSubmittingFetcher", "abortController", "fetchRequest", "originatingLoadId", "actionResults", "getDoneFetcher", "revalidationRequest", "loadId", "loadFetcher", "staleKey", "done<PERSON>etcher", "resolveDeferredData", "isNavigation", "_temp2", "redirectLocation", "isDocumentReload", "redirectHistoryAction", "fetcher<PERSON>ey", "dataResults", "callDataStrategyImpl", "isRedirectDataStrategyResultResult", "normalizeRelativeRoutingRedirectResponse", "convertDataStrategyResultToDataResult", "fetchersToLoad", "currentMatches", "loaderResultsPromise", "fetcherResultsPromise", "all", "resolveNavigationDeferredResults", "resolveFetcherDeferredResults", "getFetcher", "deleteFetcherAndUpdateState", "count", "markFetchersDone", "done<PERSON><PERSON><PERSON>", "landedId", "yeeted<PERSON><PERSON>s", "get<PERSON><PERSON>er", "blocker", "newBlocker", "blockerFunction", "predicate", "cancelledRouteIds", "dfd", "enableScrollRestoration", "positions", "getPosition", "<PERSON><PERSON><PERSON>", "y", "getScrollKey", "fogMatches", "isNonHMR", "localManifest", "patch", "patchRoutesImpl", "newMatches", "newPartialMatches", "_internalSetRoutes", "newRoutes", "patchRoutes", "_internalFetchControllers", "_internalActiveDeferreds", "UNSAFE_DEFERRED_SYMBOL", "Symbol", "createStaticHandler", "v7_throwAbortReason", "query", "_temp3", "requestContext", "skipL<PERSON>derError<PERSON><PERSON>bling", "isValidMethod", "methodNotAllowedMatches", "statusCode", "loaderHeaders", "actionHeaders", "queryImpl", "isResponse", "queryRoute", "_temp4", "find", "values", "_result$activeDeferre", "routeMatch", "submit", "loadRouteData", "isDataStrategyResult", "isRedirectResponse", "isRouteRequest", "throwStaticHandlerAbortedError", "Location", "loaderRequest", "Request", "context", "getLoaderMatchesUntilBoundary", "processRouteLoaderData", "executedLoaders", "fromEntries", "getStaticContextFromError", "newContext", "_deepestRenderedBoundaryId", "reason", "isSubmissionNavigation", "body", "prependBasename", "contextualMatches", "activeRouteMatch", "nakedIndex", "hasNakedIndexQuery", "URLSearchParams", "indexValues", "getAll", "append", "qs", "normalizeFormMethod", "isFetcher", "getInvalidBodyError", "rawFormMethod", "toUpperCase", "stripHashFromPath", "FormData", "parse", "searchParams", "convertFormDataToSearchParams", "convertSearchParamsToFormData", "includeBoundary", "skipActionErrorRevalidation", "currentUrl", "nextUrl", "boundaryMatches", "actionStatus", "shouldSkipRevalidation", "navigationMatches", "is<PERSON>ew<PERSON><PERSON>der", "currentRouteMatch", "nextRouteMatch", "shouldRevalidateLoader", "currentParams", "nextParams", "defaultShouldRevalidate", "isNewRouteInstance", "fetcherMatches", "fetcherMatch", "shouldRevalidate", "hasData", "<PERSON><PERSON><PERSON><PERSON>", "hydrate", "currentLoaderData", "currentMatch", "isNew", "isMissingData", "currentPath", "loaderMatch", "arg", "routeChoice", "_childrenToPatch", "childrenToPatch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newRoute", "existingRoute", "isSameRoute", "<PERSON><PERSON><PERSON><PERSON>", "_existingRoute$childr", "b<PERSON><PERSON><PERSON>", "loadLazyRouteModule", "lazyRoute", "routeToUpdate", "routeUpdates", "lazyRouteProperty", "staticRouteValue", "isPropertyStaticallyDefined", "_ref4", "shouldLoad", "loadRouteDefinitionsPromises", "dsMatches", "loadRoutePromise", "handlerOverride", "callLoaderOrAction", "staticContext", "onReject", "<PERSON><PERSON><PERSON><PERSON>", "handler", "<PERSON><PERSON><PERSON><PERSON>", "ctx", "handlerPromise", "val", "handlerError", "dataStrategyResult", "contentType", "isDataWithResponseInit", "_result$init3", "_result$init4", "_result$init", "_result$init2", "isDeferredData", "_result$init5", "_result$init6", "deferred", "_result$init7", "_result$init8", "trimmedMatches", "normalizedLocation", "protocol", "isSameBasename", "found<PERSON><PERSON>r", "newLoaderData", "mergedLoaderData", "hasOwnProperty", "eligibleMatches", "reverse", "_temp5", "errorMessage", "isRevalidatingLoader", "unwrap", "_window", "transitions", "sessionPositions", "sessionStorage", "getItem", "setItem"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAAA;EACA;EACA;;EAEA;EACA;EACA;AACYA,MAAAA,MAAM,0BAANA,MAAM,EAAA;IAANA,MAAM,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA;IAANA,MAAM,CAAA,MAAA,CAAA,GAAA,MAAA,CAAA;IAANA,MAAM,CAAA,SAAA,CAAA,GAAA,SAAA,CAAA;EAAA,EAAA,OAANA,MAAM,CAAA;EAAA,CAAA,CAAA,EAAA,EAAA;;EAwBlB;EACA;EACA;;EAkBA;EACA;EAEA;EACA;EACA;EACA;EAgBA;EACA;EACA;EAkBA;EACA;EACA;EAKA;EACA;EACA;EACA;EACA;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAgFA,MAAMC,iBAAiB,GAAG,UAAU,CAAA;EACpC;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EASA;EACA;EACA;EACA;EACA;EAQA;EACA;EACA;EACA;EACO,SAASC,mBAAmBA,CACjCC,OAA6B,EACd;EAAA,EAAA,IADfA,OAA6B,KAAA,KAAA,CAAA,EAAA;MAA7BA,OAA6B,GAAG,EAAE,CAAA;EAAA,GAAA;IAElC,IAAI;MAAEC,cAAc,GAAG,CAAC,GAAG,CAAC;MAAEC,YAAY;EAAEC,IAAAA,QAAQ,GAAG,KAAA;EAAM,GAAC,GAAGH,OAAO,CAAA;IACxE,IAAII,OAAmB,CAAC;EACxBA,EAAAA,OAAO,GAAGH,cAAc,CAACI,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,KACxCC,oBAAoB,CAClBF,KAAK,EACL,OAAOA,KAAK,KAAK,QAAQ,GAAG,IAAI,GAAGA,KAAK,CAACG,KAAK,EAC9CF,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGG,SAC5B,CACF,CAAC,CAAA;EACD,EAAA,IAAIH,KAAK,GAAGI,UAAU,CACpBT,YAAY,IAAI,IAAI,GAAGE,OAAO,CAACQ,MAAM,GAAG,CAAC,GAAGV,YAC9C,CAAC,CAAA;EACD,EAAA,IAAIW,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;IACvB,IAAIC,QAAyB,GAAG,IAAI,CAAA;IAEpC,SAASJ,UAAUA,CAACK,CAAS,EAAU;EACrC,IAAA,OAAOC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,CAAC,EAAE,CAAC,CAAC,EAAEZ,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,CAAA;EACrD,GAAA;IACA,SAASQ,kBAAkBA,GAAa;MACtC,OAAOhB,OAAO,CAACG,KAAK,CAAC,CAAA;EACvB,GAAA;EACA,EAAA,SAASC,oBAAoBA,CAC3Ba,EAAM,EACNZ,KAAU,EACVa,GAAY,EACF;EAAA,IAAA,IAFVb,KAAU,KAAA,KAAA,CAAA,EAAA;EAAVA,MAAAA,KAAU,GAAG,IAAI,CAAA;EAAA,KAAA;EAGjB,IAAA,IAAIc,QAAQ,GAAGC,cAAc,CAC3BpB,OAAO,GAAGgB,kBAAkB,EAAE,CAACK,QAAQ,GAAG,GAAG,EAC7CJ,EAAE,EACFZ,KAAK,EACLa,GACF,CAAC,CAAA;EACDI,IAAAA,OAAO,CACLH,QAAQ,CAACE,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,+DACwBC,IAAI,CAACC,SAAS,CACvER,EACF,CACF,CAAC,CAAA;EACD,IAAA,OAAOE,QAAQ,CAAA;EACjB,GAAA;IAEA,SAASO,UAAUA,CAACT,EAAM,EAAE;MAC1B,OAAO,OAAOA,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAA;EACrD,GAAA;EAEA,EAAA,IAAIW,OAAsB,GAAG;MAC3B,IAAIzB,KAAKA,GAAG;EACV,MAAA,OAAOA,KAAK,CAAA;OACb;MACD,IAAIM,MAAMA,GAAG;EACX,MAAA,OAAOA,MAAM,CAAA;OACd;MACD,IAAIU,QAAQA,GAAG;QACb,OAAOH,kBAAkB,EAAE,CAAA;OAC5B;MACDU,UAAU;MACVG,SAASA,CAACZ,EAAE,EAAE;QACZ,OAAO,IAAIa,GAAG,CAACJ,UAAU,CAACT,EAAE,CAAC,EAAE,kBAAkB,CAAC,CAAA;OACnD;MACDc,cAAcA,CAACd,EAAM,EAAE;EACrB,MAAA,IAAIe,IAAI,GAAG,OAAOf,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE,CAAA;QACtD,OAAO;EACLI,QAAAA,QAAQ,EAAEW,IAAI,CAACX,QAAQ,IAAI,EAAE;EAC7Ba,QAAAA,MAAM,EAAEF,IAAI,CAACE,MAAM,IAAI,EAAE;EACzBC,QAAAA,IAAI,EAAEH,IAAI,CAACG,IAAI,IAAI,EAAA;SACpB,CAAA;OACF;EACDC,IAAAA,IAAIA,CAACnB,EAAE,EAAEZ,KAAK,EAAE;QACdI,MAAM,GAAGhB,MAAM,CAAC4C,IAAI,CAAA;EACpB,MAAA,IAAIC,YAAY,GAAGlC,oBAAoB,CAACa,EAAE,EAAEZ,KAAK,CAAC,CAAA;EAClDF,MAAAA,KAAK,IAAI,CAAC,CAAA;QACVH,OAAO,CAACuC,MAAM,CAACpC,KAAK,EAAEH,OAAO,CAACQ,MAAM,EAAE8B,YAAY,CAAC,CAAA;QACnD,IAAIvC,QAAQ,IAAIY,QAAQ,EAAE;EACxBA,QAAAA,QAAQ,CAAC;YAAEF,MAAM;EAAEU,UAAAA,QAAQ,EAAEmB,YAAY;EAAEE,UAAAA,KAAK,EAAE,CAAA;EAAE,SAAC,CAAC,CAAA;EACxD,OAAA;OACD;EACDC,IAAAA,OAAOA,CAACxB,EAAE,EAAEZ,KAAK,EAAE;QACjBI,MAAM,GAAGhB,MAAM,CAACiD,OAAO,CAAA;EACvB,MAAA,IAAIJ,YAAY,GAAGlC,oBAAoB,CAACa,EAAE,EAAEZ,KAAK,CAAC,CAAA;EAClDL,MAAAA,OAAO,CAACG,KAAK,CAAC,GAAGmC,YAAY,CAAA;QAC7B,IAAIvC,QAAQ,IAAIY,QAAQ,EAAE;EACxBA,QAAAA,QAAQ,CAAC;YAAEF,MAAM;EAAEU,UAAAA,QAAQ,EAAEmB,YAAY;EAAEE,UAAAA,KAAK,EAAE,CAAA;EAAE,SAAC,CAAC,CAAA;EACxD,OAAA;OACD;MACDG,EAAEA,CAACH,KAAK,EAAE;QACR/B,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;EACnB,MAAA,IAAIkC,SAAS,GAAGrC,UAAU,CAACJ,KAAK,GAAGqC,KAAK,CAAC,CAAA;EACzC,MAAA,IAAIF,YAAY,GAAGtC,OAAO,CAAC4C,SAAS,CAAC,CAAA;EACrCzC,MAAAA,KAAK,GAAGyC,SAAS,CAAA;EACjB,MAAA,IAAIjC,QAAQ,EAAE;EACZA,QAAAA,QAAQ,CAAC;YAAEF,MAAM;EAAEU,UAAAA,QAAQ,EAAEmB,YAAY;EAAEE,UAAAA,KAAAA;EAAM,SAAC,CAAC,CAAA;EACrD,OAAA;OACD;MACDK,MAAMA,CAACC,EAAY,EAAE;EACnBnC,MAAAA,QAAQ,GAAGmC,EAAE,CAAA;EACb,MAAA,OAAO,MAAM;EACXnC,QAAAA,QAAQ,GAAG,IAAI,CAAA;SAChB,CAAA;EACH,KAAA;KACD,CAAA;EAED,EAAA,OAAOiB,OAAO,CAAA;EAChB,CAAA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EAKA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASmB,oBAAoBA,CAClCnD,OAA8B,EACd;EAAA,EAAA,IADhBA,OAA8B,KAAA,KAAA,CAAA,EAAA;MAA9BA,OAA8B,GAAG,EAAE,CAAA;EAAA,GAAA;EAEnC,EAAA,SAASoD,qBAAqBA,CAC5BC,MAAc,EACdC,aAAgC,EAChC;MACA,IAAI;QAAE7B,QAAQ;QAAEa,MAAM;EAAEC,MAAAA,IAAAA;OAAM,GAAGc,MAAM,CAAC9B,QAAQ,CAAA;MAChD,OAAOC,cAAc,CACnB,EAAE,EACF;QAAEC,QAAQ;QAAEa,MAAM;EAAEC,MAAAA,IAAAA;OAAM;EAC1B;MACCe,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAAC8C,GAAG,IAAK,IAAI,EACvDD,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAACa,GAAG,IAAK,SACtD,CAAC,CAAA;EACH,GAAA;EAEA,EAAA,SAASkC,iBAAiBA,CAACH,MAAc,EAAEhC,EAAM,EAAE;MACjD,OAAO,OAAOA,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAA;EACrD,GAAA;IAEA,OAAOoC,kBAAkB,CACvBL,qBAAqB,EACrBI,iBAAiB,EACjB,IAAI,EACJxD,OACF,CAAC,CAAA;EACH,CAAA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAKA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS0D,iBAAiBA,CAC/B1D,OAA2B,EACd;EAAA,EAAA,IADbA,OAA2B,KAAA,KAAA,CAAA,EAAA;MAA3BA,OAA2B,GAAG,EAAE,CAAA;EAAA,GAAA;EAEhC,EAAA,SAAS2D,kBAAkBA,CACzBN,MAAc,EACdC,aAAgC,EAChC;MACA,IAAI;EACF7B,MAAAA,QAAQ,GAAG,GAAG;EACda,MAAAA,MAAM,GAAG,EAAE;EACXC,MAAAA,IAAI,GAAG,EAAA;EACT,KAAC,GAAGF,SAAS,CAACgB,MAAM,CAAC9B,QAAQ,CAACgB,IAAI,CAACqB,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;;EAE7C;EACA;EACA;EACA;EACA;EACA;EACA,IAAA,IAAI,CAACnC,QAAQ,CAACoC,UAAU,CAAC,GAAG,CAAC,IAAI,CAACpC,QAAQ,CAACoC,UAAU,CAAC,GAAG,CAAC,EAAE;QAC1DpC,QAAQ,GAAG,GAAG,GAAGA,QAAQ,CAAA;EAC3B,KAAA;MAEA,OAAOD,cAAc,CACnB,EAAE,EACF;QAAEC,QAAQ;QAAEa,MAAM;EAAEC,MAAAA,IAAAA;OAAM;EAC1B;MACCe,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAAC8C,GAAG,IAAK,IAAI,EACvDD,aAAa,CAAC7C,KAAK,IAAI6C,aAAa,CAAC7C,KAAK,CAACa,GAAG,IAAK,SACtD,CAAC,CAAA;EACH,GAAA;EAEA,EAAA,SAASwC,cAAcA,CAACT,MAAc,EAAEhC,EAAM,EAAE;MAC9C,IAAI0C,IAAI,GAAGV,MAAM,CAACW,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAAA;MAChD,IAAIC,IAAI,GAAG,EAAE,CAAA;MAEb,IAAIH,IAAI,IAAIA,IAAI,CAACI,YAAY,CAAC,MAAM,CAAC,EAAE;EACrC,MAAA,IAAIC,GAAG,GAAGf,MAAM,CAAC9B,QAAQ,CAAC2C,IAAI,CAAA;EAC9B,MAAA,IAAIG,SAAS,GAAGD,GAAG,CAACE,OAAO,CAAC,GAAG,CAAC,CAAA;EAChCJ,MAAAA,IAAI,GAAGG,SAAS,KAAK,CAAC,CAAC,GAAGD,GAAG,GAAGA,GAAG,CAACG,KAAK,CAAC,CAAC,EAAEF,SAAS,CAAC,CAAA;EACzD,KAAA;EAEA,IAAA,OAAOH,IAAI,GAAG,GAAG,IAAI,OAAO7C,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAC,CAAA;EACpE,GAAA;EAEA,EAAA,SAASmD,oBAAoBA,CAACjD,QAAkB,EAAEF,EAAM,EAAE;EACxDK,IAAAA,OAAO,CACLH,QAAQ,CAACE,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAA,4DAAA,GAC0BC,IAAI,CAACC,SAAS,CACzER,EACF,CAAC,MACH,CAAC,CAAA;EACH,GAAA;IAEA,OAAOoC,kBAAkB,CACvBE,kBAAkB,EAClBG,cAAc,EACdU,oBAAoB,EACpBxE,OACF,CAAC,CAAA;EACH,CAAA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EAMO,SAASyE,SAASA,CAACC,KAAU,EAAEC,OAAgB,EAAE;EACtD,EAAA,IAAID,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;EACrE,IAAA,MAAM,IAAIE,KAAK,CAACD,OAAO,CAAC,CAAA;EAC1B,GAAA;EACF,CAAA;EAEO,SAASjD,OAAOA,CAACmD,IAAS,EAAEF,OAAe,EAAE;IAClD,IAAI,CAACE,IAAI,EAAE;EACT;MACA,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAEA,OAAO,CAACC,IAAI,CAACJ,OAAO,CAAC,CAAA;MAEzD,IAAI;EACF;EACA;EACA;EACA;EACA;EACA,MAAA,MAAM,IAAIC,KAAK,CAACD,OAAO,CAAC,CAAA;EACxB;EACF,KAAC,CAAC,OAAOK,CAAC,EAAE,EAAC;EACf,GAAA;EACF,CAAA;EAEA,SAASC,SAASA,GAAG;EACnB,EAAA,OAAOhE,IAAI,CAACiE,MAAM,EAAE,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACvB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;EAChD,CAAA;;EAEA;EACA;EACA;EACA,SAASwB,eAAeA,CAAC7D,QAAkB,EAAEhB,KAAa,EAAgB;IACxE,OAAO;MACLgD,GAAG,EAAEhC,QAAQ,CAACd,KAAK;MACnBa,GAAG,EAAEC,QAAQ,CAACD,GAAG;EACjB+D,IAAAA,GAAG,EAAE9E,KAAAA;KACN,CAAA;EACH,CAAA;;EAEA;EACA;EACA;EACO,SAASiB,cAAcA,CAC5B8D,OAA0B,EAC1BjE,EAAM,EACNZ,KAAU,EACVa,GAAY,EACQ;EAAA,EAAA,IAFpBb,KAAU,KAAA,KAAA,CAAA,EAAA;EAAVA,IAAAA,KAAU,GAAG,IAAI,CAAA;EAAA,GAAA;IAGjB,IAAIc,QAA4B,GAAAgE,QAAA,CAAA;MAC9B9D,QAAQ,EAAE,OAAO6D,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGA,OAAO,CAAC7D,QAAQ;EAClEa,IAAAA,MAAM,EAAE,EAAE;EACVC,IAAAA,IAAI,EAAE,EAAA;KACF,EAAA,OAAOlB,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE,EAAA;MAC/CZ,KAAK;EACL;EACA;EACA;EACA;MACAa,GAAG,EAAGD,EAAE,IAAKA,EAAE,CAAcC,GAAG,IAAKA,GAAG,IAAI2D,SAAS,EAAC;KACvD,CAAA,CAAA;EACD,EAAA,OAAO1D,QAAQ,CAAA;EACjB,CAAA;;EAEA;EACA;EACA;EACO,SAASQ,UAAUA,CAAAyD,IAAA,EAIR;IAAA,IAJS;EACzB/D,IAAAA,QAAQ,GAAG,GAAG;EACda,IAAAA,MAAM,GAAG,EAAE;EACXC,IAAAA,IAAI,GAAG,EAAA;EACM,GAAC,GAAAiD,IAAA,CAAA;IACd,IAAIlD,MAAM,IAAIA,MAAM,KAAK,GAAG,EAC1Bb,QAAQ,IAAIa,MAAM,CAACX,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGW,MAAM,GAAG,GAAG,GAAGA,MAAM,CAAA;IAC9D,IAAIC,IAAI,IAAIA,IAAI,KAAK,GAAG,EACtBd,QAAQ,IAAIc,IAAI,CAACZ,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGY,IAAI,GAAG,GAAG,GAAGA,IAAI,CAAA;EACxD,EAAA,OAAOd,QAAQ,CAAA;EACjB,CAAA;;EAEA;EACA;EACA;EACO,SAASY,SAASA,CAACD,IAAY,EAAiB;IACrD,IAAIqD,UAAyB,GAAG,EAAE,CAAA;EAElC,EAAA,IAAIrD,IAAI,EAAE;EACR,IAAA,IAAIiC,SAAS,GAAGjC,IAAI,CAACkC,OAAO,CAAC,GAAG,CAAC,CAAA;MACjC,IAAID,SAAS,IAAI,CAAC,EAAE;QAClBoB,UAAU,CAAClD,IAAI,GAAGH,IAAI,CAACwB,MAAM,CAACS,SAAS,CAAC,CAAA;QACxCjC,IAAI,GAAGA,IAAI,CAACwB,MAAM,CAAC,CAAC,EAAES,SAAS,CAAC,CAAA;EAClC,KAAA;EAEA,IAAA,IAAIqB,WAAW,GAAGtD,IAAI,CAACkC,OAAO,CAAC,GAAG,CAAC,CAAA;MACnC,IAAIoB,WAAW,IAAI,CAAC,EAAE;QACpBD,UAAU,CAACnD,MAAM,GAAGF,IAAI,CAACwB,MAAM,CAAC8B,WAAW,CAAC,CAAA;QAC5CtD,IAAI,GAAGA,IAAI,CAACwB,MAAM,CAAC,CAAC,EAAE8B,WAAW,CAAC,CAAA;EACpC,KAAA;EAEA,IAAA,IAAItD,IAAI,EAAE;QACRqD,UAAU,CAAChE,QAAQ,GAAGW,IAAI,CAAA;EAC5B,KAAA;EACF,GAAA;EAEA,EAAA,OAAOqD,UAAU,CAAA;EACnB,CAAA;EASA,SAAShC,kBAAkBA,CACzBkC,WAA2E,EAC3E7D,UAA8C,EAC9C8D,gBAA+D,EAC/D5F,OAA0B,EACd;EAAA,EAAA,IADZA,OAA0B,KAAA,KAAA,CAAA,EAAA;MAA1BA,OAA0B,GAAG,EAAE,CAAA;EAAA,GAAA;IAE/B,IAAI;MAAEqD,MAAM,GAAGW,QAAQ,CAAC6B,WAAY;EAAE1F,IAAAA,QAAQ,GAAG,KAAA;EAAM,GAAC,GAAGH,OAAO,CAAA;EAClE,EAAA,IAAIsD,aAAa,GAAGD,MAAM,CAACrB,OAAO,CAAA;EAClC,EAAA,IAAInB,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;IACvB,IAAIC,QAAyB,GAAG,IAAI,CAAA;EAEpC,EAAA,IAAIR,KAAK,GAAGuF,QAAQ,EAAG,CAAA;EACvB;EACA;EACA;IACA,IAAIvF,KAAK,IAAI,IAAI,EAAE;EACjBA,IAAAA,KAAK,GAAG,CAAC,CAAA;EACT+C,IAAAA,aAAa,CAACyC,YAAY,CAAAR,QAAA,CAAMjC,EAAAA,EAAAA,aAAa,CAAC7C,KAAK,EAAA;EAAE4E,MAAAA,GAAG,EAAE9E,KAAAA;EAAK,KAAA,CAAA,EAAI,EAAE,CAAC,CAAA;EACxE,GAAA;IAEA,SAASuF,QAAQA,GAAW;EAC1B,IAAA,IAAIrF,KAAK,GAAG6C,aAAa,CAAC7C,KAAK,IAAI;EAAE4E,MAAAA,GAAG,EAAE,IAAA;OAAM,CAAA;MAChD,OAAO5E,KAAK,CAAC4E,GAAG,CAAA;EAClB,GAAA;IAEA,SAASW,SAASA,GAAG;MACnBnF,MAAM,GAAGhB,MAAM,CAACiB,GAAG,CAAA;EACnB,IAAA,IAAIkC,SAAS,GAAG8C,QAAQ,EAAE,CAAA;MAC1B,IAAIlD,KAAK,GAAGI,SAAS,IAAI,IAAI,GAAG,IAAI,GAAGA,SAAS,GAAGzC,KAAK,CAAA;EACxDA,IAAAA,KAAK,GAAGyC,SAAS,CAAA;EACjB,IAAA,IAAIjC,QAAQ,EAAE;EACZA,MAAAA,QAAQ,CAAC;UAAEF,MAAM;UAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;EAAEqB,QAAAA,KAAAA;EAAM,OAAC,CAAC,CAAA;EACzD,KAAA;EACF,GAAA;EAEA,EAAA,SAASJ,IAAIA,CAACnB,EAAM,EAAEZ,KAAW,EAAE;MACjCI,MAAM,GAAGhB,MAAM,CAAC4C,IAAI,CAAA;MACpB,IAAIlB,QAAQ,GAAGC,cAAc,CAACQ,OAAO,CAACT,QAAQ,EAAEF,EAAE,EAAEZ,KAAK,CAAC,CAAA;EAC1D,IAAA,IAAImF,gBAAgB,EAAEA,gBAAgB,CAACrE,QAAQ,EAAEF,EAAE,CAAC,CAAA;EAEpDd,IAAAA,KAAK,GAAGuF,QAAQ,EAAE,GAAG,CAAC,CAAA;EACtB,IAAA,IAAIG,YAAY,GAAGb,eAAe,CAAC7D,QAAQ,EAAEhB,KAAK,CAAC,CAAA;EACnD,IAAA,IAAI6D,GAAG,GAAGpC,OAAO,CAACF,UAAU,CAACP,QAAQ,CAAC,CAAA;;EAEtC;MACA,IAAI;QACF+B,aAAa,CAAC4C,SAAS,CAACD,YAAY,EAAE,EAAE,EAAE7B,GAAG,CAAC,CAAA;OAC/C,CAAC,OAAO+B,KAAK,EAAE;EACd;EACA;EACA;EACA;QACA,IAAIA,KAAK,YAAYC,YAAY,IAAID,KAAK,CAACE,IAAI,KAAK,gBAAgB,EAAE;EACpE,QAAA,MAAMF,KAAK,CAAA;EACb,OAAA;EACA;EACA;EACA9C,MAAAA,MAAM,CAAC9B,QAAQ,CAAC+E,MAAM,CAAClC,GAAG,CAAC,CAAA;EAC7B,KAAA;MAEA,IAAIjE,QAAQ,IAAIY,QAAQ,EAAE;EACxBA,MAAAA,QAAQ,CAAC;UAAEF,MAAM;UAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;EAAEqB,QAAAA,KAAK,EAAE,CAAA;EAAE,OAAC,CAAC,CAAA;EAC5D,KAAA;EACF,GAAA;EAEA,EAAA,SAASC,OAAOA,CAACxB,EAAM,EAAEZ,KAAW,EAAE;MACpCI,MAAM,GAAGhB,MAAM,CAACiD,OAAO,CAAA;MACvB,IAAIvB,QAAQ,GAAGC,cAAc,CAACQ,OAAO,CAACT,QAAQ,EAAEF,EAAE,EAAEZ,KAAK,CAAC,CAAA;EAC1D,IAAA,IAAImF,gBAAgB,EAAEA,gBAAgB,CAACrE,QAAQ,EAAEF,EAAE,CAAC,CAAA;MAEpDd,KAAK,GAAGuF,QAAQ,EAAE,CAAA;EAClB,IAAA,IAAIG,YAAY,GAAGb,eAAe,CAAC7D,QAAQ,EAAEhB,KAAK,CAAC,CAAA;EACnD,IAAA,IAAI6D,GAAG,GAAGpC,OAAO,CAACF,UAAU,CAACP,QAAQ,CAAC,CAAA;MACtC+B,aAAa,CAACyC,YAAY,CAACE,YAAY,EAAE,EAAE,EAAE7B,GAAG,CAAC,CAAA;MAEjD,IAAIjE,QAAQ,IAAIY,QAAQ,EAAE;EACxBA,MAAAA,QAAQ,CAAC;UAAEF,MAAM;UAAEU,QAAQ,EAAES,OAAO,CAACT,QAAQ;EAAEqB,QAAAA,KAAK,EAAE,CAAA;EAAE,OAAC,CAAC,CAAA;EAC5D,KAAA;EACF,GAAA;IAEA,SAASX,SAASA,CAACZ,EAAM,EAAO;EAC9B;EACA;EACA;MACA,IAAI0C,IAAI,GACNV,MAAM,CAAC9B,QAAQ,CAACgF,MAAM,KAAK,MAAM,GAC7BlD,MAAM,CAAC9B,QAAQ,CAACgF,MAAM,GACtBlD,MAAM,CAAC9B,QAAQ,CAAC2C,IAAI,CAAA;EAE1B,IAAA,IAAIA,IAAI,GAAG,OAAO7C,EAAE,KAAK,QAAQ,GAAGA,EAAE,GAAGU,UAAU,CAACV,EAAE,CAAC,CAAA;EACvD;EACA;EACA;MACA6C,IAAI,GAAGA,IAAI,CAACrB,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;EAChC4B,IAAAA,SAAS,CACPV,IAAI,EACkEG,qEAAAA,GAAAA,IACxE,CAAC,CAAA;EACD,IAAA,OAAO,IAAIhC,GAAG,CAACgC,IAAI,EAAEH,IAAI,CAAC,CAAA;EAC5B,GAAA;EAEA,EAAA,IAAI/B,OAAgB,GAAG;MACrB,IAAInB,MAAMA,GAAG;EACX,MAAA,OAAOA,MAAM,CAAA;OACd;MACD,IAAIU,QAAQA,GAAG;EACb,MAAA,OAAOoE,WAAW,CAACtC,MAAM,EAAEC,aAAa,CAAC,CAAA;OAC1C;MACDL,MAAMA,CAACC,EAAY,EAAE;EACnB,MAAA,IAAInC,QAAQ,EAAE;EACZ,QAAA,MAAM,IAAI6D,KAAK,CAAC,4CAA4C,CAAC,CAAA;EAC/D,OAAA;EACAvB,MAAAA,MAAM,CAACmD,gBAAgB,CAAC1G,iBAAiB,EAAEkG,SAAS,CAAC,CAAA;EACrDjF,MAAAA,QAAQ,GAAGmC,EAAE,CAAA;EAEb,MAAA,OAAO,MAAM;EACXG,QAAAA,MAAM,CAACoD,mBAAmB,CAAC3G,iBAAiB,EAAEkG,SAAS,CAAC,CAAA;EACxDjF,QAAAA,QAAQ,GAAG,IAAI,CAAA;SAChB,CAAA;OACF;MACDe,UAAUA,CAACT,EAAE,EAAE;EACb,MAAA,OAAOS,UAAU,CAACuB,MAAM,EAAEhC,EAAE,CAAC,CAAA;OAC9B;MACDY,SAAS;MACTE,cAAcA,CAACd,EAAE,EAAE;EACjB;EACA,MAAA,IAAI+C,GAAG,GAAGnC,SAAS,CAACZ,EAAE,CAAC,CAAA;QACvB,OAAO;UACLI,QAAQ,EAAE2C,GAAG,CAAC3C,QAAQ;UACtBa,MAAM,EAAE8B,GAAG,CAAC9B,MAAM;UAClBC,IAAI,EAAE6B,GAAG,CAAC7B,IAAAA;SACX,CAAA;OACF;MACDC,IAAI;MACJK,OAAO;MACPE,EAAEA,CAAC/B,CAAC,EAAE;EACJ,MAAA,OAAOsC,aAAa,CAACP,EAAE,CAAC/B,CAAC,CAAC,CAAA;EAC5B,KAAA;KACD,CAAA;EAED,EAAA,OAAOgB,OAAO,CAAA;EAChB,CAAA;;EAEA;;ECtuBA;EACA;EACA;;EAKY0E,IAAAA,UAAU,0BAAVA,UAAU,EAAA;IAAVA,UAAU,CAAA,MAAA,CAAA,GAAA,MAAA,CAAA;IAAVA,UAAU,CAAA,UAAA,CAAA,GAAA,UAAA,CAAA;IAAVA,UAAU,CAAA,UAAA,CAAA,GAAA,UAAA,CAAA;IAAVA,UAAU,CAAA,OAAA,CAAA,GAAA,OAAA,CAAA;EAAA,EAAA,OAAVA,UAAU,CAAA;EAAA,CAAA,CAAA,EAAA,CAAA,CAAA;;EAOtB;EACA;EACA;;EAQA;EACA;EACA;;EAQA;EACA;EACA;;EAOA;EACA;EACA;;EAQA;EACA;EACA;;EAUA;EACA;EACA;EACA;;EAGA;EACA;EACA;EACA;;EAIA;EACA;EACA;EACA;;EAUA;;EAQA;EACA;EACA;EACA;EACA;;EA2BA;EACA;EACA;EACA;EACA;;EAOA;EACA;EACA;EAEA;EACA;EACA;EAIA;EACA;EACA;EAIA;EACA;EACA;EACA;EACA;EAKA;EACA;EACA;EAQA;EACA;EACA;EAQA;EACA;EACA;EAiBA;EACA;EACA;EACA;EACA;EACA;EACA;EAKA;EACA;EACA;EACA;EACA;EACA;EAqBA;EACA;EACA;EA4BA;EACA;EACA;EACA;EAOA;EACA;EACA;EACA;EACA;EASO,MAAMC,kBAAkB,GAAG,IAAIC,GAAG,CAAoB,CAC3D,MAAM,EACN,eAAe,EACf,MAAM,EACN,IAAI,EACJ,OAAO,EACP,UAAU,CACX,CAAC,CAAA;;EASF;EACA;EACA;EACA;;EAKA;EACA;EACA;;EAaA;EACA;EACA;;EAMA;EACA;EACA;;EAMA;EACA;EACA;EACA;;EAcA;EACA;EACA;;EAOA;;EAaA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAWA;EACA;EACA;EAKA;EACA;EACA;EAKA;EACA;EACA;EA0BA,SAASC,YAAYA,CACnBC,KAA0B,EACS;EACnC,EAAA,OAAOA,KAAK,CAACvG,KAAK,KAAK,IAAI,CAAA;EAC7B,CAAA;;EAEA;EACA;EACO,SAASwG,yBAAyBA,CACvCC,MAA6B,EAC7BC,kBAA8C,EAC9CC,UAAoB,EACpBC,QAAuB,EACI;EAAA,EAAA,IAF3BD,UAAoB,KAAA,KAAA,CAAA,EAAA;EAApBA,IAAAA,UAAoB,GAAG,EAAE,CAAA;EAAA,GAAA;EAAA,EAAA,IACzBC,QAAuB,KAAA,KAAA,CAAA,EAAA;MAAvBA,QAAuB,GAAG,EAAE,CAAA;EAAA,GAAA;IAE5B,OAAOH,MAAM,CAAC3G,GAAG,CAAC,CAACyG,KAAK,EAAEvG,KAAK,KAAK;MAClC,IAAI6G,QAAQ,GAAG,CAAC,GAAGF,UAAU,EAAEG,MAAM,CAAC9G,KAAK,CAAC,CAAC,CAAA;EAC7C,IAAA,IAAI+G,EAAE,GAAG,OAAOR,KAAK,CAACQ,EAAE,KAAK,QAAQ,GAAGR,KAAK,CAACQ,EAAE,GAAGF,QAAQ,CAACG,IAAI,CAAC,GAAG,CAAC,CAAA;EACrE9C,IAAAA,SAAS,CACPqC,KAAK,CAACvG,KAAK,KAAK,IAAI,IAAI,CAACuG,KAAK,CAACU,QAAQ,EAAA,2CAEzC,CAAC,CAAA;MACD/C,SAAS,CACP,CAAC0C,QAAQ,CAACG,EAAE,CAAC,EACb,qCAAqCA,GAAAA,EAAE,GACrC,aAAA,GAAA,wDACJ,CAAC,CAAA;EAED,IAAA,IAAIT,YAAY,CAACC,KAAK,CAAC,EAAE;QACvB,IAAIW,UAAwC,GAAAlC,QAAA,CAAA,EAAA,EACvCuB,KAAK,EACLG,kBAAkB,CAACH,KAAK,CAAC,EAAA;EAC5BQ,QAAAA,EAAAA;SACD,CAAA,CAAA;EACDH,MAAAA,QAAQ,CAACG,EAAE,CAAC,GAAGG,UAAU,CAAA;EACzB,MAAA,OAAOA,UAAU,CAAA;EACnB,KAAC,MAAM;QACL,IAAIC,iBAAkD,GAAAnC,QAAA,CAAA,EAAA,EACjDuB,KAAK,EACLG,kBAAkB,CAACH,KAAK,CAAC,EAAA;UAC5BQ,EAAE;EACFE,QAAAA,QAAQ,EAAE9G,SAAAA;SACX,CAAA,CAAA;EACDyG,MAAAA,QAAQ,CAACG,EAAE,CAAC,GAAGI,iBAAiB,CAAA;QAEhC,IAAIZ,KAAK,CAACU,QAAQ,EAAE;EAClBE,QAAAA,iBAAiB,CAACF,QAAQ,GAAGT,yBAAyB,CACpDD,KAAK,CAACU,QAAQ,EACdP,kBAAkB,EAClBG,QAAQ,EACRD,QACF,CAAC,CAAA;EACH,OAAA;EAEA,MAAA,OAAOO,iBAAiB,CAAA;EAC1B,KAAA;EACF,GAAC,CAAC,CAAA;EACJ,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACO,SAASC,WAAWA,CAGzBX,MAAyB,EACzBY,WAAuC,EACvCC,QAAQ,EAC8C;EAAA,EAAA,IADtDA,QAAQ,KAAA,KAAA,CAAA,EAAA;EAARA,IAAAA,QAAQ,GAAG,GAAG,CAAA;EAAA,GAAA;IAEd,OAAOC,eAAe,CAACd,MAAM,EAAEY,WAAW,EAAEC,QAAQ,EAAE,KAAK,CAAC,CAAA;EAC9D,CAAA;EAEO,SAASC,eAAeA,CAG7Bd,MAAyB,EACzBY,WAAuC,EACvCC,QAAgB,EAChBE,YAAqB,EACiC;EACtD,EAAA,IAAIxG,QAAQ,GACV,OAAOqG,WAAW,KAAK,QAAQ,GAAGvF,SAAS,CAACuF,WAAW,CAAC,GAAGA,WAAW,CAAA;IAExE,IAAInG,QAAQ,GAAGuG,aAAa,CAACzG,QAAQ,CAACE,QAAQ,IAAI,GAAG,EAAEoG,QAAQ,CAAC,CAAA;IAEhE,IAAIpG,QAAQ,IAAI,IAAI,EAAE;EACpB,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;EAEA,EAAA,IAAIwG,QAAQ,GAAGC,aAAa,CAAClB,MAAM,CAAC,CAAA;IACpCmB,iBAAiB,CAACF,QAAQ,CAAC,CAAA;IAE3B,IAAIG,OAAO,GAAG,IAAI,CAAA;EAClB,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAED,OAAO,IAAI,IAAI,IAAIC,CAAC,GAAGJ,QAAQ,CAACrH,MAAM,EAAE,EAAEyH,CAAC,EAAE;EAC3D;EACA;EACA;EACA;EACA;EACA;EACA,IAAA,IAAIC,OAAO,GAAGC,UAAU,CAAC9G,QAAQ,CAAC,CAAA;MAClC2G,OAAO,GAAGI,gBAAgB,CACxBP,QAAQ,CAACI,CAAC,CAAC,EACXC,OAAO,EACPP,YACF,CAAC,CAAA;EACH,GAAA;EAEA,EAAA,OAAOK,OAAO,CAAA;EAChB,CAAA;EAUO,SAASK,0BAA0BA,CACxCC,KAA6B,EAC7BC,UAAqB,EACZ;IACT,IAAI;MAAE7B,KAAK;MAAErF,QAAQ;EAAEmH,IAAAA,MAAAA;EAAO,GAAC,GAAGF,KAAK,CAAA;IACvC,OAAO;MACLpB,EAAE,EAAER,KAAK,CAACQ,EAAE;MACZ7F,QAAQ;MACRmH,MAAM;EACNC,IAAAA,IAAI,EAAEF,UAAU,CAAC7B,KAAK,CAACQ,EAAE,CAAC;MAC1BwB,MAAM,EAAEhC,KAAK,CAACgC,MAAAA;KACf,CAAA;EACH,CAAA;EAmBA,SAASZ,aAAaA,CAGpBlB,MAAyB,EACzBiB,QAAwC,EACxCc,WAAyC,EACzC7B,UAAU,EACsB;EAAA,EAAA,IAHhCe,QAAwC,KAAA,KAAA,CAAA,EAAA;EAAxCA,IAAAA,QAAwC,GAAG,EAAE,CAAA;EAAA,GAAA;EAAA,EAAA,IAC7Cc,WAAyC,KAAA,KAAA,CAAA,EAAA;EAAzCA,IAAAA,WAAyC,GAAG,EAAE,CAAA;EAAA,GAAA;EAAA,EAAA,IAC9C7B,UAAU,KAAA,KAAA,CAAA,EAAA;EAAVA,IAAAA,UAAU,GAAG,EAAE,CAAA;EAAA,GAAA;IAEf,IAAI8B,YAAY,GAAGA,CACjBlC,KAAsB,EACtBvG,KAAa,EACb0I,YAAqB,KAClB;EACH,IAAA,IAAIC,IAAgC,GAAG;QACrCD,YAAY,EACVA,YAAY,KAAKvI,SAAS,GAAGoG,KAAK,CAAC1E,IAAI,IAAI,EAAE,GAAG6G,YAAY;EAC9DE,MAAAA,aAAa,EAAErC,KAAK,CAACqC,aAAa,KAAK,IAAI;EAC3CC,MAAAA,aAAa,EAAE7I,KAAK;EACpBuG,MAAAA,KAAAA;OACD,CAAA;MAED,IAAIoC,IAAI,CAACD,YAAY,CAACpF,UAAU,CAAC,GAAG,CAAC,EAAE;EACrCY,MAAAA,SAAS,CACPyE,IAAI,CAACD,YAAY,CAACpF,UAAU,CAACqD,UAAU,CAAC,EACxC,wBAAA,GAAwBgC,IAAI,CAACD,YAAY,qCACnC/B,UAAU,GAAA,gDAAA,CAA+C,gEAEjE,CAAC,CAAA;EAEDgC,MAAAA,IAAI,CAACD,YAAY,GAAGC,IAAI,CAACD,YAAY,CAAC1E,KAAK,CAAC2C,UAAU,CAACtG,MAAM,CAAC,CAAA;EAChE,KAAA;MAEA,IAAIwB,IAAI,GAAGiH,SAAS,CAAC,CAACnC,UAAU,EAAEgC,IAAI,CAACD,YAAY,CAAC,CAAC,CAAA;EACrD,IAAA,IAAIK,UAAU,GAAGP,WAAW,CAACQ,MAAM,CAACL,IAAI,CAAC,CAAA;;EAEzC;EACA;EACA;MACA,IAAIpC,KAAK,CAACU,QAAQ,IAAIV,KAAK,CAACU,QAAQ,CAAC5G,MAAM,GAAG,CAAC,EAAE;QAC/C6D,SAAS;EACP;EACA;QACAqC,KAAK,CAACvG,KAAK,KAAK,IAAI,EACpB,yDACuC6B,IAAAA,qCAAAA,GAAAA,IAAI,SAC7C,CAAC,CAAA;QACD8F,aAAa,CAACpB,KAAK,CAACU,QAAQ,EAAES,QAAQ,EAAEqB,UAAU,EAAElH,IAAI,CAAC,CAAA;EAC3D,KAAA;;EAEA;EACA;MACA,IAAI0E,KAAK,CAAC1E,IAAI,IAAI,IAAI,IAAI,CAAC0E,KAAK,CAACvG,KAAK,EAAE;EACtC,MAAA,OAAA;EACF,KAAA;MAEA0H,QAAQ,CAACzF,IAAI,CAAC;QACZJ,IAAI;QACJoH,KAAK,EAAEC,YAAY,CAACrH,IAAI,EAAE0E,KAAK,CAACvG,KAAK,CAAC;EACtC+I,MAAAA,UAAAA;EACF,KAAC,CAAC,CAAA;KACH,CAAA;EACDtC,EAAAA,MAAM,CAAC0C,OAAO,CAAC,CAAC5C,KAAK,EAAEvG,KAAK,KAAK;EAAA,IAAA,IAAAoJ,WAAA,CAAA;EAC/B;EACA,IAAA,IAAI7C,KAAK,CAAC1E,IAAI,KAAK,EAAE,IAAI,GAAAuH,WAAA,GAAC7C,KAAK,CAAC1E,IAAI,aAAVuH,WAAA,CAAYC,QAAQ,CAAC,GAAG,CAAC,CAAE,EAAA;EACnDZ,MAAAA,YAAY,CAAClC,KAAK,EAAEvG,KAAK,CAAC,CAAA;EAC5B,KAAC,MAAM;QACL,KAAK,IAAIsJ,QAAQ,IAAIC,uBAAuB,CAAChD,KAAK,CAAC1E,IAAI,CAAC,EAAE;EACxD4G,QAAAA,YAAY,CAAClC,KAAK,EAAEvG,KAAK,EAAEsJ,QAAQ,CAAC,CAAA;EACtC,OAAA;EACF,KAAA;EACF,GAAC,CAAC,CAAA;EAEF,EAAA,OAAO5B,QAAQ,CAAA;EACjB,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAAS6B,uBAAuBA,CAAC1H,IAAY,EAAY;EACvD,EAAA,IAAI2H,QAAQ,GAAG3H,IAAI,CAAC4H,KAAK,CAAC,GAAG,CAAC,CAAA;EAC9B,EAAA,IAAID,QAAQ,CAACnJ,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE,CAAA;EAEpC,EAAA,IAAI,CAACqJ,KAAK,EAAE,GAAGC,IAAI,CAAC,GAAGH,QAAQ,CAAA;;EAE/B;EACA,EAAA,IAAII,UAAU,GAAGF,KAAK,CAACG,QAAQ,CAAC,GAAG,CAAC,CAAA;EACpC;IACA,IAAIC,QAAQ,GAAGJ,KAAK,CAACpH,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAA;EAEvC,EAAA,IAAIqH,IAAI,CAACtJ,MAAM,KAAK,CAAC,EAAE;EACrB;EACA;MACA,OAAOuJ,UAAU,GAAG,CAACE,QAAQ,EAAE,EAAE,CAAC,GAAG,CAACA,QAAQ,CAAC,CAAA;EACjD,GAAA;IAEA,IAAIC,YAAY,GAAGR,uBAAuB,CAACI,IAAI,CAAC3C,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;IAE1D,IAAIgD,MAAgB,GAAG,EAAE,CAAA;;EAEzB;EACA;EACA;EACA;EACA;EACA;EACA;IACAA,MAAM,CAAC/H,IAAI,CACT,GAAG8H,YAAY,CAACjK,GAAG,CAAEmK,OAAO,IAC1BA,OAAO,KAAK,EAAE,GAAGH,QAAQ,GAAG,CAACA,QAAQ,EAAEG,OAAO,CAAC,CAACjD,IAAI,CAAC,GAAG,CAC1D,CACF,CAAC,CAAA;;EAED;EACA,EAAA,IAAI4C,UAAU,EAAE;EACdI,IAAAA,MAAM,CAAC/H,IAAI,CAAC,GAAG8H,YAAY,CAAC,CAAA;EAC9B,GAAA;;EAEA;IACA,OAAOC,MAAM,CAAClK,GAAG,CAAEwJ,QAAQ,IACzBzH,IAAI,CAACyB,UAAU,CAAC,GAAG,CAAC,IAAIgG,QAAQ,KAAK,EAAE,GAAG,GAAG,GAAGA,QAClD,CAAC,CAAA;EACH,CAAA;EAEA,SAAS1B,iBAAiBA,CAACF,QAAuB,EAAQ;IACxDA,QAAQ,CAACwC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KACjBD,CAAC,CAAClB,KAAK,KAAKmB,CAAC,CAACnB,KAAK,GACfmB,CAAC,CAACnB,KAAK,GAAGkB,CAAC,CAAClB,KAAK;EAAC,IAClBoB,cAAc,CACZF,CAAC,CAACpB,UAAU,CAACjJ,GAAG,CAAE6I,IAAI,IAAKA,IAAI,CAACE,aAAa,CAAC,EAC9CuB,CAAC,CAACrB,UAAU,CAACjJ,GAAG,CAAE6I,IAAI,IAAKA,IAAI,CAACE,aAAa,CAC/C,CACN,CAAC,CAAA;EACH,CAAA;EAEA,MAAMyB,OAAO,GAAG,WAAW,CAAA;EAC3B,MAAMC,mBAAmB,GAAG,CAAC,CAAA;EAC7B,MAAMC,eAAe,GAAG,CAAC,CAAA;EACzB,MAAMC,iBAAiB,GAAG,CAAC,CAAA;EAC3B,MAAMC,kBAAkB,GAAG,EAAE,CAAA;EAC7B,MAAMC,YAAY,GAAG,CAAC,CAAC,CAAA;EACvB,MAAMC,OAAO,GAAIC,CAAS,IAAKA,CAAC,KAAK,GAAG,CAAA;EAExC,SAAS3B,YAAYA,CAACrH,IAAY,EAAE7B,KAA0B,EAAU;EACtE,EAAA,IAAIwJ,QAAQ,GAAG3H,IAAI,CAAC4H,KAAK,CAAC,GAAG,CAAC,CAAA;EAC9B,EAAA,IAAIqB,YAAY,GAAGtB,QAAQ,CAACnJ,MAAM,CAAA;EAClC,EAAA,IAAImJ,QAAQ,CAACuB,IAAI,CAACH,OAAO,CAAC,EAAE;EAC1BE,IAAAA,YAAY,IAAIH,YAAY,CAAA;EAC9B,GAAA;EAEA,EAAA,IAAI3K,KAAK,EAAE;EACT8K,IAAAA,YAAY,IAAIN,eAAe,CAAA;EACjC,GAAA;EAEA,EAAA,OAAOhB,QAAQ,CACZwB,MAAM,CAAEH,CAAC,IAAK,CAACD,OAAO,CAACC,CAAC,CAAC,CAAC,CAC1BI,MAAM,CACL,CAAChC,KAAK,EAAEiC,OAAO,KACbjC,KAAK,IACJqB,OAAO,CAACa,IAAI,CAACD,OAAO,CAAC,GAClBX,mBAAmB,GACnBW,OAAO,KAAK,EAAE,GACdT,iBAAiB,GACjBC,kBAAkB,CAAC,EACzBI,YACF,CAAC,CAAA;EACL,CAAA;EAEA,SAAST,cAAcA,CAACF,CAAW,EAAEC,CAAW,EAAU;EACxD,EAAA,IAAIgB,QAAQ,GACVjB,CAAC,CAAC9J,MAAM,KAAK+J,CAAC,CAAC/J,MAAM,IAAI8J,CAAC,CAACnG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACqH,KAAK,CAAC,CAAC5K,CAAC,EAAEqH,CAAC,KAAKrH,CAAC,KAAK2J,CAAC,CAACtC,CAAC,CAAC,CAAC,CAAA;EAErE,EAAA,OAAOsD,QAAQ;EACX;EACA;EACA;EACA;EACAjB,EAAAA,CAAC,CAACA,CAAC,CAAC9J,MAAM,GAAG,CAAC,CAAC,GAAG+J,CAAC,CAACA,CAAC,CAAC/J,MAAM,GAAG,CAAC,CAAC;EACjC;EACA;IACA,CAAC,CAAA;EACP,CAAA;EAEA,SAAS4H,gBAAgBA,CAIvBqD,MAAoC,EACpCpK,QAAgB,EAChBsG,YAAY,EAC4C;EAAA,EAAA,IADxDA,YAAY,KAAA,KAAA,CAAA,EAAA;EAAZA,IAAAA,YAAY,GAAG,KAAK,CAAA;EAAA,GAAA;IAEpB,IAAI;EAAEuB,IAAAA,UAAAA;EAAW,GAAC,GAAGuC,MAAM,CAAA;IAE3B,IAAIC,aAAa,GAAG,EAAE,CAAA;IACtB,IAAIC,eAAe,GAAG,GAAG,CAAA;IACzB,IAAI3D,OAAwD,GAAG,EAAE,CAAA;EACjE,EAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,UAAU,CAAC1I,MAAM,EAAE,EAAEyH,CAAC,EAAE;EAC1C,IAAA,IAAIa,IAAI,GAAGI,UAAU,CAACjB,CAAC,CAAC,CAAA;MACxB,IAAI2D,GAAG,GAAG3D,CAAC,KAAKiB,UAAU,CAAC1I,MAAM,GAAG,CAAC,CAAA;EACrC,IAAA,IAAIqL,iBAAiB,GACnBF,eAAe,KAAK,GAAG,GACnBtK,QAAQ,GACRA,QAAQ,CAAC8C,KAAK,CAACwH,eAAe,CAACnL,MAAM,CAAC,IAAI,GAAG,CAAA;MACnD,IAAI8H,KAAK,GAAGwD,SAAS,CACnB;QAAE9J,IAAI,EAAE8G,IAAI,CAACD,YAAY;QAAEE,aAAa,EAAED,IAAI,CAACC,aAAa;EAAE6C,MAAAA,GAAAA;OAAK,EACnEC,iBACF,CAAC,CAAA;EAED,IAAA,IAAInF,KAAK,GAAGoC,IAAI,CAACpC,KAAK,CAAA;MAEtB,IACE,CAAC4B,KAAK,IACNsD,GAAG,IACHjE,YAAY,IACZ,CAACuB,UAAU,CAACA,UAAU,CAAC1I,MAAM,GAAG,CAAC,CAAC,CAACkG,KAAK,CAACvG,KAAK,EAC9C;QACAmI,KAAK,GAAGwD,SAAS,CACf;UACE9J,IAAI,EAAE8G,IAAI,CAACD,YAAY;UACvBE,aAAa,EAAED,IAAI,CAACC,aAAa;EACjC6C,QAAAA,GAAG,EAAE,KAAA;SACN,EACDC,iBACF,CAAC,CAAA;EACH,KAAA;MAEA,IAAI,CAACvD,KAAK,EAAE;EACV,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;MAEAyD,MAAM,CAAC7F,MAAM,CAACwF,aAAa,EAAEpD,KAAK,CAACE,MAAM,CAAC,CAAA;MAE1CR,OAAO,CAAC5F,IAAI,CAAC;EACX;EACAoG,MAAAA,MAAM,EAAEkD,aAAiC;QACzCrK,QAAQ,EAAE4H,SAAS,CAAC,CAAC0C,eAAe,EAAErD,KAAK,CAACjH,QAAQ,CAAC,CAAC;EACtD2K,MAAAA,YAAY,EAAEC,iBAAiB,CAC7BhD,SAAS,CAAC,CAAC0C,eAAe,EAAErD,KAAK,CAAC0D,YAAY,CAAC,CACjD,CAAC;EACDtF,MAAAA,KAAAA;EACF,KAAC,CAAC,CAAA;EAEF,IAAA,IAAI4B,KAAK,CAAC0D,YAAY,KAAK,GAAG,EAAE;QAC9BL,eAAe,GAAG1C,SAAS,CAAC,CAAC0C,eAAe,EAAErD,KAAK,CAAC0D,YAAY,CAAC,CAAC,CAAA;EACpE,KAAA;EACF,GAAA;EAEA,EAAA,OAAOhE,OAAO,CAAA;EAChB,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACO,SAASkE,YAAYA,CAC1BC,YAAkB,EAClB3D,MAEC,EACO;EAAA,EAAA,IAHRA,MAEC,KAAA,KAAA,CAAA,EAAA;MAFDA,MAEC,GAAG,EAAE,CAAA;EAAA,GAAA;IAEN,IAAIxG,IAAY,GAAGmK,YAAY,CAAA;EAC/B,EAAA,IAAInK,IAAI,CAACgI,QAAQ,CAAC,GAAG,CAAC,IAAIhI,IAAI,KAAK,GAAG,IAAI,CAACA,IAAI,CAACgI,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC9D1I,OAAO,CACL,KAAK,EACL,eAAeU,GAAAA,IAAI,GACbA,mCAAAA,IAAAA,IAAAA,GAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAqC,oCAAA,CAAA,GAAA,kEACE,IAChCT,oCAAAA,GAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAA,KAAA,CACjE,CAAC,CAAA;MACDT,IAAI,GAAGA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAS,CAAA;EAC1C,GAAA;;EAEA;IACA,MAAM2J,MAAM,GAAGpK,IAAI,CAACyB,UAAU,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAA;IAE9C,MAAMhC,SAAS,GAAI4K,CAAM,IACvBA,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,OAAOA,CAAC,KAAK,QAAQ,GAAGA,CAAC,GAAGpF,MAAM,CAACoF,CAAC,CAAC,CAAA;EAExD,EAAA,MAAM1C,QAAQ,GAAG3H,IAAI,CAClB4H,KAAK,CAAC,KAAK,CAAC,CACZ3J,GAAG,CAAC,CAACoL,OAAO,EAAElL,KAAK,EAAEmM,KAAK,KAAK;MAC9B,MAAMC,aAAa,GAAGpM,KAAK,KAAKmM,KAAK,CAAC9L,MAAM,GAAG,CAAC,CAAA;;EAEhD;EACA,IAAA,IAAI+L,aAAa,IAAIlB,OAAO,KAAK,GAAG,EAAE;QACpC,MAAMmB,IAAI,GAAG,GAAsB,CAAA;EACnC;EACA,MAAA,OAAO/K,SAAS,CAAC+G,MAAM,CAACgE,IAAI,CAAC,CAAC,CAAA;EAChC,KAAA;EAEA,IAAA,MAAMC,QAAQ,GAAGpB,OAAO,CAAC/C,KAAK,CAAC,kBAAkB,CAAC,CAAA;EAClD,IAAA,IAAImE,QAAQ,EAAE;EACZ,MAAA,MAAM,GAAGvL,GAAG,EAAEwL,QAAQ,CAAC,GAAGD,QAAQ,CAAA;EAClC,MAAA,IAAIE,KAAK,GAAGnE,MAAM,CAACtH,GAAG,CAAoB,CAAA;QAC1CmD,SAAS,CAACqI,QAAQ,KAAK,GAAG,IAAIC,KAAK,IAAI,IAAI,EAAA,aAAA,GAAezL,GAAG,GAAA,UAAS,CAAC,CAAA;QACvE,OAAOO,SAAS,CAACkL,KAAK,CAAC,CAAA;EACzB,KAAA;;EAEA;EACA,IAAA,OAAOtB,OAAO,CAAC5I,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAA;KACnC,CAAA;EACD;EAAA,GACC0I,MAAM,CAAEE,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC,CAAA;EAEjC,EAAA,OAAOe,MAAM,GAAGzC,QAAQ,CAACxC,IAAI,CAAC,GAAG,CAAC,CAAA;EACpC,CAAA;;EAEA;EACA;EACA;;EAmBA;EACA;EACA;;EAwBA;EACA;EACA;EACA;EACA;EACA;EACO,SAAS2E,SAASA,CAIvBc,OAAiC,EACjCvL,QAAgB,EACY;EAC5B,EAAA,IAAI,OAAOuL,OAAO,KAAK,QAAQ,EAAE;EAC/BA,IAAAA,OAAO,GAAG;EAAE5K,MAAAA,IAAI,EAAE4K,OAAO;EAAE7D,MAAAA,aAAa,EAAE,KAAK;EAAE6C,MAAAA,GAAG,EAAE,IAAA;OAAM,CAAA;EAC9D,GAAA;EAEA,EAAA,IAAI,CAACiB,OAAO,EAAEC,cAAc,CAAC,GAAGC,WAAW,CACzCH,OAAO,CAAC5K,IAAI,EACZ4K,OAAO,CAAC7D,aAAa,EACrB6D,OAAO,CAAChB,GACV,CAAC,CAAA;EAED,EAAA,IAAItD,KAAK,GAAGjH,QAAQ,CAACiH,KAAK,CAACuE,OAAO,CAAC,CAAA;EACnC,EAAA,IAAI,CAACvE,KAAK,EAAE,OAAO,IAAI,CAAA;EAEvB,EAAA,IAAIqD,eAAe,GAAGrD,KAAK,CAAC,CAAC,CAAC,CAAA;IAC9B,IAAI0D,YAAY,GAAGL,eAAe,CAAClJ,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;EAC3D,EAAA,IAAIuK,aAAa,GAAG1E,KAAK,CAACnE,KAAK,CAAC,CAAC,CAAC,CAAA;EAClC,EAAA,IAAIqE,MAAc,GAAGsE,cAAc,CAAC1B,MAAM,CACxC,CAAC6B,IAAI,EAAA7H,IAAA,EAA6BjF,KAAK,KAAK;MAAA,IAArC;QAAE+M,SAAS;EAAEnD,MAAAA,UAAAA;EAAW,KAAC,GAAA3E,IAAA,CAAA;EAC9B;EACA;MACA,IAAI8H,SAAS,KAAK,GAAG,EAAE;EACrB,MAAA,IAAIC,UAAU,GAAGH,aAAa,CAAC7M,KAAK,CAAC,IAAI,EAAE,CAAA;QAC3C6L,YAAY,GAAGL,eAAe,CAC3BxH,KAAK,CAAC,CAAC,EAAEwH,eAAe,CAACnL,MAAM,GAAG2M,UAAU,CAAC3M,MAAM,CAAC,CACpDiC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;EAC7B,KAAA;EAEA,IAAA,MAAM6B,KAAK,GAAG0I,aAAa,CAAC7M,KAAK,CAAC,CAAA;EAClC,IAAA,IAAI4J,UAAU,IAAI,CAACzF,KAAK,EAAE;EACxB2I,MAAAA,IAAI,CAACC,SAAS,CAAC,GAAG5M,SAAS,CAAA;EAC7B,KAAC,MAAM;EACL2M,MAAAA,IAAI,CAACC,SAAS,CAAC,GAAG,CAAC5I,KAAK,IAAI,EAAE,EAAE7B,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;EACtD,KAAA;EACA,IAAA,OAAOwK,IAAI,CAAA;KACZ,EACD,EACF,CAAC,CAAA;IAED,OAAO;MACLzE,MAAM;EACNnH,IAAAA,QAAQ,EAAEsK,eAAe;MACzBK,YAAY;EACZY,IAAAA,OAAAA;KACD,CAAA;EACH,CAAA;EAIA,SAASG,WAAWA,CAClB/K,IAAY,EACZ+G,aAAa,EACb6C,GAAG,EAC4B;EAAA,EAAA,IAF/B7C,aAAa,KAAA,KAAA,CAAA,EAAA;EAAbA,IAAAA,aAAa,GAAG,KAAK,CAAA;EAAA,GAAA;EAAA,EAAA,IACrB6C,GAAG,KAAA,KAAA,CAAA,EAAA;EAAHA,IAAAA,GAAG,GAAG,IAAI,CAAA;EAAA,GAAA;EAEVtK,EAAAA,OAAO,CACLU,IAAI,KAAK,GAAG,IAAI,CAACA,IAAI,CAACgI,QAAQ,CAAC,GAAG,CAAC,IAAIhI,IAAI,CAACgI,QAAQ,CAAC,IAAI,CAAC,EAC1D,eAAA,GAAehI,IAAI,GACbA,mCAAAA,IAAAA,IAAAA,GAAAA,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAqC,oCAAA,CAAA,GAAA,kEACE,2CAChCT,IAAI,CAACS,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,SACjE,CAAC,CAAA;IAED,IAAI+F,MAA2B,GAAG,EAAE,CAAA;EACpC,EAAA,IAAI4E,YAAY,GACd,GAAG,GACHpL,IAAI,CACDS,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;EAAC,GACvBA,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAAC,GACrBA,OAAO,CAAC,oBAAoB,EAAE,MAAM,CAAC;KACrCA,OAAO,CACN,mBAAmB,EACnB,CAAC4K,CAAS,EAAEH,SAAiB,EAAEnD,UAAU,KAAK;MAC5CvB,MAAM,CAACpG,IAAI,CAAC;QAAE8K,SAAS;QAAEnD,UAAU,EAAEA,UAAU,IAAI,IAAA;EAAK,KAAC,CAAC,CAAA;EAC1D,IAAA,OAAOA,UAAU,GAAG,cAAc,GAAG,YAAY,CAAA;EACnD,GACF,CAAC,CAAA;EAEL,EAAA,IAAI/H,IAAI,CAACgI,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtBxB,MAAM,CAACpG,IAAI,CAAC;EAAE8K,MAAAA,SAAS,EAAE,GAAA;EAAI,KAAC,CAAC,CAAA;MAC/BE,YAAY,IACVpL,IAAI,KAAK,GAAG,IAAIA,IAAI,KAAK,IAAI,GACzB,OAAO;QACP,mBAAmB,CAAC;KAC3B,MAAM,IAAI4J,GAAG,EAAE;EACd;EACAwB,IAAAA,YAAY,IAAI,OAAO,CAAA;KACxB,MAAM,IAAIpL,IAAI,KAAK,EAAE,IAAIA,IAAI,KAAK,GAAG,EAAE;EACtC;EACA;EACA;EACA;EACA;EACA;EACA;EACAoL,IAAAA,YAAY,IAAI,eAAe,CAAA;EACjC,GAAC,MAAM,CACL;EAGF,EAAA,IAAIP,OAAO,GAAG,IAAIS,MAAM,CAACF,YAAY,EAAErE,aAAa,GAAGzI,SAAS,GAAG,GAAG,CAAC,CAAA;EAEvE,EAAA,OAAO,CAACuM,OAAO,EAAErE,MAAM,CAAC,CAAA;EAC1B,CAAA;EAEO,SAASL,UAAUA,CAAC7D,KAAa,EAAE;IACxC,IAAI;MACF,OAAOA,KAAK,CACTsF,KAAK,CAAC,GAAG,CAAC,CACV3J,GAAG,CAAEsN,CAAC,IAAKC,kBAAkB,CAACD,CAAC,CAAC,CAAC9K,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CACvD0E,IAAI,CAAC,GAAG,CAAC,CAAA;KACb,CAAC,OAAOpB,KAAK,EAAE;MACdzE,OAAO,CACL,KAAK,EACL,iBAAA,GAAiBgD,KAAK,GAC2C,6CAAA,GAAA,+DAAA,IAAA,YAAA,GAClDyB,KAAK,GAAA,IAAA,CACtB,CAAC,CAAA;EAED,IAAA,OAAOzB,KAAK,CAAA;EACd,GAAA;EACF,CAAA;;EAEA;EACA;EACA;EACO,SAASsD,aAAaA,CAC3BvG,QAAgB,EAChBoG,QAAgB,EACD;EACf,EAAA,IAAIA,QAAQ,KAAK,GAAG,EAAE,OAAOpG,QAAQ,CAAA;EAErC,EAAA,IAAI,CAACA,QAAQ,CAACoM,WAAW,EAAE,CAAChK,UAAU,CAACgE,QAAQ,CAACgG,WAAW,EAAE,CAAC,EAAE;EAC9D,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;EACA;EACA,EAAA,IAAIC,UAAU,GAAGjG,QAAQ,CAACuC,QAAQ,CAAC,GAAG,CAAC,GACnCvC,QAAQ,CAACjH,MAAM,GAAG,CAAC,GACnBiH,QAAQ,CAACjH,MAAM,CAAA;EACnB,EAAA,IAAImN,QAAQ,GAAGtM,QAAQ,CAACE,MAAM,CAACmM,UAAU,CAAC,CAAA;EAC1C,EAAA,IAAIC,QAAQ,IAAIA,QAAQ,KAAK,GAAG,EAAE;EAChC;EACA,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;EAEA,EAAA,OAAOtM,QAAQ,CAAC8C,KAAK,CAACuJ,UAAU,CAAC,IAAI,GAAG,CAAA;EAC1C,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACO,SAASE,WAAWA,CAAC3M,EAAM,EAAE4M,YAAY,EAAc;EAAA,EAAA,IAA1BA,YAAY,KAAA,KAAA,CAAA,EAAA;EAAZA,IAAAA,YAAY,GAAG,GAAG,CAAA;EAAA,GAAA;IACpD,IAAI;EACFxM,IAAAA,QAAQ,EAAEyM,UAAU;EACpB5L,IAAAA,MAAM,GAAG,EAAE;EACXC,IAAAA,IAAI,GAAG,EAAA;KACR,GAAG,OAAOlB,EAAE,KAAK,QAAQ,GAAGgB,SAAS,CAAChB,EAAE,CAAC,GAAGA,EAAE,CAAA;IAE/C,IAAII,QAAQ,GAAGyM,UAAU,GACrBA,UAAU,CAACrK,UAAU,CAAC,GAAG,CAAC,GACxBqK,UAAU,GACVC,eAAe,CAACD,UAAU,EAAED,YAAY,CAAC,GAC3CA,YAAY,CAAA;IAEhB,OAAO;MACLxM,QAAQ;EACRa,IAAAA,MAAM,EAAE8L,eAAe,CAAC9L,MAAM,CAAC;MAC/BC,IAAI,EAAE8L,aAAa,CAAC9L,IAAI,CAAA;KACzB,CAAA;EACH,CAAA;EAEA,SAAS4L,eAAeA,CAAClF,YAAoB,EAAEgF,YAAoB,EAAU;EAC3E,EAAA,IAAIlE,QAAQ,GAAGkE,YAAY,CAACpL,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACmH,KAAK,CAAC,GAAG,CAAC,CAAA;EAC1D,EAAA,IAAIsE,gBAAgB,GAAGrF,YAAY,CAACe,KAAK,CAAC,GAAG,CAAC,CAAA;EAE9CsE,EAAAA,gBAAgB,CAAC5E,OAAO,CAAE+B,OAAO,IAAK;MACpC,IAAIA,OAAO,KAAK,IAAI,EAAE;EACpB;QACA,IAAI1B,QAAQ,CAACnJ,MAAM,GAAG,CAAC,EAAEmJ,QAAQ,CAACwE,GAAG,EAAE,CAAA;EACzC,KAAC,MAAM,IAAI9C,OAAO,KAAK,GAAG,EAAE;EAC1B1B,MAAAA,QAAQ,CAACvH,IAAI,CAACiJ,OAAO,CAAC,CAAA;EACxB,KAAA;EACF,GAAC,CAAC,CAAA;EAEF,EAAA,OAAO1B,QAAQ,CAACnJ,MAAM,GAAG,CAAC,GAAGmJ,QAAQ,CAACxC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;EACvD,CAAA;EAEA,SAASiH,mBAAmBA,CAC1BC,IAAY,EACZC,KAAa,EACbC,IAAY,EACZvM,IAAmB,EACnB;EACA,EAAA,OACE,oBAAqBqM,GAAAA,IAAI,GACjBC,sCAAAA,IAAAA,MAAAA,GAAAA,KAAK,iBAAa9M,IAAI,CAACC,SAAS,CACtCO,IACF,CAAC,GAAA,oCAAA,CAAoC,IAC7BuM,MAAAA,GAAAA,IAAI,8DAA2D,GACJ,qEAAA,CAAA;EAEvE,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,SAASC,0BAA0BA,CAExCxG,OAAY,EAAE;EACd,EAAA,OAAOA,OAAO,CAACmD,MAAM,CACnB,CAAC7C,KAAK,EAAEnI,KAAK,KACXA,KAAK,KAAK,CAAC,IAAKmI,KAAK,CAAC5B,KAAK,CAAC1E,IAAI,IAAIsG,KAAK,CAAC5B,KAAK,CAAC1E,IAAI,CAACxB,MAAM,GAAG,CAClE,CAAC,CAAA;EACH,CAAA;;EAEA;EACA;EACO,SAASiO,mBAAmBA,CAEjCzG,OAAY,EAAE0G,oBAA6B,EAAE;EAC7C,EAAA,IAAIC,WAAW,GAAGH,0BAA0B,CAACxG,OAAO,CAAC,CAAA;;EAErD;EACA;EACA;EACA,EAAA,IAAI0G,oBAAoB,EAAE;MACxB,OAAOC,WAAW,CAAC1O,GAAG,CAAC,CAACqI,KAAK,EAAErD,GAAG,KAChCA,GAAG,KAAK0J,WAAW,CAACnO,MAAM,GAAG,CAAC,GAAG8H,KAAK,CAACjH,QAAQ,GAAGiH,KAAK,CAAC0D,YAC1D,CAAC,CAAA;EACH,GAAA;IAEA,OAAO2C,WAAW,CAAC1O,GAAG,CAAEqI,KAAK,IAAKA,KAAK,CAAC0D,YAAY,CAAC,CAAA;EACvD,CAAA;;EAEA;EACA;EACA;EACO,SAAS4C,SAASA,CACvBC,KAAS,EACTC,cAAwB,EACxBC,gBAAwB,EACxBC,cAAc,EACR;EAAA,EAAA,IADNA,cAAc,KAAA,KAAA,CAAA,EAAA;EAAdA,IAAAA,cAAc,GAAG,KAAK,CAAA;EAAA,GAAA;EAEtB,EAAA,IAAI/N,EAAiB,CAAA;EACrB,EAAA,IAAI,OAAO4N,KAAK,KAAK,QAAQ,EAAE;EAC7B5N,IAAAA,EAAE,GAAGgB,SAAS,CAAC4M,KAAK,CAAC,CAAA;EACvB,GAAC,MAAM;EACL5N,IAAAA,EAAE,GAAAkE,QAAA,CAAQ0J,EAAAA,EAAAA,KAAK,CAAE,CAAA;MAEjBxK,SAAS,CACP,CAACpD,EAAE,CAACI,QAAQ,IAAI,CAACJ,EAAE,CAACI,QAAQ,CAACmI,QAAQ,CAAC,GAAG,CAAC,EAC1C4E,mBAAmB,CAAC,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAEnN,EAAE,CACnD,CAAC,CAAA;MACDoD,SAAS,CACP,CAACpD,EAAE,CAACI,QAAQ,IAAI,CAACJ,EAAE,CAACI,QAAQ,CAACmI,QAAQ,CAAC,GAAG,CAAC,EAC1C4E,mBAAmB,CAAC,GAAG,EAAE,UAAU,EAAE,MAAM,EAAEnN,EAAE,CACjD,CAAC,CAAA;MACDoD,SAAS,CACP,CAACpD,EAAE,CAACiB,MAAM,IAAI,CAACjB,EAAE,CAACiB,MAAM,CAACsH,QAAQ,CAAC,GAAG,CAAC,EACtC4E,mBAAmB,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,EAAEnN,EAAE,CAC/C,CAAC,CAAA;EACH,GAAA;IAEA,IAAIgO,WAAW,GAAGJ,KAAK,KAAK,EAAE,IAAI5N,EAAE,CAACI,QAAQ,KAAK,EAAE,CAAA;IACpD,IAAIyM,UAAU,GAAGmB,WAAW,GAAG,GAAG,GAAGhO,EAAE,CAACI,QAAQ,CAAA;EAEhD,EAAA,IAAI6N,IAAY,CAAA;;EAEhB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACA,IAAIpB,UAAU,IAAI,IAAI,EAAE;EACtBoB,IAAAA,IAAI,GAAGH,gBAAgB,CAAA;EACzB,GAAC,MAAM;EACL,IAAA,IAAII,kBAAkB,GAAGL,cAAc,CAACtO,MAAM,GAAG,CAAC,CAAA;;EAElD;EACA;EACA;EACA;MACA,IAAI,CAACwO,cAAc,IAAIlB,UAAU,CAACrK,UAAU,CAAC,IAAI,CAAC,EAAE;EAClD,MAAA,IAAI2L,UAAU,GAAGtB,UAAU,CAAClE,KAAK,CAAC,GAAG,CAAC,CAAA;EAEtC,MAAA,OAAOwF,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;UAC7BA,UAAU,CAACC,KAAK,EAAE,CAAA;EAClBF,QAAAA,kBAAkB,IAAI,CAAC,CAAA;EACzB,OAAA;QAEAlO,EAAE,CAACI,QAAQ,GAAG+N,UAAU,CAACjI,IAAI,CAAC,GAAG,CAAC,CAAA;EACpC,KAAA;MAEA+H,IAAI,GAAGC,kBAAkB,IAAI,CAAC,GAAGL,cAAc,CAACK,kBAAkB,CAAC,GAAG,GAAG,CAAA;EAC3E,GAAA;EAEA,EAAA,IAAInN,IAAI,GAAG4L,WAAW,CAAC3M,EAAE,EAAEiO,IAAI,CAAC,CAAA;;EAEhC;EACA,EAAA,IAAII,wBAAwB,GAC1BxB,UAAU,IAAIA,UAAU,KAAK,GAAG,IAAIA,UAAU,CAAC9D,QAAQ,CAAC,GAAG,CAAC,CAAA;EAC9D;EACA,EAAA,IAAIuF,uBAAuB,GACzB,CAACN,WAAW,IAAInB,UAAU,KAAK,GAAG,KAAKiB,gBAAgB,CAAC/E,QAAQ,CAAC,GAAG,CAAC,CAAA;EACvE,EAAA,IACE,CAAChI,IAAI,CAACX,QAAQ,CAAC2I,QAAQ,CAAC,GAAG,CAAC,KAC3BsF,wBAAwB,IAAIC,uBAAuB,CAAC,EACrD;MACAvN,IAAI,CAACX,QAAQ,IAAI,GAAG,CAAA;EACtB,GAAA;EAEA,EAAA,OAAOW,IAAI,CAAA;EACb,CAAA;;EAEA;EACA;EACA;EACO,SAASwN,aAAaA,CAACvO,EAAM,EAAsB;EACxD;IACA,OAAOA,EAAE,KAAK,EAAE,IAAKA,EAAE,CAAUI,QAAQ,KAAK,EAAE,GAC5C,GAAG,GACH,OAAOJ,EAAE,KAAK,QAAQ,GACtBgB,SAAS,CAAChB,EAAE,CAAC,CAACI,QAAQ,GACtBJ,EAAE,CAACI,QAAQ,CAAA;EACjB,CAAA;;EAEA;EACA;EACA;QACa4H,SAAS,GAAIwG,KAAe,IACvCA,KAAK,CAACtI,IAAI,CAAC,GAAG,CAAC,CAAC1E,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAC;;EAExC;EACA;EACA;QACawJ,iBAAiB,GAAI5K,QAAgB,IAChDA,QAAQ,CAACoB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,EAAE,GAAG,EAAC;;EAEnD;EACA;EACA;EACO,MAAMuL,eAAe,GAAI9L,MAAc,IAC5C,CAACA,MAAM,IAAIA,MAAM,KAAK,GAAG,GACrB,EAAE,GACFA,MAAM,CAACuB,UAAU,CAAC,GAAG,CAAC,GACtBvB,MAAM,GACN,GAAG,GAAGA,MAAM,CAAA;;EAElB;EACA;EACA;EACO,MAAM+L,aAAa,GAAI9L,IAAY,IACxC,CAACA,IAAI,IAAIA,IAAI,KAAK,GAAG,GAAG,EAAE,GAAGA,IAAI,CAACsB,UAAU,CAAC,GAAG,CAAC,GAAGtB,IAAI,GAAG,GAAG,GAAGA,IAAI,CAAA;EAOvE;EACA;EACA;EACA;EACA;EACA;EACA;AACO,QAAMuN,IAAkB,GAAG,SAArBA,IAAkBA,CAAIjH,IAAI,EAAEkH,IAAI,EAAU;EAAA,EAAA,IAAdA,IAAI,KAAA,KAAA,CAAA,EAAA;MAAJA,IAAI,GAAG,EAAE,CAAA;EAAA,GAAA;EAChD,EAAA,IAAIC,YAAY,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG;EAAEE,IAAAA,MAAM,EAAEF,IAAAA;EAAK,GAAC,GAAGA,IAAI,CAAA;IAErE,IAAIG,OAAO,GAAG,IAAIC,OAAO,CAACH,YAAY,CAACE,OAAO,CAAC,CAAA;EAC/C,EAAA,IAAI,CAACA,OAAO,CAACE,GAAG,CAAC,cAAc,CAAC,EAAE;EAChCF,IAAAA,OAAO,CAACG,GAAG,CAAC,cAAc,EAAE,iCAAiC,CAAC,CAAA;EAChE,GAAA;EAEA,EAAA,OAAO,IAAIC,QAAQ,CAAC1O,IAAI,CAACC,SAAS,CAACgH,IAAI,CAAC,EAAAtD,QAAA,CAAA,EAAA,EACnCyK,YAAY,EAAA;EACfE,IAAAA,OAAAA;EAAO,GAAA,CACR,CAAC,CAAA;EACJ,EAAC;EAEM,MAAMK,oBAAoB,CAAI;EAKnCC,EAAAA,WAAWA,CAAC3H,IAAO,EAAEkH,IAAmB,EAAE;MAAA,IAJ1CU,CAAAA,IAAI,GAAW,sBAAsB,CAAA;MAKnC,IAAI,CAAC5H,IAAI,GAAGA,IAAI,CAAA;EAChB,IAAA,IAAI,CAACkH,IAAI,GAAGA,IAAI,IAAI,IAAI,CAAA;EAC1B,GAAA;EACF,CAAA;;EAEA;EACA;EACA;EACA;EACO,SAASlH,IAAIA,CAAIA,IAAO,EAAEkH,IAA4B,EAAE;IAC7D,OAAO,IAAIQ,oBAAoB,CAC7B1H,IAAI,EACJ,OAAOkH,IAAI,KAAK,QAAQ,GAAG;EAAEE,IAAAA,MAAM,EAAEF,IAAAA;KAAM,GAAGA,IAChD,CAAC,CAAA;EACH,CAAA;EAQO,MAAMW,oBAAoB,SAAS9L,KAAK,CAAC,EAAA;EAEzC,MAAM+L,YAAY,CAAC;EAWxBH,EAAAA,WAAWA,CAAC3H,IAA6B,EAAEmH,YAA2B,EAAE;EAAA,IAAA,IAAA,CAVhEY,cAAc,GAAgB,IAAIhK,GAAG,EAAU,CAAA;EAAA,IAAA,IAAA,CAI/CiK,WAAW,GACjB,IAAIjK,GAAG,EAAE,CAAA;MAAA,IAGXkK,CAAAA,YAAY,GAAa,EAAE,CAAA;EAGzBrM,IAAAA,SAAS,CACPoE,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,CAACkI,KAAK,CAACC,OAAO,CAACnI,IAAI,CAAC,EACxD,oCACF,CAAC,CAAA;;EAED;EACA;EACA,IAAA,IAAIoI,MAAyC,CAAA;EAC7C,IAAA,IAAI,CAACC,YAAY,GAAG,IAAIC,OAAO,CAAC,CAAC1D,CAAC,EAAE2D,CAAC,KAAMH,MAAM,GAAGG,CAAE,CAAC,CAAA;EACvD,IAAA,IAAI,CAACC,UAAU,GAAG,IAAIC,eAAe,EAAE,CAAA;MACvC,IAAIC,OAAO,GAAGA,MACZN,MAAM,CAAC,IAAIP,oBAAoB,CAAC,uBAAuB,CAAC,CAAC,CAAA;EAC3D,IAAA,IAAI,CAACc,mBAAmB,GAAG,MACzB,IAAI,CAACH,UAAU,CAACI,MAAM,CAAChL,mBAAmB,CAAC,OAAO,EAAE8K,OAAO,CAAC,CAAA;MAC9D,IAAI,CAACF,UAAU,CAACI,MAAM,CAACjL,gBAAgB,CAAC,OAAO,EAAE+K,OAAO,CAAC,CAAA;EAEzD,IAAA,IAAI,CAAC1I,IAAI,GAAGsD,MAAM,CAAC/L,OAAO,CAACyI,IAAI,CAAC,CAAC2C,MAAM,CACrC,CAACkG,GAAG,EAAAC,KAAA,KAAA;EAAA,MAAA,IAAE,CAACrQ,GAAG,EAAEoD,KAAK,CAAC,GAAAiN,KAAA,CAAA;EAAA,MAAA,OAChBxF,MAAM,CAAC7F,MAAM,CAACoL,GAAG,EAAE;UACjB,CAACpQ,GAAG,GAAG,IAAI,CAACsQ,YAAY,CAACtQ,GAAG,EAAEoD,KAAK,CAAA;EACrC,OAAC,CAAC,CAAA;OACJ,EAAA,EACF,CAAC,CAAA;MAED,IAAI,IAAI,CAACmN,IAAI,EAAE;EACb;QACA,IAAI,CAACL,mBAAmB,EAAE,CAAA;EAC5B,KAAA;MAEA,IAAI,CAACzB,IAAI,GAAGC,YAAY,CAAA;EAC1B,GAAA;EAEQ4B,EAAAA,YAAYA,CAClBtQ,GAAW,EACXoD,KAAiC,EACP;EAC1B,IAAA,IAAI,EAAEA,KAAK,YAAYyM,OAAO,CAAC,EAAE;EAC/B,MAAA,OAAOzM,KAAK,CAAA;EACd,KAAA;EAEA,IAAA,IAAI,CAACoM,YAAY,CAACtO,IAAI,CAAClB,GAAG,CAAC,CAAA;EAC3B,IAAA,IAAI,CAACsP,cAAc,CAACkB,GAAG,CAACxQ,GAAG,CAAC,CAAA;;EAE5B;EACA;MACA,IAAIyQ,OAAuB,GAAGZ,OAAO,CAACa,IAAI,CAAC,CAACtN,KAAK,EAAE,IAAI,CAACwM,YAAY,CAAC,CAAC,CAACe,IAAI,CACxEpJ,IAAI,IAAK,IAAI,CAACqJ,QAAQ,CAACH,OAAO,EAAEzQ,GAAG,EAAEZ,SAAS,EAAEmI,IAAe,CAAC,EAChE1C,KAAK,IAAK,IAAI,CAAC+L,QAAQ,CAACH,OAAO,EAAEzQ,GAAG,EAAE6E,KAAgB,CACzD,CAAC,CAAA;;EAED;EACA;EACA4L,IAAAA,OAAO,CAACI,KAAK,CAAC,MAAM,EAAE,CAAC,CAAA;EAEvBhG,IAAAA,MAAM,CAACiG,cAAc,CAACL,OAAO,EAAE,UAAU,EAAE;QAAEM,GAAG,EAAEA,MAAM,IAAA;EAAK,KAAC,CAAC,CAAA;EAC/D,IAAA,OAAON,OAAO,CAAA;EAChB,GAAA;IAEQG,QAAQA,CACdH,OAAuB,EACvBzQ,GAAW,EACX6E,KAAc,EACd0C,IAAc,EACL;MACT,IACE,IAAI,CAACwI,UAAU,CAACI,MAAM,CAACa,OAAO,IAC9BnM,KAAK,YAAYuK,oBAAoB,EACrC;QACA,IAAI,CAACc,mBAAmB,EAAE,CAAA;EAC1BrF,MAAAA,MAAM,CAACiG,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;UAAEM,GAAG,EAAEA,MAAMlM,KAAAA;EAAM,OAAC,CAAC,CAAA;EAC9D,MAAA,OAAOgL,OAAO,CAACF,MAAM,CAAC9K,KAAK,CAAC,CAAA;EAC9B,KAAA;EAEA,IAAA,IAAI,CAACyK,cAAc,CAAC2B,MAAM,CAACjR,GAAG,CAAC,CAAA;MAE/B,IAAI,IAAI,CAACuQ,IAAI,EAAE;EACb;QACA,IAAI,CAACL,mBAAmB,EAAE,CAAA;EAC5B,KAAA;;EAEA;EACA;EACA,IAAA,IAAIrL,KAAK,KAAKzF,SAAS,IAAImI,IAAI,KAAKnI,SAAS,EAAE;QAC7C,IAAI8R,cAAc,GAAG,IAAI5N,KAAK,CAC5B,0BAA0BtD,GAAAA,GAAG,gGAE/B,CAAC,CAAA;EACD6K,MAAAA,MAAM,CAACiG,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;UAAEM,GAAG,EAAEA,MAAMG,cAAAA;EAAe,OAAC,CAAC,CAAA;EACvE,MAAA,IAAI,CAACC,IAAI,CAAC,KAAK,EAAEnR,GAAG,CAAC,CAAA;EACrB,MAAA,OAAO6P,OAAO,CAACF,MAAM,CAACuB,cAAc,CAAC,CAAA;EACvC,KAAA;MAEA,IAAI3J,IAAI,KAAKnI,SAAS,EAAE;EACtByL,MAAAA,MAAM,CAACiG,cAAc,CAACL,OAAO,EAAE,QAAQ,EAAE;UAAEM,GAAG,EAAEA,MAAMlM,KAAAA;EAAM,OAAC,CAAC,CAAA;EAC9D,MAAA,IAAI,CAACsM,IAAI,CAAC,KAAK,EAAEnR,GAAG,CAAC,CAAA;EACrB,MAAA,OAAO6P,OAAO,CAACF,MAAM,CAAC9K,KAAK,CAAC,CAAA;EAC9B,KAAA;EAEAgG,IAAAA,MAAM,CAACiG,cAAc,CAACL,OAAO,EAAE,OAAO,EAAE;QAAEM,GAAG,EAAEA,MAAMxJ,IAAAA;EAAK,KAAC,CAAC,CAAA;EAC5D,IAAA,IAAI,CAAC4J,IAAI,CAAC,KAAK,EAAEnR,GAAG,CAAC,CAAA;EACrB,IAAA,OAAOuH,IAAI,CAAA;EACb,GAAA;EAEQ4J,EAAAA,IAAIA,CAACH,OAAgB,EAAEI,UAAmB,EAAE;EAClD,IAAA,IAAI,CAAC7B,WAAW,CAACnH,OAAO,CAAEiJ,UAAU,IAAKA,UAAU,CAACL,OAAO,EAAEI,UAAU,CAAC,CAAC,CAAA;EAC3E,GAAA;IAEAE,SAASA,CAAC1P,EAAmD,EAAE;EAC7D,IAAA,IAAI,CAAC2N,WAAW,CAACiB,GAAG,CAAC5O,EAAE,CAAC,CAAA;MACxB,OAAO,MAAM,IAAI,CAAC2N,WAAW,CAAC0B,MAAM,CAACrP,EAAE,CAAC,CAAA;EAC1C,GAAA;EAEA2P,EAAAA,MAAMA,GAAG;EACP,IAAA,IAAI,CAACxB,UAAU,CAACyB,KAAK,EAAE,CAAA;EACvB,IAAA,IAAI,CAAClC,cAAc,CAAClH,OAAO,CAAC,CAACiE,CAAC,EAAEoF,CAAC,KAAK,IAAI,CAACnC,cAAc,CAAC2B,MAAM,CAACQ,CAAC,CAAC,CAAC,CAAA;EACpE,IAAA,IAAI,CAACN,IAAI,CAAC,IAAI,CAAC,CAAA;EACjB,GAAA;IAEA,MAAMO,WAAWA,CAACvB,MAAmB,EAAE;MACrC,IAAIa,OAAO,GAAG,KAAK,CAAA;EACnB,IAAA,IAAI,CAAC,IAAI,CAACT,IAAI,EAAE;QACd,IAAIN,OAAO,GAAGA,MAAM,IAAI,CAACsB,MAAM,EAAE,CAAA;EACjCpB,MAAAA,MAAM,CAACjL,gBAAgB,CAAC,OAAO,EAAE+K,OAAO,CAAC,CAAA;EACzCe,MAAAA,OAAO,GAAG,MAAM,IAAInB,OAAO,CAAE8B,OAAO,IAAK;EACvC,QAAA,IAAI,CAACL,SAAS,CAAEN,OAAO,IAAK;EAC1Bb,UAAAA,MAAM,CAAChL,mBAAmB,CAAC,OAAO,EAAE8K,OAAO,CAAC,CAAA;EAC5C,UAAA,IAAIe,OAAO,IAAI,IAAI,CAACT,IAAI,EAAE;cACxBoB,OAAO,CAACX,OAAO,CAAC,CAAA;EAClB,WAAA;EACF,SAAC,CAAC,CAAA;EACJ,OAAC,CAAC,CAAA;EACJ,KAAA;EACA,IAAA,OAAOA,OAAO,CAAA;EAChB,GAAA;IAEA,IAAIT,IAAIA,GAAG;EACT,IAAA,OAAO,IAAI,CAACjB,cAAc,CAACsC,IAAI,KAAK,CAAC,CAAA;EACvC,GAAA;IAEA,IAAIC,aAAaA,GAAG;EAClB1O,IAAAA,SAAS,CACP,IAAI,CAACoE,IAAI,KAAK,IAAI,IAAI,IAAI,CAACgJ,IAAI,EAC/B,2DACF,CAAC,CAAA;EAED,IAAA,OAAO1F,MAAM,CAAC/L,OAAO,CAAC,IAAI,CAACyI,IAAI,CAAC,CAAC2C,MAAM,CACrC,CAACkG,GAAG,EAAA0B,KAAA,KAAA;EAAA,MAAA,IAAE,CAAC9R,GAAG,EAAEoD,KAAK,CAAC,GAAA0O,KAAA,CAAA;EAAA,MAAA,OAChBjH,MAAM,CAAC7F,MAAM,CAACoL,GAAG,EAAE;EACjB,QAAA,CAACpQ,GAAG,GAAG+R,oBAAoB,CAAC3O,KAAK,CAAA;EACnC,OAAC,CAAC,CAAA;OACJ,EAAA,EACF,CAAC,CAAA;EACH,GAAA;IAEA,IAAI4O,WAAWA,GAAG;EAChB,IAAA,OAAOvC,KAAK,CAACzB,IAAI,CAAC,IAAI,CAACsB,cAAc,CAAC,CAAA;EACxC,GAAA;EACF,CAAA;EAEA,SAAS2C,gBAAgBA,CAAC7O,KAAU,EAA2B;IAC7D,OACEA,KAAK,YAAYyM,OAAO,IAAKzM,KAAK,CAAoB8O,QAAQ,KAAK,IAAI,CAAA;EAE3E,CAAA;EAEA,SAASH,oBAAoBA,CAAC3O,KAAU,EAAE;EACxC,EAAA,IAAI,CAAC6O,gBAAgB,CAAC7O,KAAK,CAAC,EAAE;EAC5B,IAAA,OAAOA,KAAK,CAAA;EACd,GAAA;IAEA,IAAIA,KAAK,CAAC+O,MAAM,EAAE;MAChB,MAAM/O,KAAK,CAAC+O,MAAM,CAAA;EACpB,GAAA;IACA,OAAO/O,KAAK,CAACgP,KAAK,CAAA;EACpB,CAAA;EAOA;EACA;EACA;EACA;AACO,QAAMC,KAAoB,GAAG,SAAvBA,KAAoBA,CAAI9K,IAAI,EAAEkH,IAAI,EAAU;EAAA,EAAA,IAAdA,IAAI,KAAA,KAAA,CAAA,EAAA;MAAJA,IAAI,GAAG,EAAE,CAAA;EAAA,GAAA;EAClD,EAAA,IAAIC,YAAY,GAAG,OAAOD,IAAI,KAAK,QAAQ,GAAG;EAAEE,IAAAA,MAAM,EAAEF,IAAAA;EAAK,GAAC,GAAGA,IAAI,CAAA;EAErE,EAAA,OAAO,IAAIY,YAAY,CAAC9H,IAAI,EAAEmH,YAAY,CAAC,CAAA;EAC7C,EAAC;EAOD;EACA;EACA;EACA;AACO,QAAM4D,QAA0B,GAAG,SAA7BA,QAA0BA,CAAIxP,GAAG,EAAE2L,IAAI,EAAW;EAAA,EAAA,IAAfA,IAAI,KAAA,KAAA,CAAA,EAAA;EAAJA,IAAAA,IAAI,GAAG,GAAG,CAAA;EAAA,GAAA;IACxD,IAAIC,YAAY,GAAGD,IAAI,CAAA;EACvB,EAAA,IAAI,OAAOC,YAAY,KAAK,QAAQ,EAAE;EACpCA,IAAAA,YAAY,GAAG;EAAEC,MAAAA,MAAM,EAAED,YAAAA;OAAc,CAAA;KACxC,MAAM,IAAI,OAAOA,YAAY,CAACC,MAAM,KAAK,WAAW,EAAE;MACrDD,YAAY,CAACC,MAAM,GAAG,GAAG,CAAA;EAC3B,GAAA;IAEA,IAAIC,OAAO,GAAG,IAAIC,OAAO,CAACH,YAAY,CAACE,OAAO,CAAC,CAAA;EAC/CA,EAAAA,OAAO,CAACG,GAAG,CAAC,UAAU,EAAEjM,GAAG,CAAC,CAAA;EAE5B,EAAA,OAAO,IAAIkM,QAAQ,CAAC,IAAI,EAAA/K,QAAA,KACnByK,YAAY,EAAA;EACfE,IAAAA,OAAAA;EAAO,GAAA,CACR,CAAC,CAAA;EACJ,EAAC;;EAED;EACA;EACA;EACA;EACA;QACa2D,gBAAkC,GAAGA,CAACzP,GAAG,EAAE2L,IAAI,KAAK;EAC/D,EAAA,IAAI+D,QAAQ,GAAGF,QAAQ,CAACxP,GAAG,EAAE2L,IAAI,CAAC,CAAA;IAClC+D,QAAQ,CAAC5D,OAAO,CAACG,GAAG,CAAC,yBAAyB,EAAE,MAAM,CAAC,CAAA;EACvD,EAAA,OAAOyD,QAAQ,CAAA;EACjB,EAAC;;EAED;EACA;EACA;EACA;EACA;EACA;QACajR,OAAyB,GAAGA,CAACuB,GAAG,EAAE2L,IAAI,KAAK;EACtD,EAAA,IAAI+D,QAAQ,GAAGF,QAAQ,CAACxP,GAAG,EAAE2L,IAAI,CAAC,CAAA;IAClC+D,QAAQ,CAAC5D,OAAO,CAACG,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAA;EAC/C,EAAA,OAAOyD,QAAQ,CAAA;EACjB,EAAC;EAQD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACO,MAAMC,iBAAiB,CAA0B;IAOtDvD,WAAWA,CACTP,MAAc,EACd+D,UAA8B,EAC9BnL,IAAS,EACToL,QAAQ,EACR;EAAA,IAAA,IADAA,QAAQ,KAAA,KAAA,CAAA,EAAA;EAARA,MAAAA,QAAQ,GAAG,KAAK,CAAA;EAAA,KAAA;MAEhB,IAAI,CAAChE,MAAM,GAAGA,MAAM,CAAA;EACpB,IAAA,IAAI,CAAC+D,UAAU,GAAGA,UAAU,IAAI,EAAE,CAAA;MAClC,IAAI,CAACC,QAAQ,GAAGA,QAAQ,CAAA;MACxB,IAAIpL,IAAI,YAAYjE,KAAK,EAAE;EACzB,MAAA,IAAI,CAACiE,IAAI,GAAGA,IAAI,CAAC1D,QAAQ,EAAE,CAAA;QAC3B,IAAI,CAACgB,KAAK,GAAG0C,IAAI,CAAA;EACnB,KAAC,MAAM;QACL,IAAI,CAACA,IAAI,GAAGA,IAAI,CAAA;EAClB,KAAA;EACF,GAAA;EACF,CAAA;;EAEA;EACA;EACA;EACA;EACO,SAASqL,oBAAoBA,CAAC/N,KAAU,EAA0B;IACvE,OACEA,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,CAAC8J,MAAM,KAAK,QAAQ,IAChC,OAAO9J,KAAK,CAAC6N,UAAU,KAAK,QAAQ,IACpC,OAAO7N,KAAK,CAAC8N,QAAQ,KAAK,SAAS,IACnC,MAAM,IAAI9N,KAAK,CAAA;EAEnB;;ECjoDA;EACA;EACA;;EAEA;EACA;EACA;EA8NA;EACA;EACA;EACA;EAwEA;EACA;EACA;EAKA;EACA;EACA;EAUA;EACA;EACA;EAiBA;EACA;EACA;EAeA;EACA;EACA;EA0BA;EACA;EACA;EAYA;EACA;EACA;EACA;EAKA;EACA;EACA;EAOA;EAOA;EAQA;EASA;EACA;EACA;EAGA;EACA;EACA;EAGA;EACA;EACA;EAKA;EACA;EACA;EAGA;EACA;EACA;EAGA;EACA;EACA;EAGA;EACA;EACA;EAsCA;EACA;EACA;EAuGA;EACA;EACA;EACA;EAMA;EACA;EACA;EAQA,MAAMgO,uBAA6C,GAAG,CACpD,MAAM,EACN,KAAK,EACL,OAAO,EACP,QAAQ,CACT,CAAA;EACD,MAAMC,oBAAoB,GAAG,IAAIxN,GAAG,CAClCuN,uBACF,CAAC,CAAA;EAED,MAAME,sBAAoC,GAAG,CAC3C,KAAK,EACL,GAAGF,uBAAuB,CAC3B,CAAA;EACD,MAAMG,mBAAmB,GAAG,IAAI1N,GAAG,CAAayN,sBAAsB,CAAC,CAAA;EAEvE,MAAME,mBAAmB,GAAG,IAAI3N,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;EAC9D,MAAM4N,iCAAiC,GAAG,IAAI5N,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAA;AAEtD,QAAM6N,eAAyC,GAAG;EACvDhU,EAAAA,KAAK,EAAE,MAAM;EACbc,EAAAA,QAAQ,EAAEb,SAAS;EACnBgU,EAAAA,UAAU,EAAEhU,SAAS;EACrBiU,EAAAA,UAAU,EAAEjU,SAAS;EACrBkU,EAAAA,WAAW,EAAElU,SAAS;EACtBmU,EAAAA,QAAQ,EAAEnU,SAAS;EACnBoP,EAAAA,IAAI,EAAEpP,SAAS;EACfoU,EAAAA,IAAI,EAAEpU,SAAAA;EACR,EAAC;AAEM,QAAMqU,YAAmC,GAAG;EACjDtU,EAAAA,KAAK,EAAE,MAAM;EACboI,EAAAA,IAAI,EAAEnI,SAAS;EACfgU,EAAAA,UAAU,EAAEhU,SAAS;EACrBiU,EAAAA,UAAU,EAAEjU,SAAS;EACrBkU,EAAAA,WAAW,EAAElU,SAAS;EACtBmU,EAAAA,QAAQ,EAAEnU,SAAS;EACnBoP,EAAAA,IAAI,EAAEpP,SAAS;EACfoU,EAAAA,IAAI,EAAEpU,SAAAA;EACR,EAAC;AAEM,QAAMsU,YAA8B,GAAG;EAC5CvU,EAAAA,KAAK,EAAE,WAAW;EAClBwU,EAAAA,OAAO,EAAEvU,SAAS;EAClBwU,EAAAA,KAAK,EAAExU,SAAS;EAChBa,EAAAA,QAAQ,EAAEb,SAAAA;EACZ,EAAC;EAED,MAAMyU,kBAAkB,GAAG,+BAA+B,CAAA;EAE1D,MAAMC,yBAAqD,GAAItO,KAAK,KAAM;EACxEuO,EAAAA,gBAAgB,EAAEC,OAAO,CAACxO,KAAK,CAACuO,gBAAgB,CAAA;EAClD,CAAC,CAAC,CAAA;EAEF,MAAME,uBAAuB,GAAG,0BAA0B,CAAA;;EAE1D;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACO,SAASC,YAAYA,CAACzF,IAAgB,EAAU;EACrD,EAAA,MAAM0F,YAAY,GAAG1F,IAAI,CAAC1M,MAAM,GAC5B0M,IAAI,CAAC1M,MAAM,GACX,OAAOA,MAAM,KAAK,WAAW,GAC7BA,MAAM,GACN3C,SAAS,CAAA;IACb,MAAMgV,SAAS,GACb,OAAOD,YAAY,KAAK,WAAW,IACnC,OAAOA,YAAY,CAACzR,QAAQ,KAAK,WAAW,IAC5C,OAAOyR,YAAY,CAACzR,QAAQ,CAAC2R,aAAa,KAAK,WAAW,CAAA;IAC5D,MAAMC,QAAQ,GAAG,CAACF,SAAS,CAAA;IAE3BjR,SAAS,CACPsL,IAAI,CAAC/I,MAAM,CAACpG,MAAM,GAAG,CAAC,EACtB,2DACF,CAAC,CAAA;EAED,EAAA,IAAIqG,kBAA8C,CAAA;IAClD,IAAI8I,IAAI,CAAC9I,kBAAkB,EAAE;MAC3BA,kBAAkB,GAAG8I,IAAI,CAAC9I,kBAAkB,CAAA;EAC9C,GAAC,MAAM,IAAI8I,IAAI,CAAC8F,mBAAmB,EAAE;EACnC;EACA,IAAA,IAAIA,mBAAmB,GAAG9F,IAAI,CAAC8F,mBAAmB,CAAA;MAClD5O,kBAAkB,GAAIH,KAAK,KAAM;QAC/BuO,gBAAgB,EAAEQ,mBAAmB,CAAC/O,KAAK,CAAA;EAC7C,KAAC,CAAC,CAAA;EACJ,GAAC,MAAM;EACLG,IAAAA,kBAAkB,GAAGmO,yBAAyB,CAAA;EAChD,GAAA;;EAEA;IACA,IAAIjO,QAAuB,GAAG,EAAE,CAAA;EAChC;EACA,EAAA,IAAI2O,UAAU,GAAG/O,yBAAyB,CACxCgJ,IAAI,CAAC/I,MAAM,EACXC,kBAAkB,EAClBvG,SAAS,EACTyG,QACF,CAAC,CAAA;EACD,EAAA,IAAI4O,kBAAyD,CAAA;EAC7D,EAAA,IAAIlO,QAAQ,GAAGkI,IAAI,CAAClI,QAAQ,IAAI,GAAG,CAAA;EACnC,EAAA,IAAImO,gBAAgB,GAAGjG,IAAI,CAACkG,YAAY,IAAIC,mBAAmB,CAAA;EAC/D,EAAA,IAAIC,2BAA2B,GAAGpG,IAAI,CAACqG,uBAAuB,CAAA;;EAE9D;IACA,IAAIC,MAAoB,GAAA9Q,QAAA,CAAA;EACtB+Q,IAAAA,iBAAiB,EAAE,KAAK;EACxBC,IAAAA,sBAAsB,EAAE,KAAK;EAC7BC,IAAAA,mBAAmB,EAAE,KAAK;EAC1BC,IAAAA,kBAAkB,EAAE,KAAK;EACzB3H,IAAAA,oBAAoB,EAAE,KAAK;EAC3B4H,IAAAA,8BAA8B,EAAE,KAAA;KAC7B3G,EAAAA,IAAI,CAACsG,MAAM,CACf,CAAA;EACD;IACA,IAAIM,eAAoC,GAAG,IAAI,CAAA;EAC/C;EACA,EAAA,IAAI9F,WAAW,GAAG,IAAIjK,GAAG,EAAoB,CAAA;EAC7C;IACA,IAAIgQ,oBAAmD,GAAG,IAAI,CAAA;EAC9D;IACA,IAAIC,uBAA+D,GAAG,IAAI,CAAA;EAC1E;IACA,IAAIC,iBAAmD,GAAG,IAAI,CAAA;EAC9D;EACA;EACA;EACA;EACA;EACA;EACA,EAAA,IAAIC,qBAAqB,GAAGhH,IAAI,CAACiH,aAAa,IAAI,IAAI,CAAA;EAEtD,EAAA,IAAIC,cAAc,GAAGtP,WAAW,CAACmO,UAAU,EAAE/F,IAAI,CAAC/N,OAAO,CAACT,QAAQ,EAAEsG,QAAQ,CAAC,CAAA;IAC7E,IAAIqP,mBAAmB,GAAG,KAAK,CAAA;IAC/B,IAAIC,aAA+B,GAAG,IAAI,CAAA;EAE1C,EAAA,IAAIF,cAAc,IAAI,IAAI,IAAI,CAACd,2BAA2B,EAAE;EAC1D;EACA;EACA,IAAA,IAAIhQ,KAAK,GAAGiR,sBAAsB,CAAC,GAAG,EAAE;EACtC3V,MAAAA,QAAQ,EAAEsO,IAAI,CAAC/N,OAAO,CAACT,QAAQ,CAACE,QAAAA;EAClC,KAAC,CAAC,CAAA;MACF,IAAI;QAAE2G,OAAO;EAAEtB,MAAAA,KAAAA;EAAM,KAAC,GAAGuQ,sBAAsB,CAACvB,UAAU,CAAC,CAAA;EAC3DmB,IAAAA,cAAc,GAAG7O,OAAO,CAAA;EACxB+O,IAAAA,aAAa,GAAG;QAAE,CAACrQ,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;OAAO,CAAA;EACvC,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA,EAAA,IAAI8Q,cAAc,IAAI,CAAClH,IAAI,CAACiH,aAAa,EAAE;EACzC,IAAA,IAAIM,QAAQ,GAAGC,aAAa,CAC1BN,cAAc,EACdnB,UAAU,EACV/F,IAAI,CAAC/N,OAAO,CAACT,QAAQ,CAACE,QACxB,CAAC,CAAA;MACD,IAAI6V,QAAQ,CAACE,MAAM,EAAE;EACnBP,MAAAA,cAAc,GAAG,IAAI,CAAA;EACvB,KAAA;EACF,GAAA;EAEA,EAAA,IAAIQ,WAAoB,CAAA;IACxB,IAAI,CAACR,cAAc,EAAE;EACnBQ,IAAAA,WAAW,GAAG,KAAK,CAAA;EACnBR,IAAAA,cAAc,GAAG,EAAE,CAAA;;EAEnB;EACA;EACA;MACA,IAAIZ,MAAM,CAACG,mBAAmB,EAAE;EAC9B,MAAA,IAAIc,QAAQ,GAAGC,aAAa,CAC1B,IAAI,EACJzB,UAAU,EACV/F,IAAI,CAAC/N,OAAO,CAACT,QAAQ,CAACE,QACxB,CAAC,CAAA;EACD,MAAA,IAAI6V,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAAClP,OAAO,EAAE;EACvC8O,QAAAA,mBAAmB,GAAG,IAAI,CAAA;UAC1BD,cAAc,GAAGK,QAAQ,CAAClP,OAAO,CAAA;EACnC,OAAA;EACF,KAAA;EACF,GAAC,MAAM,IAAI6O,cAAc,CAAC3L,IAAI,CAAEoM,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAAC6Q,IAAI,CAAC,EAAE;EACnD;EACA;EACAF,IAAAA,WAAW,GAAG,KAAK,CAAA;EACrB,GAAC,MAAM,IAAI,CAACR,cAAc,CAAC3L,IAAI,CAAEoM,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAAC8Q,MAAM,CAAC,EAAE;EACtD;EACAH,IAAAA,WAAW,GAAG,IAAI,CAAA;EACpB,GAAC,MAAM,IAAIpB,MAAM,CAACG,mBAAmB,EAAE;EACrC;EACA;EACA;EACA,IAAA,IAAI7N,UAAU,GAAGoH,IAAI,CAACiH,aAAa,GAAGjH,IAAI,CAACiH,aAAa,CAACrO,UAAU,GAAG,IAAI,CAAA;EAC1E,IAAA,IAAIkP,MAAM,GAAG9H,IAAI,CAACiH,aAAa,GAAGjH,IAAI,CAACiH,aAAa,CAACa,MAAM,GAAG,IAAI,CAAA;EAClE;EACA,IAAA,IAAIA,MAAM,EAAE;EACV,MAAA,IAAIxS,GAAG,GAAG4R,cAAc,CAACa,SAAS,CAC/BJ,CAAC,IAAKG,MAAM,CAAEH,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,CAAC,KAAK5G,SACjC,CAAC,CAAA;QACD+W,WAAW,GAAGR,cAAc,CACzB1S,KAAK,CAAC,CAAC,EAAEc,GAAG,GAAG,CAAC,CAAC,CACjBuG,KAAK,CAAE8L,CAAC,IAAK,CAACK,0BAA0B,CAACL,CAAC,CAAC5Q,KAAK,EAAE6B,UAAU,EAAEkP,MAAM,CAAC,CAAC,CAAA;EAC3E,KAAC,MAAM;EACLJ,MAAAA,WAAW,GAAGR,cAAc,CAACrL,KAAK,CAC/B8L,CAAC,IAAK,CAACK,0BAA0B,CAACL,CAAC,CAAC5Q,KAAK,EAAE6B,UAAU,EAAEkP,MAAM,CAChE,CAAC,CAAA;EACH,KAAA;EACF,GAAC,MAAM;EACL;EACA;EACAJ,IAAAA,WAAW,GAAG1H,IAAI,CAACiH,aAAa,IAAI,IAAI,CAAA;EAC1C,GAAA;EAEA,EAAA,IAAIgB,MAAc,CAAA;EAClB,EAAA,IAAIvX,KAAkB,GAAG;EACvBwX,IAAAA,aAAa,EAAElI,IAAI,CAAC/N,OAAO,CAACnB,MAAM;EAClCU,IAAAA,QAAQ,EAAEwO,IAAI,CAAC/N,OAAO,CAACT,QAAQ;EAC/B6G,IAAAA,OAAO,EAAE6O,cAAc;MACvBQ,WAAW;EACXS,IAAAA,UAAU,EAAEzD,eAAe;EAC3B;MACA0D,qBAAqB,EAAEpI,IAAI,CAACiH,aAAa,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI;EAChEoB,IAAAA,kBAAkB,EAAE,KAAK;EACzBC,IAAAA,YAAY,EAAE,MAAM;EACpB1P,IAAAA,UAAU,EAAGoH,IAAI,CAACiH,aAAa,IAAIjH,IAAI,CAACiH,aAAa,CAACrO,UAAU,IAAK,EAAE;MACvE2P,UAAU,EAAGvI,IAAI,CAACiH,aAAa,IAAIjH,IAAI,CAACiH,aAAa,CAACsB,UAAU,IAAK,IAAI;MACzET,MAAM,EAAG9H,IAAI,CAACiH,aAAa,IAAIjH,IAAI,CAACiH,aAAa,CAACa,MAAM,IAAKV,aAAa;EAC1EoB,IAAAA,QAAQ,EAAE,IAAIC,GAAG,EAAE;MACnBC,QAAQ,EAAE,IAAID,GAAG,EAAC;KACnB,CAAA;;EAED;EACA;EACA,EAAA,IAAIE,aAA4B,GAAGC,MAAa,CAAC7X,GAAG,CAAA;;EAEpD;EACA;IACA,IAAI8X,yBAAyB,GAAG,KAAK,CAAA;;EAErC;EACA,EAAA,IAAIC,2BAAmD,CAAA;;EAEvD;IACA,IAAIC,4BAA4B,GAAG,KAAK,CAAA;;EAExC;EACA,EAAA,IAAIC,sBAAgD,GAAG,IAAIP,GAAG,EAG3D,CAAA;;EAEH;IACA,IAAIQ,2BAAgD,GAAG,IAAI,CAAA;;EAE3D;EACA;IACA,IAAIC,2BAA2B,GAAG,KAAK,CAAA;;EAEvC;EACA;EACA;EACA;IACA,IAAIC,sBAAsB,GAAG,KAAK,CAAA;;EAElC;EACA;IACA,IAAIC,uBAAiC,GAAG,EAAE,CAAA;;EAE1C;EACA;EACA,EAAA,IAAIC,qBAAkC,GAAG,IAAIxS,GAAG,EAAE,CAAA;;EAElD;EACA,EAAA,IAAIyS,gBAAgB,GAAG,IAAIb,GAAG,EAA2B,CAAA;;EAEzD;IACA,IAAIc,kBAAkB,GAAG,CAAC,CAAA;;EAE1B;EACA;EACA;IACA,IAAIC,uBAAuB,GAAG,CAAC,CAAC,CAAA;;EAEhC;EACA,EAAA,IAAIC,cAAc,GAAG,IAAIhB,GAAG,EAAkB,CAAA;;EAE9C;EACA,EAAA,IAAIiB,gBAAgB,GAAG,IAAI7S,GAAG,EAAU,CAAA;;EAExC;EACA,EAAA,IAAI8S,gBAAgB,GAAG,IAAIlB,GAAG,EAA0B,CAAA;;EAExD;EACA,EAAA,IAAImB,cAAc,GAAG,IAAInB,GAAG,EAAkB,CAAA;;EAE9C;EACA;EACA,EAAA,IAAIoB,eAAe,GAAG,IAAIhT,GAAG,EAAU,CAAA;;EAEvC;EACA;EACA;EACA;EACA,EAAA,IAAIiT,eAAe,GAAG,IAAIrB,GAAG,EAAwB,CAAA;;EAErD;EACA;EACA,EAAA,IAAIsB,gBAAgB,GAAG,IAAItB,GAAG,EAA2B,CAAA;;EASzD;EACA;IACA,IAAIuB,2BAAqD,GAAGrZ,SAAS,CAAA;;EAErE;EACA;EACA;IACA,SAASsZ,UAAUA,GAAG;EACpB;EACA;MACArD,eAAe,GAAG5G,IAAI,CAAC/N,OAAO,CAACiB,MAAM,CACnCuC,IAAA,IAAgD;QAAA,IAA/C;EAAE3E,QAAAA,MAAM,EAAEoX,aAAa;UAAE1W,QAAQ;EAAEqB,QAAAA,KAAAA;EAAM,OAAC,GAAA4C,IAAA,CAAA;EACzC;EACA;EACA,MAAA,IAAIuU,2BAA2B,EAAE;EAC/BA,QAAAA,2BAA2B,EAAE,CAAA;EAC7BA,QAAAA,2BAA2B,GAAGrZ,SAAS,CAAA;EACvC,QAAA,OAAA;EACF,OAAA;QAEAgB,OAAO,CACLoY,gBAAgB,CAAC5G,IAAI,KAAK,CAAC,IAAItQ,KAAK,IAAI,IAAI,EAC5C,oEAAoE,GAClE,wEAAwE,GACxE,uEAAuE,GACvE,yEAAyE,GACzE,iEAAiE,GACjE,yDACJ,CAAC,CAAA;QAED,IAAIqX,UAAU,GAAGC,qBAAqB,CAAC;UACrCC,eAAe,EAAE1Z,KAAK,CAACc,QAAQ;EAC/BmB,QAAAA,YAAY,EAAEnB,QAAQ;EACtB0W,QAAAA,aAAAA;EACF,OAAC,CAAC,CAAA;EAEF,MAAA,IAAIgC,UAAU,IAAIrX,KAAK,IAAI,IAAI,EAAE;EAC/B;EACA,QAAA,IAAIwX,wBAAwB,GAAG,IAAIjJ,OAAO,CAAQ8B,OAAO,IAAK;EAC5D8G,UAAAA,2BAA2B,GAAG9G,OAAO,CAAA;EACvC,SAAC,CAAC,CAAA;UACFlD,IAAI,CAAC/N,OAAO,CAACe,EAAE,CAACH,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;;EAE3B;UACAyX,aAAa,CAACJ,UAAU,EAAE;EACxBxZ,UAAAA,KAAK,EAAE,SAAS;YAChBc,QAAQ;EACR0T,UAAAA,OAAOA,GAAG;cACRoF,aAAa,CAACJ,UAAU,EAAG;EACzBxZ,cAAAA,KAAK,EAAE,YAAY;EACnBwU,cAAAA,OAAO,EAAEvU,SAAS;EAClBwU,cAAAA,KAAK,EAAExU,SAAS;EAChBa,cAAAA,QAAAA;EACF,aAAC,CAAC,CAAA;EACF;EACA;EACA;EACA6Y,YAAAA,wBAAwB,CAACnI,IAAI,CAAC,MAAMlC,IAAI,CAAC/N,OAAO,CAACe,EAAE,CAACH,KAAK,CAAC,CAAC,CAAA;aAC5D;EACDsS,UAAAA,KAAKA,GAAG;cACN,IAAIuD,QAAQ,GAAG,IAAID,GAAG,CAAC/X,KAAK,CAACgY,QAAQ,CAAC,CAAA;EACtCA,YAAAA,QAAQ,CAACpI,GAAG,CAAC4J,UAAU,EAAGjF,YAAY,CAAC,CAAA;EACvCsF,YAAAA,WAAW,CAAC;EAAE7B,cAAAA,QAAAA;EAAS,aAAC,CAAC,CAAA;EAC3B,WAAA;EACF,SAAC,CAAC,CAAA;EACF,QAAA,OAAA;EACF,OAAA;EAEA,MAAA,OAAO8B,eAAe,CAACtC,aAAa,EAAE1W,QAAQ,CAAC,CAAA;EACjD,KACF,CAAC,CAAA;EAED,IAAA,IAAImU,SAAS,EAAE;EACb;EACA;EACA8E,MAAAA,yBAAyB,CAAC/E,YAAY,EAAEsD,sBAAsB,CAAC,CAAA;QAC/D,IAAI0B,uBAAuB,GAAGA,MAC5BC,yBAAyB,CAACjF,YAAY,EAAEsD,sBAAsB,CAAC,CAAA;EACjEtD,MAAAA,YAAY,CAACjP,gBAAgB,CAAC,UAAU,EAAEiU,uBAAuB,CAAC,CAAA;QAClEzB,2BAA2B,GAAGA,MAC5BvD,YAAY,CAAChP,mBAAmB,CAAC,UAAU,EAAEgU,uBAAuB,CAAC,CAAA;EACzE,KAAA;;EAEA;EACA;EACA;EACA;EACA;EACA,IAAA,IAAI,CAACha,KAAK,CAACgX,WAAW,EAAE;QACtB8C,eAAe,CAAC5B,MAAa,CAAC7X,GAAG,EAAEL,KAAK,CAACc,QAAQ,EAAE;EACjDoZ,QAAAA,gBAAgB,EAAE,IAAA;EACpB,OAAC,CAAC,CAAA;EACJ,KAAA;EAEA,IAAA,OAAO3C,MAAM,CAAA;EACf,GAAA;;EAEA;IACA,SAAS4C,OAAOA,GAAG;EACjB,IAAA,IAAIjE,eAAe,EAAE;EACnBA,MAAAA,eAAe,EAAE,CAAA;EACnB,KAAA;EACA,IAAA,IAAIqC,2BAA2B,EAAE;EAC/BA,MAAAA,2BAA2B,EAAE,CAAA;EAC/B,KAAA;MACAnI,WAAW,CAACgK,KAAK,EAAE,CAAA;EACnBhC,IAAAA,2BAA2B,IAAIA,2BAA2B,CAAC/F,KAAK,EAAE,CAAA;EAClErS,IAAAA,KAAK,CAAC8X,QAAQ,CAAC7O,OAAO,CAAC,CAAC+D,CAAC,EAAEnM,GAAG,KAAKwZ,aAAa,CAACxZ,GAAG,CAAC,CAAC,CAAA;EACtDb,IAAAA,KAAK,CAACgY,QAAQ,CAAC/O,OAAO,CAAC,CAAC+D,CAAC,EAAEnM,GAAG,KAAKyZ,aAAa,CAACzZ,GAAG,CAAC,CAAC,CAAA;EACxD,GAAA;;EAEA;IACA,SAASsR,SAASA,CAAC1P,EAAoB,EAAE;EACvC2N,IAAAA,WAAW,CAACiB,GAAG,CAAC5O,EAAE,CAAC,CAAA;EACnB,IAAA,OAAO,MAAM2N,WAAW,CAAC0B,MAAM,CAACrP,EAAE,CAAC,CAAA;EACrC,GAAA;;EAEA;EACA,EAAA,SAASoX,WAAWA,CAClBU,QAA8B,EAC9BC,IAGC,EACK;EAAA,IAAA,IAJNA,IAGC,KAAA,KAAA,CAAA,EAAA;QAHDA,IAGC,GAAG,EAAE,CAAA;EAAA,KAAA;EAENxa,IAAAA,KAAK,GAAA8E,QAAA,CAAA,EAAA,EACA9E,KAAK,EACLua,QAAQ,CACZ,CAAA;;EAED;EACA;MACA,IAAIE,iBAA2B,GAAG,EAAE,CAAA;MACpC,IAAIC,mBAA6B,GAAG,EAAE,CAAA;MAEtC,IAAI9E,MAAM,CAACC,iBAAiB,EAAE;QAC5B7V,KAAK,CAAC8X,QAAQ,CAAC7O,OAAO,CAAC,CAAC0R,OAAO,EAAE9Z,GAAG,KAAK;EACvC,QAAA,IAAI8Z,OAAO,CAAC3a,KAAK,KAAK,MAAM,EAAE;EAC5B,UAAA,IAAImZ,eAAe,CAACxJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;EAC5B;EACA6Z,YAAAA,mBAAmB,CAAC3Y,IAAI,CAAClB,GAAG,CAAC,CAAA;EAC/B,WAAC,MAAM;EACL;EACA;EACA4Z,YAAAA,iBAAiB,CAAC1Y,IAAI,CAAClB,GAAG,CAAC,CAAA;EAC7B,WAAA;EACF,SAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAA;;EAEA;EACA;EACAsY,IAAAA,eAAe,CAAClQ,OAAO,CAAEpI,GAAG,IAAK;EAC/B,MAAA,IAAI,CAACb,KAAK,CAAC8X,QAAQ,CAACnI,GAAG,CAAC9O,GAAG,CAAC,IAAI,CAAC+X,gBAAgB,CAACjJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;EAC1D6Z,QAAAA,mBAAmB,CAAC3Y,IAAI,CAAClB,GAAG,CAAC,CAAA;EAC/B,OAAA;EACF,KAAC,CAAC,CAAA;;EAEF;EACA;EACA;MACA,CAAC,GAAGuP,WAAW,CAAC,CAACnH,OAAO,CAAEiJ,UAAU,IAClCA,UAAU,CAAClS,KAAK,EAAE;EAChBmZ,MAAAA,eAAe,EAAEuB,mBAAmB;QACpCE,kBAAkB,EAAEJ,IAAI,CAACI,kBAAkB;EAC3CC,MAAAA,SAAS,EAAEL,IAAI,CAACK,SAAS,KAAK,IAAA;EAChC,KAAC,CACH,CAAC,CAAA;;EAED;MACA,IAAIjF,MAAM,CAACC,iBAAiB,EAAE;EAC5B4E,MAAAA,iBAAiB,CAACxR,OAAO,CAAEpI,GAAG,IAAKb,KAAK,CAAC8X,QAAQ,CAAChG,MAAM,CAACjR,GAAG,CAAC,CAAC,CAAA;QAC9D6Z,mBAAmB,CAACzR,OAAO,CAAEpI,GAAG,IAAKwZ,aAAa,CAACxZ,GAAG,CAAC,CAAC,CAAA;EAC1D,KAAC,MAAM;EACL;EACA;QACA6Z,mBAAmB,CAACzR,OAAO,CAAEpI,GAAG,IAAKsY,eAAe,CAACrH,MAAM,CAACjR,GAAG,CAAC,CAAC,CAAA;EACnE,KAAA;EACF,GAAA;;EAEA;EACA;EACA;EACA;EACA;EACA,EAAA,SAASia,kBAAkBA,CACzBha,QAAkB,EAClByZ,QAA0E,EAAAQ,KAAA,EAEpE;MAAA,IAAAC,eAAA,EAAAC,gBAAA,CAAA;MAAA,IADN;EAAEJ,MAAAA,SAAAA;EAAmC,KAAC,GAAAE,KAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,KAAA,CAAA;EAE3C;EACA;EACA;EACA;EACA;MACA,IAAIG,cAAc,GAChBlb,KAAK,CAAC6X,UAAU,IAAI,IAAI,IACxB7X,KAAK,CAACyX,UAAU,CAACxD,UAAU,IAAI,IAAI,IACnCkH,gBAAgB,CAACnb,KAAK,CAACyX,UAAU,CAACxD,UAAU,CAAC,IAC7CjU,KAAK,CAACyX,UAAU,CAACzX,KAAK,KAAK,SAAS,IACpC,CAAA,CAAAgb,eAAA,GAAAla,QAAQ,CAACd,KAAK,KAAA,IAAA,GAAA,KAAA,CAAA,GAAdgb,eAAA,CAAgBI,WAAW,MAAK,IAAI,CAAA;EAEtC,IAAA,IAAIvD,UAA4B,CAAA;MAChC,IAAI0C,QAAQ,CAAC1C,UAAU,EAAE;EACvB,MAAA,IAAInM,MAAM,CAAC2P,IAAI,CAACd,QAAQ,CAAC1C,UAAU,CAAC,CAAC1X,MAAM,GAAG,CAAC,EAAE;UAC/C0X,UAAU,GAAG0C,QAAQ,CAAC1C,UAAU,CAAA;EAClC,OAAC,MAAM;EACL;EACAA,QAAAA,UAAU,GAAG,IAAI,CAAA;EACnB,OAAA;OACD,MAAM,IAAIqD,cAAc,EAAE;EACzB;QACArD,UAAU,GAAG7X,KAAK,CAAC6X,UAAU,CAAA;EAC/B,KAAC,MAAM;EACL;EACAA,MAAAA,UAAU,GAAG,IAAI,CAAA;EACnB,KAAA;;EAEA;EACA,IAAA,IAAI3P,UAAU,GAAGqS,QAAQ,CAACrS,UAAU,GAChCoT,eAAe,CACbtb,KAAK,CAACkI,UAAU,EAChBqS,QAAQ,CAACrS,UAAU,EACnBqS,QAAQ,CAAC5S,OAAO,IAAI,EAAE,EACtB4S,QAAQ,CAACnD,MACX,CAAC,GACDpX,KAAK,CAACkI,UAAU,CAAA;;EAEpB;EACA;EACA,IAAA,IAAI8P,QAAQ,GAAGhY,KAAK,CAACgY,QAAQ,CAAA;EAC7B,IAAA,IAAIA,QAAQ,CAACvF,IAAI,GAAG,CAAC,EAAE;EACrBuF,MAAAA,QAAQ,GAAG,IAAID,GAAG,CAACC,QAAQ,CAAC,CAAA;EAC5BA,MAAAA,QAAQ,CAAC/O,OAAO,CAAC,CAAC+D,CAAC,EAAEsF,CAAC,KAAK0F,QAAQ,CAACpI,GAAG,CAAC0C,CAAC,EAAEiC,YAAY,CAAC,CAAC,CAAA;EAC3D,KAAA;;EAEA;EACA;EACA,IAAA,IAAIoD,kBAAkB,GACpBQ,yBAAyB,KAAK,IAAI,IACjCnY,KAAK,CAACyX,UAAU,CAACxD,UAAU,IAAI,IAAI,IAClCkH,gBAAgB,CAACnb,KAAK,CAACyX,UAAU,CAACxD,UAAU,CAAC,IAC7C,EAAAgH,gBAAA,GAAAna,QAAQ,CAACd,KAAK,KAAdib,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,gBAAA,CAAgBG,WAAW,MAAK,IAAK,CAAA;;EAEzC;EACA,IAAA,IAAI9F,kBAAkB,EAAE;EACtBD,MAAAA,UAAU,GAAGC,kBAAkB,CAAA;EAC/BA,MAAAA,kBAAkB,GAAGrV,SAAS,CAAA;EAChC,KAAA;EAEA,IAAA,IAAIuY,2BAA2B,EAAE,CAEhC,MAAM,IAAIP,aAAa,KAAKC,MAAa,CAAC7X,GAAG,EAAE,CAE/C,MAAM,IAAI4X,aAAa,KAAKC,MAAa,CAAClW,IAAI,EAAE;QAC/CsN,IAAI,CAAC/N,OAAO,CAACQ,IAAI,CAACjB,QAAQ,EAAEA,QAAQ,CAACd,KAAK,CAAC,CAAA;EAC7C,KAAC,MAAM,IAAIiY,aAAa,KAAKC,MAAa,CAAC7V,OAAO,EAAE;QAClDiN,IAAI,CAAC/N,OAAO,CAACa,OAAO,CAACtB,QAAQ,EAAEA,QAAQ,CAACd,KAAK,CAAC,CAAA;EAChD,KAAA;EAEA,IAAA,IAAI4a,kBAAkD,CAAA;;EAEtD;EACA,IAAA,IAAI3C,aAAa,KAAKC,MAAa,CAAC7X,GAAG,EAAE;EACvC;QACA,IAAIkb,UAAU,GAAGjD,sBAAsB,CAAC1G,GAAG,CAAC5R,KAAK,CAACc,QAAQ,CAACE,QAAQ,CAAC,CAAA;QACpE,IAAIua,UAAU,IAAIA,UAAU,CAAC5L,GAAG,CAAC7O,QAAQ,CAACE,QAAQ,CAAC,EAAE;EACnD4Z,QAAAA,kBAAkB,GAAG;YACnBlB,eAAe,EAAE1Z,KAAK,CAACc,QAAQ;EAC/BmB,UAAAA,YAAY,EAAEnB,QAAAA;WACf,CAAA;SACF,MAAM,IAAIwX,sBAAsB,CAAC3I,GAAG,CAAC7O,QAAQ,CAACE,QAAQ,CAAC,EAAE;EACxD;EACA;EACA4Z,QAAAA,kBAAkB,GAAG;EACnBlB,UAAAA,eAAe,EAAE5Y,QAAQ;YACzBmB,YAAY,EAAEjC,KAAK,CAACc,QAAAA;WACrB,CAAA;EACH,OAAA;OACD,MAAM,IAAIuX,4BAA4B,EAAE;EACvC;QACA,IAAImD,OAAO,GAAGlD,sBAAsB,CAAC1G,GAAG,CAAC5R,KAAK,CAACc,QAAQ,CAACE,QAAQ,CAAC,CAAA;EACjE,MAAA,IAAIwa,OAAO,EAAE;EACXA,QAAAA,OAAO,CAACnK,GAAG,CAACvQ,QAAQ,CAACE,QAAQ,CAAC,CAAA;EAChC,OAAC,MAAM;UACLwa,OAAO,GAAG,IAAIrV,GAAG,CAAS,CAACrF,QAAQ,CAACE,QAAQ,CAAC,CAAC,CAAA;UAC9CsX,sBAAsB,CAAC1I,GAAG,CAAC5P,KAAK,CAACc,QAAQ,CAACE,QAAQ,EAAEwa,OAAO,CAAC,CAAA;EAC9D,OAAA;EACAZ,MAAAA,kBAAkB,GAAG;UACnBlB,eAAe,EAAE1Z,KAAK,CAACc,QAAQ;EAC/BmB,QAAAA,YAAY,EAAEnB,QAAAA;SACf,CAAA;EACH,KAAA;MAEA+Y,WAAW,CAAA/U,QAAA,CAAA,EAAA,EAEJyV,QAAQ,EAAA;EAAE;QACb1C,UAAU;QACV3P,UAAU;EACVsP,MAAAA,aAAa,EAAES,aAAa;QAC5BnX,QAAQ;EACRkW,MAAAA,WAAW,EAAE,IAAI;EACjBS,MAAAA,UAAU,EAAEzD,eAAe;EAC3B4D,MAAAA,YAAY,EAAE,MAAM;EACpBF,MAAAA,qBAAqB,EAAE+D,sBAAsB,CAC3C3a,QAAQ,EACRyZ,QAAQ,CAAC5S,OAAO,IAAI3H,KAAK,CAAC2H,OAC5B,CAAC;QACDgQ,kBAAkB;EAClBK,MAAAA,QAAAA;OAEF,CAAA,EAAA;QACE4C,kBAAkB;QAClBC,SAAS,EAAEA,SAAS,KAAK,IAAA;EAC3B,KACF,CAAC,CAAA;;EAED;MACA5C,aAAa,GAAGC,MAAa,CAAC7X,GAAG,CAAA;EACjC8X,IAAAA,yBAAyB,GAAG,KAAK,CAAA;EACjCE,IAAAA,4BAA4B,GAAG,KAAK,CAAA;EACpCG,IAAAA,2BAA2B,GAAG,KAAK,CAAA;EACnCC,IAAAA,sBAAsB,GAAG,KAAK,CAAA;EAC9BC,IAAAA,uBAAuB,GAAG,EAAE,CAAA;EAC9B,GAAA;;EAEA;EACA;EACA,EAAA,eAAegD,QAAQA,CACrB9a,EAAsB,EACtB4Z,IAA4B,EACb;EACf,IAAA,IAAI,OAAO5Z,EAAE,KAAK,QAAQ,EAAE;EAC1B0O,MAAAA,IAAI,CAAC/N,OAAO,CAACe,EAAE,CAAC1B,EAAE,CAAC,CAAA;EACnB,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAI+a,cAAc,GAAGC,WAAW,CAC9B5b,KAAK,CAACc,QAAQ,EACdd,KAAK,CAAC2H,OAAO,EACbP,QAAQ,EACRwO,MAAM,CAACI,kBAAkB,EACzBpV,EAAE,EACFgV,MAAM,CAACvH,oBAAoB,EAC3BmM,IAAI,IAAJA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEqB,WAAW,EACjBrB,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAEsB,QACR,CAAC,CAAA;MACD,IAAI;QAAEna,IAAI;QAAEoa,UAAU;EAAErW,MAAAA,KAAAA;EAAM,KAAC,GAAGsW,wBAAwB,CACxDpG,MAAM,CAACE,sBAAsB,EAC7B,KAAK,EACL6F,cAAc,EACdnB,IACF,CAAC,CAAA;EAED,IAAA,IAAId,eAAe,GAAG1Z,KAAK,CAACc,QAAQ,CAAA;EACpC,IAAA,IAAImB,YAAY,GAAGlB,cAAc,CAACf,KAAK,CAACc,QAAQ,EAAEa,IAAI,EAAE6Y,IAAI,IAAIA,IAAI,CAACxa,KAAK,CAAC,CAAA;;EAE3E;EACA;EACA;EACA;EACA;EACAiC,IAAAA,YAAY,GAAA6C,QAAA,CACP7C,EAAAA,EAAAA,YAAY,EACZqN,IAAI,CAAC/N,OAAO,CAACG,cAAc,CAACO,YAAY,CAAC,CAC7C,CAAA;EAED,IAAA,IAAIga,WAAW,GAAGzB,IAAI,IAAIA,IAAI,CAACpY,OAAO,IAAI,IAAI,GAAGoY,IAAI,CAACpY,OAAO,GAAGnC,SAAS,CAAA;EAEzE,IAAA,IAAIuX,aAAa,GAAGU,MAAa,CAAClW,IAAI,CAAA;MAEtC,IAAIia,WAAW,KAAK,IAAI,EAAE;QACxBzE,aAAa,GAAGU,MAAa,CAAC7V,OAAO,CAAA;EACvC,KAAC,MAAM,IAAI4Z,WAAW,KAAK,KAAK,EAAE,CAEjC,MAAM,IACLF,UAAU,IAAI,IAAI,IAClBZ,gBAAgB,CAACY,UAAU,CAAC9H,UAAU,CAAC,IACvC8H,UAAU,CAAC7H,UAAU,KAAKlU,KAAK,CAACc,QAAQ,CAACE,QAAQ,GAAGhB,KAAK,CAACc,QAAQ,CAACe,MAAM,EACzE;EACA;EACA;EACA;EACA;QACA2V,aAAa,GAAGU,MAAa,CAAC7V,OAAO,CAAA;EACvC,KAAA;EAEA,IAAA,IAAIsV,kBAAkB,GACpB6C,IAAI,IAAI,oBAAoB,IAAIA,IAAI,GAChCA,IAAI,CAAC7C,kBAAkB,KAAK,IAAI,GAChC1X,SAAS,CAAA;MAEf,IAAI4a,SAAS,GAAG,CAACL,IAAI,IAAIA,IAAI,CAACK,SAAS,MAAM,IAAI,CAAA;MAEjD,IAAIrB,UAAU,GAAGC,qBAAqB,CAAC;QACrCC,eAAe;QACfzX,YAAY;EACZuV,MAAAA,aAAAA;EACF,KAAC,CAAC,CAAA;EAEF,IAAA,IAAIgC,UAAU,EAAE;EACd;QACAI,aAAa,CAACJ,UAAU,EAAE;EACxBxZ,QAAAA,KAAK,EAAE,SAAS;EAChBc,QAAAA,QAAQ,EAAEmB,YAAY;EACtBuS,QAAAA,OAAOA,GAAG;YACRoF,aAAa,CAACJ,UAAU,EAAG;EACzBxZ,YAAAA,KAAK,EAAE,YAAY;EACnBwU,YAAAA,OAAO,EAAEvU,SAAS;EAClBwU,YAAAA,KAAK,EAAExU,SAAS;EAChBa,YAAAA,QAAQ,EAAEmB,YAAAA;EACZ,WAAC,CAAC,CAAA;EACF;EACAyZ,UAAAA,QAAQ,CAAC9a,EAAE,EAAE4Z,IAAI,CAAC,CAAA;WACnB;EACD/F,QAAAA,KAAKA,GAAG;YACN,IAAIuD,QAAQ,GAAG,IAAID,GAAG,CAAC/X,KAAK,CAACgY,QAAQ,CAAC,CAAA;EACtCA,UAAAA,QAAQ,CAACpI,GAAG,CAAC4J,UAAU,EAAGjF,YAAY,CAAC,CAAA;EACvCsF,UAAAA,WAAW,CAAC;EAAE7B,YAAAA,QAAAA;EAAS,WAAC,CAAC,CAAA;EAC3B,SAAA;EACF,OAAC,CAAC,CAAA;EACF,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,OAAO,MAAM8B,eAAe,CAACtC,aAAa,EAAEvV,YAAY,EAAE;QACxD8Z,UAAU;EACV;EACA;EACAG,MAAAA,YAAY,EAAExW,KAAK;QACnBiS,kBAAkB;EAClBvV,MAAAA,OAAO,EAAEoY,IAAI,IAAIA,IAAI,CAACpY,OAAO;EAC7B+Z,MAAAA,oBAAoB,EAAE3B,IAAI,IAAIA,IAAI,CAAC4B,cAAc;EACjDvB,MAAAA,SAAAA;EACF,KAAC,CAAC,CAAA;EACJ,GAAA;;EAEA;EACA;EACA;IACA,SAASwB,UAAUA,GAAG;EACpBC,IAAAA,oBAAoB,EAAE,CAAA;EACtBzC,IAAAA,WAAW,CAAC;EAAEjC,MAAAA,YAAY,EAAE,SAAA;EAAU,KAAC,CAAC,CAAA;;EAExC;EACA;EACA,IAAA,IAAI5X,KAAK,CAACyX,UAAU,CAACzX,KAAK,KAAK,YAAY,EAAE;EAC3C,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA;EACA,IAAA,IAAIA,KAAK,CAACyX,UAAU,CAACzX,KAAK,KAAK,MAAM,EAAE;QACrC8Z,eAAe,CAAC9Z,KAAK,CAACwX,aAAa,EAAExX,KAAK,CAACc,QAAQ,EAAE;EACnDyb,QAAAA,8BAA8B,EAAE,IAAA;EAClC,OAAC,CAAC,CAAA;EACF,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA;EACAzC,IAAAA,eAAe,CACb7B,aAAa,IAAIjY,KAAK,CAACwX,aAAa,EACpCxX,KAAK,CAACyX,UAAU,CAAC3W,QAAQ,EACzB;QACE0b,kBAAkB,EAAExc,KAAK,CAACyX,UAAU;EACpC;QACA0E,oBAAoB,EAAE9D,4BAA4B,KAAK,IAAA;EACzD,KACF,CAAC,CAAA;EACH,GAAA;;EAEA;EACA;EACA;EACA,EAAA,eAAeyB,eAAeA,CAC5BtC,aAA4B,EAC5B1W,QAAkB,EAClB0Z,IAWC,EACc;EACf;EACA;EACA;EACApC,IAAAA,2BAA2B,IAAIA,2BAA2B,CAAC/F,KAAK,EAAE,CAAA;EAClE+F,IAAAA,2BAA2B,GAAG,IAAI,CAAA;EAClCH,IAAAA,aAAa,GAAGT,aAAa,CAAA;MAC7BgB,2BAA2B,GACzB,CAACgC,IAAI,IAAIA,IAAI,CAAC+B,8BAA8B,MAAM,IAAI,CAAA;;EAExD;EACA;MACAE,kBAAkB,CAACzc,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAAC2H,OAAO,CAAC,CAAA;MACjDwQ,yBAAyB,GAAG,CAACqC,IAAI,IAAIA,IAAI,CAAC7C,kBAAkB,MAAM,IAAI,CAAA;MAEtEU,4BAA4B,GAAG,CAACmC,IAAI,IAAIA,IAAI,CAAC2B,oBAAoB,MAAM,IAAI,CAAA;EAE3E,IAAA,IAAIO,WAAW,GAAGpH,kBAAkB,IAAID,UAAU,CAAA;EAClD,IAAA,IAAIsH,iBAAiB,GAAGnC,IAAI,IAAIA,IAAI,CAACgC,kBAAkB,CAAA;MACvD,IAAI7U,OAAO,GACT6S,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEN,gBAAgB,IACtBla,KAAK,CAAC2H,OAAO,IACb3H,KAAK,CAAC2H,OAAO,CAACxH,MAAM,GAAG,CAAC,IACxB,CAACsW,mBAAmB;EAChB;MACAzW,KAAK,CAAC2H,OAAO,GACbT,WAAW,CAACwV,WAAW,EAAE5b,QAAQ,EAAEsG,QAAQ,CAAC,CAAA;MAClD,IAAIyT,SAAS,GAAG,CAACL,IAAI,IAAIA,IAAI,CAACK,SAAS,MAAM,IAAI,CAAA;;EAEjD;EACA;EACA;EACA;EACA;EACA;EACA,IAAA,IACElT,OAAO,IACP3H,KAAK,CAACgX,WAAW,IACjB,CAACyB,sBAAsB,IACvBmE,gBAAgB,CAAC5c,KAAK,CAACc,QAAQ,EAAEA,QAAQ,CAAC,IAC1C,EAAE0Z,IAAI,IAAIA,IAAI,CAACuB,UAAU,IAAIZ,gBAAgB,CAACX,IAAI,CAACuB,UAAU,CAAC9H,UAAU,CAAC,CAAC,EAC1E;QACA6G,kBAAkB,CAACha,QAAQ,EAAE;EAAE6G,QAAAA,OAAAA;EAAQ,OAAC,EAAE;EAAEkT,QAAAA,SAAAA;EAAU,OAAC,CAAC,CAAA;EACxD,MAAA,OAAA;EACF,KAAA;MAEA,IAAIhE,QAAQ,GAAGC,aAAa,CAACnP,OAAO,EAAE+U,WAAW,EAAE5b,QAAQ,CAACE,QAAQ,CAAC,CAAA;EACrE,IAAA,IAAI6V,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAAClP,OAAO,EAAE;QACvCA,OAAO,GAAGkP,QAAQ,CAAClP,OAAO,CAAA;EAC5B,KAAA;;EAEA;MACA,IAAI,CAACA,OAAO,EAAE;QACZ,IAAI;UAAEjC,KAAK;UAAEmX,eAAe;EAAExW,QAAAA,KAAAA;EAAM,OAAC,GAAGyW,qBAAqB,CAC3Dhc,QAAQ,CAACE,QACX,CAAC,CAAA;QACD8Z,kBAAkB,CAChBha,QAAQ,EACR;EACE6G,QAAAA,OAAO,EAAEkV,eAAe;UACxB3U,UAAU,EAAE,EAAE;EACdkP,QAAAA,MAAM,EAAE;YACN,CAAC/Q,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;EACd,SAAA;EACF,OAAC,EACD;EAAEmV,QAAAA,SAAAA;EAAU,OACd,CAAC,CAAA;EACD,MAAA,OAAA;EACF,KAAA;;EAEA;EACAzC,IAAAA,2BAA2B,GAAG,IAAIvH,eAAe,EAAE,CAAA;EACnD,IAAA,IAAIkM,OAAO,GAAGC,uBAAuB,CACnC1N,IAAI,CAAC/N,OAAO,EACZT,QAAQ,EACRsX,2BAA2B,CAACpH,MAAM,EAClCwJ,IAAI,IAAIA,IAAI,CAACuB,UACf,CAAC,CAAA;EACD,IAAA,IAAIkB,mBAAoD,CAAA;EAExD,IAAA,IAAIzC,IAAI,IAAIA,IAAI,CAAC0B,YAAY,EAAE;EAC7B;EACA;EACA;EACA;QACAe,mBAAmB,GAAG,CACpBC,mBAAmB,CAACvV,OAAO,CAAC,CAACtB,KAAK,CAACQ,EAAE,EACrC;UAAEmJ,IAAI,EAAE/J,UAAU,CAACP,KAAK;UAAEA,KAAK,EAAE8U,IAAI,CAAC0B,YAAAA;EAAa,OAAC,CACrD,CAAA;EACH,KAAC,MAAM,IACL1B,IAAI,IACJA,IAAI,CAACuB,UAAU,IACfZ,gBAAgB,CAACX,IAAI,CAACuB,UAAU,CAAC9H,UAAU,CAAC,EAC5C;EACA;EACA,MAAA,IAAIkJ,YAAY,GAAG,MAAMC,YAAY,CACnCL,OAAO,EACPjc,QAAQ,EACR0Z,IAAI,CAACuB,UAAU,EACfpU,OAAO,EACPkP,QAAQ,CAACE,MAAM,EACf;UAAE3U,OAAO,EAAEoY,IAAI,CAACpY,OAAO;EAAEyY,QAAAA,SAAAA;EAAU,OACrC,CAAC,CAAA;QAED,IAAIsC,YAAY,CAACE,cAAc,EAAE;EAC/B,QAAA,OAAA;EACF,OAAA;;EAEA;EACA;QACA,IAAIF,YAAY,CAACF,mBAAmB,EAAE;UACpC,IAAI,CAACK,OAAO,EAAExT,MAAM,CAAC,GAAGqT,YAAY,CAACF,mBAAmB,CAAA;EACxD,QAAA,IACEM,aAAa,CAACzT,MAAM,CAAC,IACrB2J,oBAAoB,CAAC3J,MAAM,CAACpE,KAAK,CAAC,IAClCoE,MAAM,CAACpE,KAAK,CAAC8J,MAAM,KAAK,GAAG,EAC3B;EACA4I,UAAAA,2BAA2B,GAAG,IAAI,CAAA;YAElC0C,kBAAkB,CAACha,QAAQ,EAAE;cAC3B6G,OAAO,EAAEwV,YAAY,CAACxV,OAAO;cAC7BO,UAAU,EAAE,EAAE;EACdkP,YAAAA,MAAM,EAAE;gBACN,CAACkG,OAAO,GAAGxT,MAAM,CAACpE,KAAAA;EACpB,aAAA;EACF,WAAC,CAAC,CAAA;EACF,UAAA,OAAA;EACF,SAAA;EACF,OAAA;EAEAiC,MAAAA,OAAO,GAAGwV,YAAY,CAACxV,OAAO,IAAIA,OAAO,CAAA;QACzCsV,mBAAmB,GAAGE,YAAY,CAACF,mBAAmB,CAAA;QACtDN,iBAAiB,GAAGa,oBAAoB,CAAC1c,QAAQ,EAAE0Z,IAAI,CAACuB,UAAU,CAAC,CAAA;EACnElB,MAAAA,SAAS,GAAG,KAAK,CAAA;EACjB;QACAhE,QAAQ,CAACE,MAAM,GAAG,KAAK,CAAA;;EAEvB;EACAgG,MAAAA,OAAO,GAAGC,uBAAuB,CAC/B1N,IAAI,CAAC/N,OAAO,EACZwb,OAAO,CAACpZ,GAAG,EACXoZ,OAAO,CAAC/L,MACV,CAAC,CAAA;EACH,KAAA;;EAEA;MACA,IAAI;QACFqM,cAAc;EACd1V,MAAAA,OAAO,EAAE8V,cAAc;QACvBvV,UAAU;EACVkP,MAAAA,MAAAA;OACD,GAAG,MAAMsG,aAAa,CACrBX,OAAO,EACPjc,QAAQ,EACR6G,OAAO,EACPkP,QAAQ,CAACE,MAAM,EACf4F,iBAAiB,EACjBnC,IAAI,IAAIA,IAAI,CAACuB,UAAU,EACvBvB,IAAI,IAAIA,IAAI,CAACmD,iBAAiB,EAC9BnD,IAAI,IAAIA,IAAI,CAACpY,OAAO,EACpBoY,IAAI,IAAIA,IAAI,CAACN,gBAAgB,KAAK,IAAI,EACtCW,SAAS,EACToC,mBACF,CAAC,CAAA;EAED,IAAA,IAAII,cAAc,EAAE;EAClB,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA;EACAjF,IAAAA,2BAA2B,GAAG,IAAI,CAAA;MAElC0C,kBAAkB,CAACha,QAAQ,EAAAgE,QAAA,CAAA;QACzB6C,OAAO,EAAE8V,cAAc,IAAI9V,OAAAA;OACxBiW,EAAAA,sBAAsB,CAACX,mBAAmB,CAAC,EAAA;QAC9C/U,UAAU;EACVkP,MAAAA,MAAAA;EAAM,KAAA,CACP,CAAC,CAAA;EACJ,GAAA;;EAEA;EACA;EACA,EAAA,eAAegG,YAAYA,CACzBL,OAAgB,EAChBjc,QAAkB,EAClBib,UAAsB,EACtBpU,OAAiC,EACjCkW,UAAmB,EACnBrD,IAAgD,EACnB;EAAA,IAAA,IAD7BA,IAAgD,KAAA,KAAA,CAAA,EAAA;QAAhDA,IAAgD,GAAG,EAAE,CAAA;EAAA,KAAA;EAErD8B,IAAAA,oBAAoB,EAAE,CAAA;;EAEtB;EACA,IAAA,IAAI7E,UAAU,GAAGqG,uBAAuB,CAAChd,QAAQ,EAAEib,UAAU,CAAC,CAAA;EAC9DlC,IAAAA,WAAW,CAAC;EAAEpC,MAAAA,UAAAA;EAAW,KAAC,EAAE;EAAEoD,MAAAA,SAAS,EAAEL,IAAI,CAACK,SAAS,KAAK,IAAA;EAAK,KAAC,CAAC,CAAA;EAEnE,IAAA,IAAIgD,UAAU,EAAE;EACd,MAAA,IAAIE,cAAc,GAAG,MAAMC,cAAc,CACvCrW,OAAO,EACP7G,QAAQ,CAACE,QAAQ,EACjB+b,OAAO,CAAC/L,MACV,CAAC,CAAA;EACD,MAAA,IAAI+M,cAAc,CAAC/N,IAAI,KAAK,SAAS,EAAE;UACrC,OAAO;EAAEqN,UAAAA,cAAc,EAAE,IAAA;WAAM,CAAA;EACjC,OAAC,MAAM,IAAIU,cAAc,CAAC/N,IAAI,KAAK,OAAO,EAAE;UAC1C,IAAIiO,UAAU,GAAGf,mBAAmB,CAACa,cAAc,CAACG,cAAc,CAAC,CAChE7X,KAAK,CAACQ,EAAE,CAAA;UACX,OAAO;YACLc,OAAO,EAAEoW,cAAc,CAACG,cAAc;YACtCjB,mBAAmB,EAAE,CACnBgB,UAAU,EACV;cACEjO,IAAI,EAAE/J,UAAU,CAACP,KAAK;cACtBA,KAAK,EAAEqY,cAAc,CAACrY,KAAAA;aACvB,CAAA;WAEJ,CAAA;EACH,OAAC,MAAM,IAAI,CAACqY,cAAc,CAACpW,OAAO,EAAE;UAClC,IAAI;YAAEkV,eAAe;YAAEnX,KAAK;EAAEW,UAAAA,KAAAA;EAAM,SAAC,GAAGyW,qBAAqB,CAC3Dhc,QAAQ,CAACE,QACX,CAAC,CAAA;UACD,OAAO;EACL2G,UAAAA,OAAO,EAAEkV,eAAe;EACxBI,UAAAA,mBAAmB,EAAE,CACnB5W,KAAK,CAACQ,EAAE,EACR;cACEmJ,IAAI,EAAE/J,UAAU,CAACP,KAAK;EACtBA,YAAAA,KAAAA;aACD,CAAA;WAEJ,CAAA;EACH,OAAC,MAAM;UACLiC,OAAO,GAAGoW,cAAc,CAACpW,OAAO,CAAA;EAClC,OAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAImC,MAAkB,CAAA;EACtB,IAAA,IAAIqU,WAAW,GAAGC,cAAc,CAACzW,OAAO,EAAE7G,QAAQ,CAAC,CAAA;EAEnD,IAAA,IAAI,CAACqd,WAAW,CAAC9X,KAAK,CAACjG,MAAM,IAAI,CAAC+d,WAAW,CAAC9X,KAAK,CAAC6Q,IAAI,EAAE;EACxDpN,MAAAA,MAAM,GAAG;UACPkG,IAAI,EAAE/J,UAAU,CAACP,KAAK;EACtBA,QAAAA,KAAK,EAAEiR,sBAAsB,CAAC,GAAG,EAAE;YACjC0H,MAAM,EAAEtB,OAAO,CAACsB,MAAM;YACtBrd,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;EAC3Bsc,UAAAA,OAAO,EAAEa,WAAW,CAAC9X,KAAK,CAACQ,EAAAA;WAC5B,CAAA;SACF,CAAA;EACH,KAAC,MAAM;EACL,MAAA,IAAIyX,OAAO,GAAG,MAAMC,gBAAgB,CAClC,QAAQ,EACRve,KAAK,EACL+c,OAAO,EACP,CAACoB,WAAW,CAAC,EACbxW,OAAO,EACP,IACF,CAAC,CAAA;QACDmC,MAAM,GAAGwU,OAAO,CAACH,WAAW,CAAC9X,KAAK,CAACQ,EAAE,CAAC,CAAA;EAEtC,MAAA,IAAIkW,OAAO,CAAC/L,MAAM,CAACa,OAAO,EAAE;UAC1B,OAAO;EAAEwL,UAAAA,cAAc,EAAE,IAAA;WAAM,CAAA;EACjC,OAAA;EACF,KAAA;EAEA,IAAA,IAAImB,gBAAgB,CAAC1U,MAAM,CAAC,EAAE;EAC5B,MAAA,IAAI1H,OAAgB,CAAA;EACpB,MAAA,IAAIoY,IAAI,IAAIA,IAAI,CAACpY,OAAO,IAAI,IAAI,EAAE;UAChCA,OAAO,GAAGoY,IAAI,CAACpY,OAAO,CAAA;EACxB,OAAC,MAAM;EACL;EACA;EACA;UACA,IAAItB,QAAQ,GAAG2d,yBAAyB,CACtC3U,MAAM,CAACuJ,QAAQ,CAAC5D,OAAO,CAACmC,GAAG,CAAC,UAAU,CAAC,EACvC,IAAInQ,GAAG,CAACsb,OAAO,CAACpZ,GAAG,CAAC,EACpByD,QACF,CAAC,CAAA;EACDhF,QAAAA,OAAO,GAAGtB,QAAQ,KAAKd,KAAK,CAACc,QAAQ,CAACE,QAAQ,GAAGhB,KAAK,CAACc,QAAQ,CAACe,MAAM,CAAA;EACxE,OAAA;EACA,MAAA,MAAM6c,uBAAuB,CAAC3B,OAAO,EAAEjT,MAAM,EAAE,IAAI,EAAE;UACnDiS,UAAU;EACV3Z,QAAAA,OAAAA;EACF,OAAC,CAAC,CAAA;QACF,OAAO;EAAEib,QAAAA,cAAc,EAAE,IAAA;SAAM,CAAA;EACjC,KAAA;EAEA,IAAA,IAAIsB,gBAAgB,CAAC7U,MAAM,CAAC,EAAE;QAC5B,MAAM6M,sBAAsB,CAAC,GAAG,EAAE;EAAE3G,QAAAA,IAAI,EAAE,cAAA;EAAe,OAAC,CAAC,CAAA;EAC7D,KAAA;EAEA,IAAA,IAAIuN,aAAa,CAACzT,MAAM,CAAC,EAAE;EACzB;EACA;QACA,IAAI8U,aAAa,GAAG1B,mBAAmB,CAACvV,OAAO,EAAEwW,WAAW,CAAC9X,KAAK,CAACQ,EAAE,CAAC,CAAA;;EAEtE;EACA;EACA;EACA;EACA;QACA,IAAI,CAAC2T,IAAI,IAAIA,IAAI,CAACpY,OAAO,MAAM,IAAI,EAAE;UACnC6V,aAAa,GAAGC,MAAa,CAAClW,IAAI,CAAA;EACpC,OAAA;QAEA,OAAO;UACL2F,OAAO;UACPsV,mBAAmB,EAAE,CAAC2B,aAAa,CAACvY,KAAK,CAACQ,EAAE,EAAEiD,MAAM,CAAA;SACrD,CAAA;EACH,KAAA;MAEA,OAAO;QACLnC,OAAO;QACPsV,mBAAmB,EAAE,CAACkB,WAAW,CAAC9X,KAAK,CAACQ,EAAE,EAAEiD,MAAM,CAAA;OACnD,CAAA;EACH,GAAA;;EAEA;EACA;IACA,eAAe4T,aAAaA,CAC1BX,OAAgB,EAChBjc,QAAkB,EAClB6G,OAAiC,EACjCkW,UAAmB,EACnBrB,kBAA+B,EAC/BT,UAAuB,EACvB4B,iBAA8B,EAC9Bvb,OAAiB,EACjB8X,gBAA0B,EAC1BW,SAAmB,EACnBoC,mBAAyC,EACX;EAC9B;MACA,IAAIN,iBAAiB,GACnBH,kBAAkB,IAAIgB,oBAAoB,CAAC1c,QAAQ,EAAEib,UAAU,CAAC,CAAA;;EAElE;EACA;MACA,IAAI8C,gBAAgB,GAClB9C,UAAU,IACV4B,iBAAiB,IACjBmB,2BAA2B,CAACnC,iBAAiB,CAAC,CAAA;;EAEhD;EACA;EACA;EACA;EACA;EACA;EACA,IAAA,IAAIoC,2BAA2B,GAC7B,CAACvG,2BAA2B,KAC3B,CAAC5C,MAAM,CAACG,mBAAmB,IAAI,CAACmE,gBAAgB,CAAC,CAAA;;EAEpD;EACA;EACA;EACA;EACA;EACA,IAAA,IAAI2D,UAAU,EAAE;EACd,MAAA,IAAIkB,2BAA2B,EAAE;EAC/B,QAAA,IAAIlH,UAAU,GAAGmH,oBAAoB,CAAC/B,mBAAmB,CAAC,CAAA;EAC1DpD,QAAAA,WAAW,CAAA/U,QAAA,CAAA;EAEP2S,UAAAA,UAAU,EAAEkF,iBAAAA;WACR9E,EAAAA,UAAU,KAAK5X,SAAS,GAAG;EAAE4X,UAAAA,UAAAA;WAAY,GAAG,EAAE,CAEpD,EAAA;EACEgD,UAAAA,SAAAA;EACF,SACF,CAAC,CAAA;EACH,OAAA;EAEA,MAAA,IAAIkD,cAAc,GAAG,MAAMC,cAAc,CACvCrW,OAAO,EACP7G,QAAQ,CAACE,QAAQ,EACjB+b,OAAO,CAAC/L,MACV,CAAC,CAAA;EAED,MAAA,IAAI+M,cAAc,CAAC/N,IAAI,KAAK,SAAS,EAAE;UACrC,OAAO;EAAEqN,UAAAA,cAAc,EAAE,IAAA;WAAM,CAAA;EACjC,OAAC,MAAM,IAAIU,cAAc,CAAC/N,IAAI,KAAK,OAAO,EAAE;UAC1C,IAAIiO,UAAU,GAAGf,mBAAmB,CAACa,cAAc,CAACG,cAAc,CAAC,CAChE7X,KAAK,CAACQ,EAAE,CAAA;UACX,OAAO;YACLc,OAAO,EAAEoW,cAAc,CAACG,cAAc;YACtChW,UAAU,EAAE,EAAE;EACdkP,UAAAA,MAAM,EAAE;cACN,CAAC6G,UAAU,GAAGF,cAAc,CAACrY,KAAAA;EAC/B,WAAA;WACD,CAAA;EACH,OAAC,MAAM,IAAI,CAACqY,cAAc,CAACpW,OAAO,EAAE;UAClC,IAAI;YAAEjC,KAAK;YAAEmX,eAAe;EAAExW,UAAAA,KAAAA;EAAM,SAAC,GAAGyW,qBAAqB,CAC3Dhc,QAAQ,CAACE,QACX,CAAC,CAAA;UACD,OAAO;EACL2G,UAAAA,OAAO,EAAEkV,eAAe;YACxB3U,UAAU,EAAE,EAAE;EACdkP,UAAAA,MAAM,EAAE;cACN,CAAC/Q,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;EACd,WAAA;WACD,CAAA;EACH,OAAC,MAAM;UACLiC,OAAO,GAAGoW,cAAc,CAACpW,OAAO,CAAA;EAClC,OAAA;EACF,KAAA;EAEA,IAAA,IAAI+U,WAAW,GAAGpH,kBAAkB,IAAID,UAAU,CAAA;MAClD,IAAI,CAAC4J,aAAa,EAAEC,oBAAoB,CAAC,GAAGC,gBAAgB,CAC1D7P,IAAI,CAAC/N,OAAO,EACZvB,KAAK,EACL2H,OAAO,EACPkX,gBAAgB,EAChB/d,QAAQ,EACR8U,MAAM,CAACG,mBAAmB,IAAImE,gBAAgB,KAAK,IAAI,EACvDtE,MAAM,CAACK,8BAA8B,EACrCwC,sBAAsB,EACtBC,uBAAuB,EACvBC,qBAAqB,EACrBQ,eAAe,EACfF,gBAAgB,EAChBD,gBAAgB,EAChB0D,WAAW,EACXtV,QAAQ,EACR6V,mBACF,CAAC,CAAA;;EAED;EACA;EACA;EACAmC,IAAAA,qBAAqB,CAClB9B,OAAO,IACN,EAAE3V,OAAO,IAAIA,OAAO,CAACkD,IAAI,CAAEoM,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,KAAKyW,OAAO,CAAC,CAAC,IACxD2B,aAAa,IAAIA,aAAa,CAACpU,IAAI,CAAEoM,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,KAAKyW,OAAO,CACtE,CAAC,CAAA;MAEDxE,uBAAuB,GAAG,EAAED,kBAAkB,CAAA;;EAE9C;MACA,IAAIoG,aAAa,CAAC9e,MAAM,KAAK,CAAC,IAAI+e,oBAAoB,CAAC/e,MAAM,KAAK,CAAC,EAAE;EACnE,MAAA,IAAIkf,eAAe,GAAGC,sBAAsB,EAAE,CAAA;QAC9CxE,kBAAkB,CAChBha,QAAQ,EAAAgE,QAAA,CAAA;UAEN6C,OAAO;UACPO,UAAU,EAAE,EAAE;EACd;UACAkP,MAAM,EACJ6F,mBAAmB,IAAIM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GACxD;YAAE,CAACA,mBAAmB,CAAC,CAAC,CAAC,GAAGA,mBAAmB,CAAC,CAAC,CAAC,CAACvX,KAAAA;EAAM,SAAC,GAC1D,IAAA;EAAI,OAAA,EACPkY,sBAAsB,CAACX,mBAAmB,CAAC,EAC1CoC,eAAe,GAAG;EAAEvH,QAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC/X,KAAK,CAAC8X,QAAQ,CAAA;SAAG,GAAG,EAAE,CAElE,EAAA;EAAE+C,QAAAA,SAAAA;EAAU,OACd,CAAC,CAAA;QACD,OAAO;EAAEwC,QAAAA,cAAc,EAAE,IAAA;SAAM,CAAA;EACjC,KAAA;EAEA,IAAA,IAAI0B,2BAA2B,EAAE;QAC/B,IAAIQ,OAA6B,GAAG,EAAE,CAAA;QACtC,IAAI,CAAC1B,UAAU,EAAE;EACf;UACA0B,OAAO,CAAC9H,UAAU,GAAGkF,iBAAiB,CAAA;EACtC,QAAA,IAAI9E,UAAU,GAAGmH,oBAAoB,CAAC/B,mBAAmB,CAAC,CAAA;UAC1D,IAAIpF,UAAU,KAAK5X,SAAS,EAAE;YAC5Bsf,OAAO,CAAC1H,UAAU,GAAGA,UAAU,CAAA;EACjC,SAAA;EACF,OAAA;EACA,MAAA,IAAIqH,oBAAoB,CAAC/e,MAAM,GAAG,CAAC,EAAE;EACnCof,QAAAA,OAAO,CAACzH,QAAQ,GAAG0H,8BAA8B,CAACN,oBAAoB,CAAC,CAAA;EACzE,OAAA;QACArF,WAAW,CAAC0F,OAAO,EAAE;EAAE1E,QAAAA,SAAAA;EAAU,OAAC,CAAC,CAAA;EACrC,KAAA;EAEAqE,IAAAA,oBAAoB,CAACjW,OAAO,CAAEwW,EAAE,IAAK;EACnCC,MAAAA,YAAY,CAACD,EAAE,CAAC5e,GAAG,CAAC,CAAA;QACpB,IAAI4e,EAAE,CAAC7O,UAAU,EAAE;EACjB;EACA;EACA;UACAgI,gBAAgB,CAAChJ,GAAG,CAAC6P,EAAE,CAAC5e,GAAG,EAAE4e,EAAE,CAAC7O,UAAU,CAAC,CAAA;EAC7C,OAAA;EACF,KAAC,CAAC,CAAA;;EAEF;EACA,IAAA,IAAI+O,8BAA8B,GAAGA,MACnCT,oBAAoB,CAACjW,OAAO,CAAE2W,CAAC,IAAKF,YAAY,CAACE,CAAC,CAAC/e,GAAG,CAAC,CAAC,CAAA;EAC1D,IAAA,IAAIuX,2BAA2B,EAAE;QAC/BA,2BAA2B,CAACpH,MAAM,CAACjL,gBAAgB,CACjD,OAAO,EACP4Z,8BACF,CAAC,CAAA;EACH,KAAA;MAEA,IAAI;QAAEE,aAAa;EAAEC,MAAAA,cAAAA;EAAe,KAAC,GACnC,MAAMC,8BAA8B,CAClC/f,KAAK,EACL2H,OAAO,EACPsX,aAAa,EACbC,oBAAoB,EACpBnC,OACF,CAAC,CAAA;EAEH,IAAA,IAAIA,OAAO,CAAC/L,MAAM,CAACa,OAAO,EAAE;QAC1B,OAAO;EAAEwL,QAAAA,cAAc,EAAE,IAAA;SAAM,CAAA;EACjC,KAAA;;EAEA;EACA;EACA;EACA,IAAA,IAAIjF,2BAA2B,EAAE;QAC/BA,2BAA2B,CAACpH,MAAM,CAAChL,mBAAmB,CACpD,OAAO,EACP2Z,8BACF,CAAC,CAAA;EACH,KAAA;EAEAT,IAAAA,oBAAoB,CAACjW,OAAO,CAAEwW,EAAE,IAAK7G,gBAAgB,CAAC9G,MAAM,CAAC2N,EAAE,CAAC5e,GAAG,CAAC,CAAC,CAAA;;EAErE;EACA,IAAA,IAAIsS,QAAQ,GAAG6M,YAAY,CAACH,aAAa,CAAC,CAAA;EAC1C,IAAA,IAAI1M,QAAQ,EAAE;QACZ,MAAMuL,uBAAuB,CAAC3B,OAAO,EAAE5J,QAAQ,CAACrJ,MAAM,EAAE,IAAI,EAAE;EAC5D1H,QAAAA,OAAAA;EACF,OAAC,CAAC,CAAA;QACF,OAAO;EAAEib,QAAAA,cAAc,EAAE,IAAA;SAAM,CAAA;EACjC,KAAA;EAEAlK,IAAAA,QAAQ,GAAG6M,YAAY,CAACF,cAAc,CAAC,CAAA;EACvC,IAAA,IAAI3M,QAAQ,EAAE;EACZ;EACA;EACA;EACA6F,MAAAA,gBAAgB,CAAC3H,GAAG,CAAC8B,QAAQ,CAACtS,GAAG,CAAC,CAAA;QAClC,MAAM6d,uBAAuB,CAAC3B,OAAO,EAAE5J,QAAQ,CAACrJ,MAAM,EAAE,IAAI,EAAE;EAC5D1H,QAAAA,OAAAA;EACF,OAAC,CAAC,CAAA;QACF,OAAO;EAAEib,QAAAA,cAAc,EAAE,IAAA;SAAM,CAAA;EACjC,KAAA;;EAEA;MACA,IAAI;QAAEnV,UAAU;EAAEkP,MAAAA,MAAAA;EAAO,KAAC,GAAG6I,iBAAiB,CAC5CjgB,KAAK,EACL2H,OAAO,EACPkY,aAAa,EACb5C,mBAAmB,EACnBiC,oBAAoB,EACpBY,cAAc,EACd1G,eACF,CAAC,CAAA;;EAED;EACAA,IAAAA,eAAe,CAACnQ,OAAO,CAAC,CAACiX,YAAY,EAAE5C,OAAO,KAAK;EACjD4C,MAAAA,YAAY,CAAC/N,SAAS,CAAEN,OAAO,IAAK;EAClC;EACA;EACA;EACA,QAAA,IAAIA,OAAO,IAAIqO,YAAY,CAAC9O,IAAI,EAAE;EAChCgI,UAAAA,eAAe,CAACtH,MAAM,CAACwL,OAAO,CAAC,CAAA;EACjC,SAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAC,CAAC,CAAA;;EAEF;MACA,IAAI1H,MAAM,CAACG,mBAAmB,IAAImE,gBAAgB,IAAIla,KAAK,CAACoX,MAAM,EAAE;QAClEA,MAAM,GAAAtS,QAAA,CAAQ9E,EAAAA,EAAAA,KAAK,CAACoX,MAAM,EAAKA,MAAM,CAAE,CAAA;EACzC,KAAA;EAEA,IAAA,IAAIiI,eAAe,GAAGC,sBAAsB,EAAE,CAAA;EAC9C,IAAA,IAAIa,kBAAkB,GAAGC,oBAAoB,CAACtH,uBAAuB,CAAC,CAAA;MACtE,IAAIuH,oBAAoB,GACtBhB,eAAe,IAAIc,kBAAkB,IAAIjB,oBAAoB,CAAC/e,MAAM,GAAG,CAAC,CAAA;EAE1E,IAAA,OAAA2E,QAAA,CAAA;QACE6C,OAAO;QACPO,UAAU;EACVkP,MAAAA,MAAAA;EAAM,KAAA,EACFiJ,oBAAoB,GAAG;EAAEvI,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC/X,KAAK,CAAC8X,QAAQ,CAAA;OAAG,GAAG,EAAE,CAAA,CAAA;EAEzE,GAAA;IAEA,SAASkH,oBAAoBA,CAC3B/B,mBAAoD,EACN;MAC9C,IAAIA,mBAAmB,IAAI,CAACM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE;EACjE;EACA;EACA;QACA,OAAO;UACL,CAACA,mBAAmB,CAAC,CAAC,CAAC,GAAGA,mBAAmB,CAAC,CAAC,CAAC,CAAC7U,IAAAA;SAClD,CAAA;EACH,KAAC,MAAM,IAAIpI,KAAK,CAAC6X,UAAU,EAAE;EAC3B,MAAA,IAAInM,MAAM,CAAC2P,IAAI,CAACrb,KAAK,CAAC6X,UAAU,CAAC,CAAC1X,MAAM,KAAK,CAAC,EAAE;EAC9C,QAAA,OAAO,IAAI,CAAA;EACb,OAAC,MAAM;UACL,OAAOH,KAAK,CAAC6X,UAAU,CAAA;EACzB,OAAA;EACF,KAAA;EACF,GAAA;IAEA,SAAS2H,8BAA8BA,CACrCN,oBAA2C,EAC3C;EACAA,IAAAA,oBAAoB,CAACjW,OAAO,CAAEwW,EAAE,IAAK;QACnC,IAAI9E,OAAO,GAAG3a,KAAK,CAAC8X,QAAQ,CAAClG,GAAG,CAAC6N,EAAE,CAAC5e,GAAG,CAAC,CAAA;EACxC,MAAA,IAAIyf,mBAAmB,GAAGC,iBAAiB,CACzCtgB,SAAS,EACT0a,OAAO,GAAGA,OAAO,CAACvS,IAAI,GAAGnI,SAC3B,CAAC,CAAA;QACDD,KAAK,CAAC8X,QAAQ,CAAClI,GAAG,CAAC6P,EAAE,CAAC5e,GAAG,EAAEyf,mBAAmB,CAAC,CAAA;EACjD,KAAC,CAAC,CAAA;EACF,IAAA,OAAO,IAAIvI,GAAG,CAAC/X,KAAK,CAAC8X,QAAQ,CAAC,CAAA;EAChC,GAAA;;EAEA;IACA,SAAS0I,KAAKA,CACZ3f,GAAW,EACXyc,OAAe,EACf7Z,IAAmB,EACnB+W,IAAyB,EACzB;EACA,IAAA,IAAIrF,QAAQ,EAAE;QACZ,MAAM,IAAIhR,KAAK,CACb,2EAA2E,GACzE,8EAA8E,GAC9E,6CACJ,CAAC,CAAA;EACH,KAAA;MAEAub,YAAY,CAAC7e,GAAG,CAAC,CAAA;MAEjB,IAAIga,SAAS,GAAG,CAACL,IAAI,IAAIA,IAAI,CAACK,SAAS,MAAM,IAAI,CAAA;EAEjD,IAAA,IAAI6B,WAAW,GAAGpH,kBAAkB,IAAID,UAAU,CAAA;EAClD,IAAA,IAAIsG,cAAc,GAAGC,WAAW,CAC9B5b,KAAK,CAACc,QAAQ,EACdd,KAAK,CAAC2H,OAAO,EACbP,QAAQ,EACRwO,MAAM,CAACI,kBAAkB,EACzBvS,IAAI,EACJmS,MAAM,CAACvH,oBAAoB,EAC3BiP,OAAO,EACP9C,IAAI,IAAA,IAAA,GAAA,KAAA,CAAA,GAAJA,IAAI,CAAEsB,QACR,CAAC,CAAA;MACD,IAAInU,OAAO,GAAGT,WAAW,CAACwV,WAAW,EAAEf,cAAc,EAAEvU,QAAQ,CAAC,CAAA;MAEhE,IAAIyP,QAAQ,GAAGC,aAAa,CAACnP,OAAO,EAAE+U,WAAW,EAAEf,cAAc,CAAC,CAAA;EAClE,IAAA,IAAI9E,QAAQ,CAACE,MAAM,IAAIF,QAAQ,CAAClP,OAAO,EAAE;QACvCA,OAAO,GAAGkP,QAAQ,CAAClP,OAAO,CAAA;EAC5B,KAAA;MAEA,IAAI,CAACA,OAAO,EAAE;QACZ8Y,eAAe,CACb5f,GAAG,EACHyc,OAAO,EACP3G,sBAAsB,CAAC,GAAG,EAAE;EAAE3V,QAAAA,QAAQ,EAAE2a,cAAAA;EAAe,OAAC,CAAC,EACzD;EAAEd,QAAAA,SAAAA;EAAU,OACd,CAAC,CAAA;EACD,MAAA,OAAA;EACF,KAAA;MAEA,IAAI;QAAElZ,IAAI;QAAEoa,UAAU;EAAErW,MAAAA,KAAAA;EAAM,KAAC,GAAGsW,wBAAwB,CACxDpG,MAAM,CAACE,sBAAsB,EAC7B,IAAI,EACJ6F,cAAc,EACdnB,IACF,CAAC,CAAA;EAED,IAAA,IAAI9U,KAAK,EAAE;EACT+a,MAAAA,eAAe,CAAC5f,GAAG,EAAEyc,OAAO,EAAE5X,KAAK,EAAE;EAAEmV,QAAAA,SAAAA;EAAU,OAAC,CAAC,CAAA;EACnD,MAAA,OAAA;EACF,KAAA;EAEA,IAAA,IAAI5S,KAAK,GAAGmW,cAAc,CAACzW,OAAO,EAAEhG,IAAI,CAAC,CAAA;MAEzC,IAAIgW,kBAAkB,GAAG,CAAC6C,IAAI,IAAIA,IAAI,CAAC7C,kBAAkB,MAAM,IAAI,CAAA;MAEnE,IAAIoE,UAAU,IAAIZ,gBAAgB,CAACY,UAAU,CAAC9H,UAAU,CAAC,EAAE;QACzDyM,mBAAmB,CACjB7f,GAAG,EACHyc,OAAO,EACP3b,IAAI,EACJsG,KAAK,EACLN,OAAO,EACPkP,QAAQ,CAACE,MAAM,EACf8D,SAAS,EACTlD,kBAAkB,EAClBoE,UACF,CAAC,CAAA;EACD,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA9C,IAAAA,gBAAgB,CAACrJ,GAAG,CAAC/O,GAAG,EAAE;QAAEyc,OAAO;EAAE3b,MAAAA,IAAAA;EAAK,KAAC,CAAC,CAAA;MAC5Cgf,mBAAmB,CACjB9f,GAAG,EACHyc,OAAO,EACP3b,IAAI,EACJsG,KAAK,EACLN,OAAO,EACPkP,QAAQ,CAACE,MAAM,EACf8D,SAAS,EACTlD,kBAAkB,EAClBoE,UACF,CAAC,CAAA;EACH,GAAA;;EAEA;EACA;EACA,EAAA,eAAe2E,mBAAmBA,CAChC7f,GAAW,EACXyc,OAAe,EACf3b,IAAY,EACZsG,KAA6B,EAC7B2Y,cAAwC,EACxC/C,UAAmB,EACnBhD,SAAkB,EAClBlD,kBAA2B,EAC3BoE,UAAsB,EACtB;EACAO,IAAAA,oBAAoB,EAAE,CAAA;EACtBrD,IAAAA,gBAAgB,CAACnH,MAAM,CAACjR,GAAG,CAAC,CAAA;MAE5B,SAASggB,uBAAuBA,CAAC5J,CAAyB,EAAE;EAC1D,MAAA,IAAI,CAACA,CAAC,CAAC5Q,KAAK,CAACjG,MAAM,IAAI,CAAC6W,CAAC,CAAC5Q,KAAK,CAAC6Q,IAAI,EAAE;EACpC,QAAA,IAAIxR,KAAK,GAAGiR,sBAAsB,CAAC,GAAG,EAAE;YACtC0H,MAAM,EAAEtC,UAAU,CAAC9H,UAAU;EAC7BjT,UAAAA,QAAQ,EAAEW,IAAI;EACd2b,UAAAA,OAAO,EAAEA,OAAAA;EACX,SAAC,CAAC,CAAA;EACFmD,QAAAA,eAAe,CAAC5f,GAAG,EAAEyc,OAAO,EAAE5X,KAAK,EAAE;EAAEmV,UAAAA,SAAAA;EAAU,SAAC,CAAC,CAAA;EACnD,QAAA,OAAO,IAAI,CAAA;EACb,OAAA;EACA,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;EAEA,IAAA,IAAI,CAACgD,UAAU,IAAIgD,uBAAuB,CAAC5Y,KAAK,CAAC,EAAE;EACjD,MAAA,OAAA;EACF,KAAA;;EAEA;MACA,IAAI6Y,eAAe,GAAG9gB,KAAK,CAAC8X,QAAQ,CAAClG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;MAC7CkgB,kBAAkB,CAAClgB,GAAG,EAAEmgB,oBAAoB,CAACjF,UAAU,EAAE+E,eAAe,CAAC,EAAE;EACzEjG,MAAAA,SAAAA;EACF,KAAC,CAAC,CAAA;EAEF,IAAA,IAAIoG,eAAe,GAAG,IAAIpQ,eAAe,EAAE,CAAA;EAC3C,IAAA,IAAIqQ,YAAY,GAAGlE,uBAAuB,CACxC1N,IAAI,CAAC/N,OAAO,EACZI,IAAI,EACJsf,eAAe,CAACjQ,MAAM,EACtB+K,UACF,CAAC,CAAA;EAED,IAAA,IAAI8B,UAAU,EAAE;QACd,IAAIE,cAAc,GAAG,MAAMC,cAAc,CACvC4C,cAAc,EACd,IAAInf,GAAG,CAACyf,YAAY,CAACvd,GAAG,CAAC,CAAC3C,QAAQ,EAClCkgB,YAAY,CAAClQ,MAAM,EACnBnQ,GACF,CAAC,CAAA;EAED,MAAA,IAAIkd,cAAc,CAAC/N,IAAI,KAAK,SAAS,EAAE;EACrC,QAAA,OAAA;EACF,OAAC,MAAM,IAAI+N,cAAc,CAAC/N,IAAI,KAAK,OAAO,EAAE;UAC1CyQ,eAAe,CAAC5f,GAAG,EAAEyc,OAAO,EAAES,cAAc,CAACrY,KAAK,EAAE;EAAEmV,UAAAA,SAAAA;EAAU,SAAC,CAAC,CAAA;EAClE,QAAA,OAAA;EACF,OAAC,MAAM,IAAI,CAACkD,cAAc,CAACpW,OAAO,EAAE;UAClC8Y,eAAe,CACb5f,GAAG,EACHyc,OAAO,EACP3G,sBAAsB,CAAC,GAAG,EAAE;EAAE3V,UAAAA,QAAQ,EAAEW,IAAAA;EAAK,SAAC,CAAC,EAC/C;EAAEkZ,UAAAA,SAAAA;EAAU,SACd,CAAC,CAAA;EACD,QAAA,OAAA;EACF,OAAC,MAAM;UACL+F,cAAc,GAAG7C,cAAc,CAACpW,OAAO,CAAA;EACvCM,QAAAA,KAAK,GAAGmW,cAAc,CAACwC,cAAc,EAAEjf,IAAI,CAAC,CAAA;EAE5C,QAAA,IAAIkf,uBAAuB,CAAC5Y,KAAK,CAAC,EAAE;EAClC,UAAA,OAAA;EACF,SAAA;EACF,OAAA;EACF,KAAA;;EAEA;EACA2Q,IAAAA,gBAAgB,CAAChJ,GAAG,CAAC/O,GAAG,EAAEogB,eAAe,CAAC,CAAA;MAE1C,IAAIE,iBAAiB,GAAGtI,kBAAkB,CAAA;EAC1C,IAAA,IAAIuI,aAAa,GAAG,MAAM7C,gBAAgB,CACxC,QAAQ,EACRve,KAAK,EACLkhB,YAAY,EACZ,CAACjZ,KAAK,CAAC,EACP2Y,cAAc,EACd/f,GACF,CAAC,CAAA;MACD,IAAIsc,YAAY,GAAGiE,aAAa,CAACnZ,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;EAEhD,IAAA,IAAIqa,YAAY,CAAClQ,MAAM,CAACa,OAAO,EAAE;EAC/B;EACA;QACA,IAAI+G,gBAAgB,CAAChH,GAAG,CAAC/Q,GAAG,CAAC,KAAKogB,eAAe,EAAE;EACjDrI,QAAAA,gBAAgB,CAAC9G,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC9B,OAAA;EACA,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA;MACA,IAAI+U,MAAM,CAACC,iBAAiB,IAAIsD,eAAe,CAACxJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;QACxD,IAAI2d,gBAAgB,CAACrB,YAAY,CAAC,IAAII,aAAa,CAACJ,YAAY,CAAC,EAAE;EACjE4D,QAAAA,kBAAkB,CAAClgB,GAAG,EAAEwgB,cAAc,CAACphB,SAAS,CAAC,CAAC,CAAA;EAClD,QAAA,OAAA;EACF,OAAA;EACA;EACF,KAAC,MAAM;EACL,MAAA,IAAIue,gBAAgB,CAACrB,YAAY,CAAC,EAAE;EAClCvE,QAAAA,gBAAgB,CAAC9G,MAAM,CAACjR,GAAG,CAAC,CAAA;UAC5B,IAAIiY,uBAAuB,GAAGqI,iBAAiB,EAAE;EAC/C;EACA;EACA;EACA;EACAJ,UAAAA,kBAAkB,CAAClgB,GAAG,EAAEwgB,cAAc,CAACphB,SAAS,CAAC,CAAC,CAAA;EAClD,UAAA,OAAA;EACF,SAAC,MAAM;EACL+Y,UAAAA,gBAAgB,CAAC3H,GAAG,CAACxQ,GAAG,CAAC,CAAA;EACzBkgB,UAAAA,kBAAkB,CAAClgB,GAAG,EAAE0f,iBAAiB,CAACxE,UAAU,CAAC,CAAC,CAAA;EACtD,UAAA,OAAO2C,uBAAuB,CAACwC,YAAY,EAAE/D,YAAY,EAAE,KAAK,EAAE;EAChEQ,YAAAA,iBAAiB,EAAE5B,UAAU;EAC7BpE,YAAAA,kBAAAA;EACF,WAAC,CAAC,CAAA;EACJ,SAAA;EACF,OAAA;;EAEA;EACA,MAAA,IAAI4F,aAAa,CAACJ,YAAY,CAAC,EAAE;UAC/BsD,eAAe,CAAC5f,GAAG,EAAEyc,OAAO,EAAEH,YAAY,CAACzX,KAAK,CAAC,CAAA;EACjD,QAAA,OAAA;EACF,OAAA;EACF,KAAA;EAEA,IAAA,IAAIiZ,gBAAgB,CAACxB,YAAY,CAAC,EAAE;QAClC,MAAMxG,sBAAsB,CAAC,GAAG,EAAE;EAAE3G,QAAAA,IAAI,EAAE,cAAA;EAAe,OAAC,CAAC,CAAA;EAC7D,KAAA;;EAEA;EACA;MACA,IAAI/N,YAAY,GAAGjC,KAAK,CAACyX,UAAU,CAAC3W,QAAQ,IAAId,KAAK,CAACc,QAAQ,CAAA;EAC9D,IAAA,IAAIwgB,mBAAmB,GAAGtE,uBAAuB,CAC/C1N,IAAI,CAAC/N,OAAO,EACZU,YAAY,EACZgf,eAAe,CAACjQ,MAClB,CAAC,CAAA;EACD,IAAA,IAAI0L,WAAW,GAAGpH,kBAAkB,IAAID,UAAU,CAAA;MAClD,IAAI1N,OAAO,GACT3H,KAAK,CAACyX,UAAU,CAACzX,KAAK,KAAK,MAAM,GAC7BkH,WAAW,CAACwV,WAAW,EAAE1c,KAAK,CAACyX,UAAU,CAAC3W,QAAQ,EAAEsG,QAAQ,CAAC,GAC7DpH,KAAK,CAAC2H,OAAO,CAAA;EAEnB3D,IAAAA,SAAS,CAAC2D,OAAO,EAAE,8CAA8C,CAAC,CAAA;MAElE,IAAI4Z,MAAM,GAAG,EAAE1I,kBAAkB,CAAA;EACjCE,IAAAA,cAAc,CAACnJ,GAAG,CAAC/O,GAAG,EAAE0gB,MAAM,CAAC,CAAA;MAE/B,IAAIC,WAAW,GAAGjB,iBAAiB,CAACxE,UAAU,EAAEoB,YAAY,CAAC/U,IAAI,CAAC,CAAA;MAClEpI,KAAK,CAAC8X,QAAQ,CAAClI,GAAG,CAAC/O,GAAG,EAAE2gB,WAAW,CAAC,CAAA;MAEpC,IAAI,CAACvC,aAAa,EAAEC,oBAAoB,CAAC,GAAGC,gBAAgB,CAC1D7P,IAAI,CAAC/N,OAAO,EACZvB,KAAK,EACL2H,OAAO,EACPoU,UAAU,EACV9Z,YAAY,EACZ,KAAK,EACL2T,MAAM,CAACK,8BAA8B,EACrCwC,sBAAsB,EACtBC,uBAAuB,EACvBC,qBAAqB,EACrBQ,eAAe,EACfF,gBAAgB,EAChBD,gBAAgB,EAChB0D,WAAW,EACXtV,QAAQ,EACR,CAACa,KAAK,CAAC5B,KAAK,CAACQ,EAAE,EAAEsW,YAAY,CAC/B,CAAC,CAAA;;EAED;EACA;EACA;EACA+B,IAAAA,oBAAoB,CACjBpU,MAAM,CAAE2U,EAAE,IAAKA,EAAE,CAAC5e,GAAG,KAAKA,GAAG,CAAC,CAC9BoI,OAAO,CAAEwW,EAAE,IAAK;EACf,MAAA,IAAIgC,QAAQ,GAAGhC,EAAE,CAAC5e,GAAG,CAAA;QACrB,IAAIigB,eAAe,GAAG9gB,KAAK,CAAC8X,QAAQ,CAAClG,GAAG,CAAC6P,QAAQ,CAAC,CAAA;EAClD,MAAA,IAAInB,mBAAmB,GAAGC,iBAAiB,CACzCtgB,SAAS,EACT6gB,eAAe,GAAGA,eAAe,CAAC1Y,IAAI,GAAGnI,SAC3C,CAAC,CAAA;QACDD,KAAK,CAAC8X,QAAQ,CAAClI,GAAG,CAAC6R,QAAQ,EAAEnB,mBAAmB,CAAC,CAAA;QACjDZ,YAAY,CAAC+B,QAAQ,CAAC,CAAA;QACtB,IAAIhC,EAAE,CAAC7O,UAAU,EAAE;UACjBgI,gBAAgB,CAAChJ,GAAG,CAAC6R,QAAQ,EAAEhC,EAAE,CAAC7O,UAAU,CAAC,CAAA;EAC/C,OAAA;EACF,KAAC,CAAC,CAAA;EAEJiJ,IAAAA,WAAW,CAAC;EAAE/B,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC/X,KAAK,CAAC8X,QAAQ,CAAA;EAAE,KAAC,CAAC,CAAA;EAElD,IAAA,IAAI6H,8BAA8B,GAAGA,MACnCT,oBAAoB,CAACjW,OAAO,CAAEwW,EAAE,IAAKC,YAAY,CAACD,EAAE,CAAC5e,GAAG,CAAC,CAAC,CAAA;MAE5DogB,eAAe,CAACjQ,MAAM,CAACjL,gBAAgB,CACrC,OAAO,EACP4Z,8BACF,CAAC,CAAA;MAED,IAAI;QAAEE,aAAa;EAAEC,MAAAA,cAAAA;EAAe,KAAC,GACnC,MAAMC,8BAA8B,CAClC/f,KAAK,EACL2H,OAAO,EACPsX,aAAa,EACbC,oBAAoB,EACpBoC,mBACF,CAAC,CAAA;EAEH,IAAA,IAAIL,eAAe,CAACjQ,MAAM,CAACa,OAAO,EAAE;EAClC,MAAA,OAAA;EACF,KAAA;MAEAoP,eAAe,CAACjQ,MAAM,CAAChL,mBAAmB,CACxC,OAAO,EACP2Z,8BACF,CAAC,CAAA;EAED5G,IAAAA,cAAc,CAACjH,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC1B+X,IAAAA,gBAAgB,CAAC9G,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC5Bqe,IAAAA,oBAAoB,CAACjW,OAAO,CAAE0H,CAAC,IAAKiI,gBAAgB,CAAC9G,MAAM,CAACnB,CAAC,CAAC9P,GAAG,CAAC,CAAC,CAAA;EAEnE,IAAA,IAAIsS,QAAQ,GAAG6M,YAAY,CAACH,aAAa,CAAC,CAAA;EAC1C,IAAA,IAAI1M,QAAQ,EAAE;QACZ,OAAOuL,uBAAuB,CAC5B4C,mBAAmB,EACnBnO,QAAQ,CAACrJ,MAAM,EACf,KAAK,EACL;EAAE6N,QAAAA,kBAAAA;EAAmB,OACvB,CAAC,CAAA;EACH,KAAA;EAEAxE,IAAAA,QAAQ,GAAG6M,YAAY,CAACF,cAAc,CAAC,CAAA;EACvC,IAAA,IAAI3M,QAAQ,EAAE;EACZ;EACA;EACA;EACA6F,MAAAA,gBAAgB,CAAC3H,GAAG,CAAC8B,QAAQ,CAACtS,GAAG,CAAC,CAAA;QAClC,OAAO6d,uBAAuB,CAC5B4C,mBAAmB,EACnBnO,QAAQ,CAACrJ,MAAM,EACf,KAAK,EACL;EAAE6N,QAAAA,kBAAAA;EAAmB,OACvB,CAAC,CAAA;EACH,KAAA;;EAEA;MACA,IAAI;QAAEzP,UAAU;EAAEkP,MAAAA,MAAAA;EAAO,KAAC,GAAG6I,iBAAiB,CAC5CjgB,KAAK,EACL2H,OAAO,EACPkY,aAAa,EACb5f,SAAS,EACTif,oBAAoB,EACpBY,cAAc,EACd1G,eACF,CAAC,CAAA;;EAED;EACA;MACA,IAAIpZ,KAAK,CAAC8X,QAAQ,CAACnI,GAAG,CAAC9O,GAAG,CAAC,EAAE;EAC3B,MAAA,IAAI6gB,WAAW,GAAGL,cAAc,CAAClE,YAAY,CAAC/U,IAAI,CAAC,CAAA;QACnDpI,KAAK,CAAC8X,QAAQ,CAAClI,GAAG,CAAC/O,GAAG,EAAE6gB,WAAW,CAAC,CAAA;EACtC,KAAA;MAEAtB,oBAAoB,CAACmB,MAAM,CAAC,CAAA;;EAE5B;EACA;EACA;MACA,IACEvhB,KAAK,CAACyX,UAAU,CAACzX,KAAK,KAAK,SAAS,IACpCuhB,MAAM,GAAGzI,uBAAuB,EAChC;EACA9U,MAAAA,SAAS,CAACiU,aAAa,EAAE,yBAAyB,CAAC,CAAA;EACnDG,MAAAA,2BAA2B,IAAIA,2BAA2B,CAAC/F,KAAK,EAAE,CAAA;EAElEyI,MAAAA,kBAAkB,CAAC9a,KAAK,CAACyX,UAAU,CAAC3W,QAAQ,EAAE;UAC5C6G,OAAO;UACPO,UAAU;UACVkP,MAAM;EACNU,QAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC/X,KAAK,CAAC8X,QAAQ,CAAA;EAClC,OAAC,CAAC,CAAA;EACJ,KAAC,MAAM;EACL;EACA;EACA;EACA+B,MAAAA,WAAW,CAAC;UACVzC,MAAM;EACNlP,QAAAA,UAAU,EAAEoT,eAAe,CACzBtb,KAAK,CAACkI,UAAU,EAChBA,UAAU,EACVP,OAAO,EACPyP,MACF,CAAC;EACDU,QAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC/X,KAAK,CAAC8X,QAAQ,CAAA;EAClC,OAAC,CAAC,CAAA;EACFW,MAAAA,sBAAsB,GAAG,KAAK,CAAA;EAChC,KAAA;EACF,GAAA;;EAEA;EACA,EAAA,eAAekI,mBAAmBA,CAChC9f,GAAW,EACXyc,OAAe,EACf3b,IAAY,EACZsG,KAA6B,EAC7BN,OAAiC,EACjCkW,UAAmB,EACnBhD,SAAkB,EAClBlD,kBAA2B,EAC3BoE,UAAuB,EACvB;MACA,IAAI+E,eAAe,GAAG9gB,KAAK,CAAC8X,QAAQ,CAAClG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;EAC7CkgB,IAAAA,kBAAkB,CAChBlgB,GAAG,EACH0f,iBAAiB,CACfxE,UAAU,EACV+E,eAAe,GAAGA,eAAe,CAAC1Y,IAAI,GAAGnI,SAC3C,CAAC,EACD;EAAE4a,MAAAA,SAAAA;EAAU,KACd,CAAC,CAAA;EAED,IAAA,IAAIoG,eAAe,GAAG,IAAIpQ,eAAe,EAAE,CAAA;EAC3C,IAAA,IAAIqQ,YAAY,GAAGlE,uBAAuB,CACxC1N,IAAI,CAAC/N,OAAO,EACZI,IAAI,EACJsf,eAAe,CAACjQ,MAClB,CAAC,CAAA;EAED,IAAA,IAAI6M,UAAU,EAAE;QACd,IAAIE,cAAc,GAAG,MAAMC,cAAc,CACvCrW,OAAO,EACP,IAAIlG,GAAG,CAACyf,YAAY,CAACvd,GAAG,CAAC,CAAC3C,QAAQ,EAClCkgB,YAAY,CAAClQ,MAAM,EACnBnQ,GACF,CAAC,CAAA;EAED,MAAA,IAAIkd,cAAc,CAAC/N,IAAI,KAAK,SAAS,EAAE;EACrC,QAAA,OAAA;EACF,OAAC,MAAM,IAAI+N,cAAc,CAAC/N,IAAI,KAAK,OAAO,EAAE;UAC1CyQ,eAAe,CAAC5f,GAAG,EAAEyc,OAAO,EAAES,cAAc,CAACrY,KAAK,EAAE;EAAEmV,UAAAA,SAAAA;EAAU,SAAC,CAAC,CAAA;EAClE,QAAA,OAAA;EACF,OAAC,MAAM,IAAI,CAACkD,cAAc,CAACpW,OAAO,EAAE;UAClC8Y,eAAe,CACb5f,GAAG,EACHyc,OAAO,EACP3G,sBAAsB,CAAC,GAAG,EAAE;EAAE3V,UAAAA,QAAQ,EAAEW,IAAAA;EAAK,SAAC,CAAC,EAC/C;EAAEkZ,UAAAA,SAAAA;EAAU,SACd,CAAC,CAAA;EACD,QAAA,OAAA;EACF,OAAC,MAAM;UACLlT,OAAO,GAAGoW,cAAc,CAACpW,OAAO,CAAA;EAChCM,QAAAA,KAAK,GAAGmW,cAAc,CAACzW,OAAO,EAAEhG,IAAI,CAAC,CAAA;EACvC,OAAA;EACF,KAAA;;EAEA;EACAiX,IAAAA,gBAAgB,CAAChJ,GAAG,CAAC/O,GAAG,EAAEogB,eAAe,CAAC,CAAA;MAE1C,IAAIE,iBAAiB,GAAGtI,kBAAkB,CAAA;EAC1C,IAAA,IAAIyF,OAAO,GAAG,MAAMC,gBAAgB,CAClC,QAAQ,EACRve,KAAK,EACLkhB,YAAY,EACZ,CAACjZ,KAAK,CAAC,EACPN,OAAO,EACP9G,GACF,CAAC,CAAA;MACD,IAAIiJ,MAAM,GAAGwU,OAAO,CAACrW,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;;EAEpC;EACA;EACA;EACA;EACA,IAAA,IAAI8X,gBAAgB,CAAC7U,MAAM,CAAC,EAAE;EAC5BA,MAAAA,MAAM,GACJ,CAAC,MAAM6X,mBAAmB,CAAC7X,MAAM,EAAEoX,YAAY,CAAClQ,MAAM,EAAE,IAAI,CAAC,KAC7DlH,MAAM,CAAA;EACV,KAAA;;EAEA;EACA;MACA,IAAI8O,gBAAgB,CAAChH,GAAG,CAAC/Q,GAAG,CAAC,KAAKogB,eAAe,EAAE;EACjDrI,MAAAA,gBAAgB,CAAC9G,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC9B,KAAA;EAEA,IAAA,IAAIqgB,YAAY,CAAClQ,MAAM,CAACa,OAAO,EAAE;EAC/B,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA,IAAA,IAAIsH,eAAe,CAACxJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;EAC5BkgB,MAAAA,kBAAkB,CAAClgB,GAAG,EAAEwgB,cAAc,CAACphB,SAAS,CAAC,CAAC,CAAA;EAClD,MAAA,OAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAIue,gBAAgB,CAAC1U,MAAM,CAAC,EAAE;QAC5B,IAAIgP,uBAAuB,GAAGqI,iBAAiB,EAAE;EAC/C;EACA;EACAJ,QAAAA,kBAAkB,CAAClgB,GAAG,EAAEwgB,cAAc,CAACphB,SAAS,CAAC,CAAC,CAAA;EAClD,QAAA,OAAA;EACF,OAAC,MAAM;EACL+Y,QAAAA,gBAAgB,CAAC3H,GAAG,CAACxQ,GAAG,CAAC,CAAA;EACzB,QAAA,MAAM6d,uBAAuB,CAACwC,YAAY,EAAEpX,MAAM,EAAE,KAAK,EAAE;EACzD6N,UAAAA,kBAAAA;EACF,SAAC,CAAC,CAAA;EACF,QAAA,OAAA;EACF,OAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAI4F,aAAa,CAACzT,MAAM,CAAC,EAAE;QACzB2W,eAAe,CAAC5f,GAAG,EAAEyc,OAAO,EAAExT,MAAM,CAACpE,KAAK,CAAC,CAAA;EAC3C,MAAA,OAAA;EACF,KAAA;MAEA1B,SAAS,CAAC,CAAC2a,gBAAgB,CAAC7U,MAAM,CAAC,EAAE,iCAAiC,CAAC,CAAA;;EAEvE;MACAiX,kBAAkB,CAAClgB,GAAG,EAAEwgB,cAAc,CAACvX,MAAM,CAAC1B,IAAI,CAAC,CAAC,CAAA;EACtD,GAAA;;EAEA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACE,eAAesW,uBAAuBA,CACpC3B,OAAgB,EAChB5J,QAAwB,EACxByO,YAAqB,EAAAC,MAAA,EAYrB;MAAA,IAXA;QACE9F,UAAU;QACV4B,iBAAiB;QACjBhG,kBAAkB;EAClBvV,MAAAA,OAAAA;EAMF,KAAC,GAAAyf,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;MAEN,IAAI1O,QAAQ,CAACE,QAAQ,CAAC5D,OAAO,CAACE,GAAG,CAAC,oBAAoB,CAAC,EAAE;EACvD8I,MAAAA,sBAAsB,GAAG,IAAI,CAAA;EAC/B,KAAA;MAEA,IAAI3X,QAAQ,GAAGqS,QAAQ,CAACE,QAAQ,CAAC5D,OAAO,CAACmC,GAAG,CAAC,UAAU,CAAC,CAAA;EACxD5N,IAAAA,SAAS,CAAClD,QAAQ,EAAE,qDAAqD,CAAC,CAAA;EAC1EA,IAAAA,QAAQ,GAAG2d,yBAAyB,CAClC3d,QAAQ,EACR,IAAIW,GAAG,CAACsb,OAAO,CAACpZ,GAAG,CAAC,EACpByD,QACF,CAAC,CAAA;MACD,IAAI0a,gBAAgB,GAAG/gB,cAAc,CAACf,KAAK,CAACc,QAAQ,EAAEA,QAAQ,EAAE;EAC9Dsa,MAAAA,WAAW,EAAE,IAAA;EACf,KAAC,CAAC,CAAA;EAEF,IAAA,IAAInG,SAAS,EAAE;QACb,IAAI8M,gBAAgB,GAAG,KAAK,CAAA;QAE5B,IAAI5O,QAAQ,CAACE,QAAQ,CAAC5D,OAAO,CAACE,GAAG,CAAC,yBAAyB,CAAC,EAAE;EAC5D;EACAoS,QAAAA,gBAAgB,GAAG,IAAI,CAAA;SACxB,MAAM,IAAIrN,kBAAkB,CAACzJ,IAAI,CAACnK,QAAQ,CAAC,EAAE;UAC5C,MAAM6C,GAAG,GAAG2L,IAAI,CAAC/N,OAAO,CAACC,SAAS,CAACV,QAAQ,CAAC,CAAA;UAC5CihB,gBAAgB;EACd;EACApe,QAAAA,GAAG,CAACmC,MAAM,KAAKkP,YAAY,CAAClU,QAAQ,CAACgF,MAAM;EAC3C;UACAyB,aAAa,CAAC5D,GAAG,CAAC3C,QAAQ,EAAEoG,QAAQ,CAAC,IAAI,IAAI,CAAA;EACjD,OAAA;EAEA,MAAA,IAAI2a,gBAAgB,EAAE;EACpB,QAAA,IAAI3f,OAAO,EAAE;EACX4S,UAAAA,YAAY,CAAClU,QAAQ,CAACsB,OAAO,CAACtB,QAAQ,CAAC,CAAA;EACzC,SAAC,MAAM;EACLkU,UAAAA,YAAY,CAAClU,QAAQ,CAAC+E,MAAM,CAAC/E,QAAQ,CAAC,CAAA;EACxC,SAAA;EACA,QAAA,OAAA;EACF,OAAA;EACF,KAAA;;EAEA;EACA;EACAsX,IAAAA,2BAA2B,GAAG,IAAI,CAAA;MAElC,IAAI4J,qBAAqB,GACvB5f,OAAO,KAAK,IAAI,IAAI+Q,QAAQ,CAACE,QAAQ,CAAC5D,OAAO,CAACE,GAAG,CAAC,iBAAiB,CAAC,GAChEuI,MAAa,CAAC7V,OAAO,GACrB6V,MAAa,CAAClW,IAAI,CAAA;;EAExB;EACA;MACA,IAAI;QAAEiS,UAAU;QAAEC,UAAU;EAAEC,MAAAA,WAAAA;OAAa,GAAGnU,KAAK,CAACyX,UAAU,CAAA;MAC9D,IACE,CAACsE,UAAU,IACX,CAAC4B,iBAAiB,IAClB1J,UAAU,IACVC,UAAU,IACVC,WAAW,EACX;EACA4H,MAAAA,UAAU,GAAG+C,2BAA2B,CAAC9e,KAAK,CAACyX,UAAU,CAAC,CAAA;EAC5D,KAAA;;EAEA;EACA;EACA;EACA,IAAA,IAAIoH,gBAAgB,GAAG9C,UAAU,IAAI4B,iBAAiB,CAAA;EACtD,IAAA,IACE5J,iCAAiC,CAACpE,GAAG,CAACwD,QAAQ,CAACE,QAAQ,CAAC7D,MAAM,CAAC,IAC/DqP,gBAAgB,IAChB1D,gBAAgB,CAAC0D,gBAAgB,CAAC5K,UAAU,CAAC,EAC7C;EACA,MAAA,MAAM6F,eAAe,CAACkI,qBAAqB,EAAEF,gBAAgB,EAAE;UAC7D/F,UAAU,EAAAjX,QAAA,CAAA,EAAA,EACL+Z,gBAAgB,EAAA;EACnB3K,UAAAA,UAAU,EAAEpT,QAAAA;WACb,CAAA;EACD;UACA6W,kBAAkB,EAAEA,kBAAkB,IAAIQ,yBAAyB;EACnEgE,QAAAA,oBAAoB,EAAEyF,YAAY,GAC9BvJ,4BAA4B,GAC5BpY,SAAAA;EACN,OAAC,CAAC,CAAA;EACJ,KAAC,MAAM;EACL;EACA;EACA,MAAA,IAAIuc,kBAAkB,GAAGgB,oBAAoB,CAC3CsE,gBAAgB,EAChB/F,UACF,CAAC,CAAA;EACD,MAAA,MAAMjC,eAAe,CAACkI,qBAAqB,EAAEF,gBAAgB,EAAE;UAC7DtF,kBAAkB;EAClB;UACAmB,iBAAiB;EACjB;UACAhG,kBAAkB,EAAEA,kBAAkB,IAAIQ,yBAAyB;EACnEgE,QAAAA,oBAAoB,EAAEyF,YAAY,GAC9BvJ,4BAA4B,GAC5BpY,SAAAA;EACN,OAAC,CAAC,CAAA;EACJ,KAAA;EACF,GAAA;;EAEA;EACA;EACA,EAAA,eAAese,gBAAgBA,CAC7BvO,IAAyB,EACzBhQ,KAAkB,EAClB+c,OAAgB,EAChBkC,aAAuC,EACvCtX,OAAiC,EACjCsa,UAAyB,EACY;EACrC,IAAA,IAAI3D,OAA2C,CAAA;MAC/C,IAAI4D,WAAuC,GAAG,EAAE,CAAA;MAChD,IAAI;QACF5D,OAAO,GAAG,MAAM6D,oBAAoB,CAClC5M,gBAAgB,EAChBvF,IAAI,EACJhQ,KAAK,EACL+c,OAAO,EACPkC,aAAa,EACbtX,OAAO,EACPsa,UAAU,EACVvb,QAAQ,EACRF,kBACF,CAAC,CAAA;OACF,CAAC,OAAOjC,CAAC,EAAE;EACV;EACA;EACA0a,MAAAA,aAAa,CAAChW,OAAO,CAAEgO,CAAC,IAAK;EAC3BiL,QAAAA,WAAW,CAACjL,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,CAAC,GAAG;YACxBmJ,IAAI,EAAE/J,UAAU,CAACP,KAAK;EACtBA,UAAAA,KAAK,EAAEnB,CAAAA;WACR,CAAA;EACH,OAAC,CAAC,CAAA;EACF,MAAA,OAAO2d,WAAW,CAAA;EACpB,KAAA;EAEA,IAAA,KAAK,IAAI,CAAC5E,OAAO,EAAExT,MAAM,CAAC,IAAI4B,MAAM,CAAC/L,OAAO,CAAC2e,OAAO,CAAC,EAAE;EACrD,MAAA,IAAI8D,kCAAkC,CAACtY,MAAM,CAAC,EAAE;EAC9C,QAAA,IAAIuJ,QAAQ,GAAGvJ,MAAM,CAACA,MAAkB,CAAA;UACxCoY,WAAW,CAAC5E,OAAO,CAAC,GAAG;YACrBtN,IAAI,EAAE/J,UAAU,CAACkN,QAAQ;EACzBE,UAAAA,QAAQ,EAAEgP,wCAAwC,CAChDhP,QAAQ,EACR0J,OAAO,EACPO,OAAO,EACP3V,OAAO,EACPP,QAAQ,EACRwO,MAAM,CAACvH,oBACT,CAAA;WACD,CAAA;EACH,OAAC,MAAM;UACL6T,WAAW,CAAC5E,OAAO,CAAC,GAAG,MAAMgF,qCAAqC,CAChExY,MACF,CAAC,CAAA;EACH,OAAA;EACF,KAAA;EAEA,IAAA,OAAOoY,WAAW,CAAA;EACpB,GAAA;IAEA,eAAenC,8BAA8BA,CAC3C/f,KAAkB,EAClB2H,OAAiC,EACjCsX,aAAuC,EACvCsD,cAAqC,EACrCxF,OAAgB,EAChB;EACA,IAAA,IAAIyF,cAAc,GAAGxiB,KAAK,CAAC2H,OAAO,CAAA;;EAElC;EACA,IAAA,IAAI8a,oBAAoB,GAAGlE,gBAAgB,CACzC,QAAQ,EACRve,KAAK,EACL+c,OAAO,EACPkC,aAAa,EACbtX,OAAO,EACP,IACF,CAAC,CAAA;EAED,IAAA,IAAI+a,qBAAqB,GAAGhS,OAAO,CAACiS,GAAG,CACrCJ,cAAc,CAAC3iB,GAAG,CAAC,MAAOggB,CAAC,IAAK;QAC9B,IAAIA,CAAC,CAACjY,OAAO,IAAIiY,CAAC,CAAC3X,KAAK,IAAI2X,CAAC,CAAChP,UAAU,EAAE;EACxC,QAAA,IAAI0N,OAAO,GAAG,MAAMC,gBAAgB,CAClC,QAAQ,EACRve,KAAK,EACLgd,uBAAuB,CAAC1N,IAAI,CAAC/N,OAAO,EAAEqe,CAAC,CAACje,IAAI,EAAEie,CAAC,CAAChP,UAAU,CAACI,MAAM,CAAC,EAClE,CAAC4O,CAAC,CAAC3X,KAAK,CAAC,EACT2X,CAAC,CAACjY,OAAO,EACTiY,CAAC,CAAC/e,GACJ,CAAC,CAAA;UACD,IAAIiJ,MAAM,GAAGwU,OAAO,CAACsB,CAAC,CAAC3X,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;EACtC;UACA,OAAO;YAAE,CAAC+Y,CAAC,CAAC/e,GAAG,GAAGiJ,MAAAA;WAAQ,CAAA;EAC5B,OAAC,MAAM;UACL,OAAO4G,OAAO,CAAC8B,OAAO,CAAC;YACrB,CAACoN,CAAC,CAAC/e,GAAG,GAAG;cACPmP,IAAI,EAAE/J,UAAU,CAACP,KAAK;EACtBA,YAAAA,KAAK,EAAEiR,sBAAsB,CAAC,GAAG,EAAE;gBACjC3V,QAAQ,EAAE4e,CAAC,CAACje,IAAAA;eACb,CAAA;EACH,WAAA;EACF,SAAC,CAAC,CAAA;EACJ,OAAA;EACF,KAAC,CACH,CAAC,CAAA;MAED,IAAIke,aAAa,GAAG,MAAM4C,oBAAoB,CAAA;MAC9C,IAAI3C,cAAc,GAAG,CAAC,MAAM4C,qBAAqB,EAAE3X,MAAM,CACvD,CAACkG,GAAG,EAAEN,CAAC,KAAKjF,MAAM,CAAC7F,MAAM,CAACoL,GAAG,EAAEN,CAAC,CAAC,EACjC,EACF,CAAC,CAAA;EAED,IAAA,MAAMD,OAAO,CAACiS,GAAG,CAAC,CAChBC,gCAAgC,CAC9Bjb,OAAO,EACPkY,aAAa,EACb9C,OAAO,CAAC/L,MAAM,EACdwR,cAAc,EACdxiB,KAAK,CAACkI,UACR,CAAC,EACD2a,6BAA6B,CAAClb,OAAO,EAAEmY,cAAc,EAAEyC,cAAc,CAAC,CACvE,CAAC,CAAA;MAEF,OAAO;QACL1C,aAAa;EACbC,MAAAA,cAAAA;OACD,CAAA;EACH,GAAA;IAEA,SAASxD,oBAAoBA,GAAG;EAC9B;EACA7D,IAAAA,sBAAsB,GAAG,IAAI,CAAA;;EAE7B;EACA;EACAC,IAAAA,uBAAuB,CAAC3W,IAAI,CAAC,GAAGqd,qBAAqB,EAAE,CAAC,CAAA;;EAExD;EACAnG,IAAAA,gBAAgB,CAAChQ,OAAO,CAAC,CAAC+D,CAAC,EAAEnM,GAAG,KAAK;EACnC,MAAA,IAAI+X,gBAAgB,CAACjJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;EAC7B8X,QAAAA,qBAAqB,CAACtH,GAAG,CAACxQ,GAAG,CAAC,CAAA;EAChC,OAAA;QACA6e,YAAY,CAAC7e,GAAG,CAAC,CAAA;EACnB,KAAC,CAAC,CAAA;EACJ,GAAA;EAEA,EAAA,SAASkgB,kBAAkBA,CACzBlgB,GAAW,EACX8Z,OAAgB,EAChBH,IAA6B,EAC7B;EAAA,IAAA,IADAA,IAA6B,KAAA,KAAA,CAAA,EAAA;QAA7BA,IAA6B,GAAG,EAAE,CAAA;EAAA,KAAA;MAElCxa,KAAK,CAAC8X,QAAQ,CAAClI,GAAG,CAAC/O,GAAG,EAAE8Z,OAAO,CAAC,CAAA;EAChCd,IAAAA,WAAW,CACT;EAAE/B,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC/X,KAAK,CAAC8X,QAAQ,CAAA;EAAE,KAAC,EACrC;EAAE+C,MAAAA,SAAS,EAAE,CAACL,IAAI,IAAIA,IAAI,CAACK,SAAS,MAAM,IAAA;EAAK,KACjD,CAAC,CAAA;EACH,GAAA;IAEA,SAAS4F,eAAeA,CACtB5f,GAAW,EACXyc,OAAe,EACf5X,KAAU,EACV8U,IAA6B,EAC7B;EAAA,IAAA,IADAA,IAA6B,KAAA,KAAA,CAAA,EAAA;QAA7BA,IAA6B,GAAG,EAAE,CAAA;EAAA,KAAA;MAElC,IAAIoE,aAAa,GAAG1B,mBAAmB,CAACld,KAAK,CAAC2H,OAAO,EAAE2V,OAAO,CAAC,CAAA;MAC/DjD,aAAa,CAACxZ,GAAG,CAAC,CAAA;EAClBgZ,IAAAA,WAAW,CACT;EACEzC,MAAAA,MAAM,EAAE;EACN,QAAA,CAACwH,aAAa,CAACvY,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;SAC3B;EACDoS,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC/X,KAAK,CAAC8X,QAAQ,CAAA;EAClC,KAAC,EACD;EAAE+C,MAAAA,SAAS,EAAE,CAACL,IAAI,IAAIA,IAAI,CAACK,SAAS,MAAM,IAAA;EAAK,KACjD,CAAC,CAAA;EACH,GAAA;IAEA,SAASiI,UAAUA,CAAcjiB,GAAW,EAAkB;EAC5DqY,IAAAA,cAAc,CAACtJ,GAAG,CAAC/O,GAAG,EAAE,CAACqY,cAAc,CAACtH,GAAG,CAAC/Q,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;EAC3D;EACA;EACA,IAAA,IAAIsY,eAAe,CAACxJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;EAC5BsY,MAAAA,eAAe,CAACrH,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC7B,KAAA;MACA,OAAOb,KAAK,CAAC8X,QAAQ,CAAClG,GAAG,CAAC/Q,GAAG,CAAC,IAAIyT,YAAY,CAAA;EAChD,GAAA;IAEA,SAAS+F,aAAaA,CAACxZ,GAAW,EAAQ;MACxC,IAAI8Z,OAAO,GAAG3a,KAAK,CAAC8X,QAAQ,CAAClG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;EACrC;EACA;EACA;MACA,IACE+X,gBAAgB,CAACjJ,GAAG,CAAC9O,GAAG,CAAC,IACzB,EAAE8Z,OAAO,IAAIA,OAAO,CAAC3a,KAAK,KAAK,SAAS,IAAI+Y,cAAc,CAACpJ,GAAG,CAAC9O,GAAG,CAAC,CAAC,EACpE;QACA6e,YAAY,CAAC7e,GAAG,CAAC,CAAA;EACnB,KAAA;EACAoY,IAAAA,gBAAgB,CAACnH,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC5BkY,IAAAA,cAAc,CAACjH,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC1BmY,IAAAA,gBAAgB,CAAClH,MAAM,CAACjR,GAAG,CAAC,CAAA;;EAE5B;EACA;EACA;EACA;EACA;EACA;MACA,IAAI+U,MAAM,CAACC,iBAAiB,EAAE;EAC5BsD,MAAAA,eAAe,CAACrH,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC7B,KAAA;EAEA8X,IAAAA,qBAAqB,CAAC7G,MAAM,CAACjR,GAAG,CAAC,CAAA;EACjCb,IAAAA,KAAK,CAAC8X,QAAQ,CAAChG,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC5B,GAAA;IAEA,SAASkiB,2BAA2BA,CAACliB,GAAW,EAAQ;EACtD,IAAA,IAAImiB,KAAK,GAAG,CAAC9J,cAAc,CAACtH,GAAG,CAAC/Q,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;MAC9C,IAAImiB,KAAK,IAAI,CAAC,EAAE;EACd9J,MAAAA,cAAc,CAACpH,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC1BsY,MAAAA,eAAe,CAAC9H,GAAG,CAACxQ,GAAG,CAAC,CAAA;EACxB,MAAA,IAAI,CAAC+U,MAAM,CAACC,iBAAiB,EAAE;UAC7BwE,aAAa,CAACxZ,GAAG,CAAC,CAAA;EACpB,OAAA;EACF,KAAC,MAAM;EACLqY,MAAAA,cAAc,CAACtJ,GAAG,CAAC/O,GAAG,EAAEmiB,KAAK,CAAC,CAAA;EAChC,KAAA;EAEAnJ,IAAAA,WAAW,CAAC;EAAE/B,MAAAA,QAAQ,EAAE,IAAIC,GAAG,CAAC/X,KAAK,CAAC8X,QAAQ,CAAA;EAAE,KAAC,CAAC,CAAA;EACpD,GAAA;IAEA,SAAS4H,YAAYA,CAAC7e,GAAW,EAAE;EACjC,IAAA,IAAI+P,UAAU,GAAGgI,gBAAgB,CAAChH,GAAG,CAAC/Q,GAAG,CAAC,CAAA;EAC1C,IAAA,IAAI+P,UAAU,EAAE;QACdA,UAAU,CAACyB,KAAK,EAAE,CAAA;EAClBuG,MAAAA,gBAAgB,CAAC9G,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC9B,KAAA;EACF,GAAA;IAEA,SAASoiB,gBAAgBA,CAAC5H,IAAc,EAAE;EACxC,IAAA,KAAK,IAAIxa,GAAG,IAAIwa,IAAI,EAAE;EACpB,MAAA,IAAIV,OAAO,GAAGmI,UAAU,CAACjiB,GAAG,CAAC,CAAA;EAC7B,MAAA,IAAI6gB,WAAW,GAAGL,cAAc,CAAC1G,OAAO,CAACvS,IAAI,CAAC,CAAA;QAC9CpI,KAAK,CAAC8X,QAAQ,CAAClI,GAAG,CAAC/O,GAAG,EAAE6gB,WAAW,CAAC,CAAA;EACtC,KAAA;EACF,GAAA;IAEA,SAASpC,sBAAsBA,GAAY;MACzC,IAAI4D,QAAQ,GAAG,EAAE,CAAA;MACjB,IAAI7D,eAAe,GAAG,KAAK,CAAA;EAC3B,IAAA,KAAK,IAAIxe,GAAG,IAAImY,gBAAgB,EAAE;QAChC,IAAI2B,OAAO,GAAG3a,KAAK,CAAC8X,QAAQ,CAAClG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;EACrCmD,MAAAA,SAAS,CAAC2W,OAAO,EAAuB9Z,oBAAAA,GAAAA,GAAK,CAAC,CAAA;EAC9C,MAAA,IAAI8Z,OAAO,CAAC3a,KAAK,KAAK,SAAS,EAAE;EAC/BgZ,QAAAA,gBAAgB,CAAClH,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC5BqiB,QAAAA,QAAQ,CAACnhB,IAAI,CAAClB,GAAG,CAAC,CAAA;EAClBwe,QAAAA,eAAe,GAAG,IAAI,CAAA;EACxB,OAAA;EACF,KAAA;MACA4D,gBAAgB,CAACC,QAAQ,CAAC,CAAA;EAC1B,IAAA,OAAO7D,eAAe,CAAA;EACxB,GAAA;IAEA,SAASe,oBAAoBA,CAAC+C,QAAgB,EAAW;MACvD,IAAIC,UAAU,GAAG,EAAE,CAAA;MACnB,KAAK,IAAI,CAACviB,GAAG,EAAEgG,EAAE,CAAC,IAAIkS,cAAc,EAAE;QACpC,IAAIlS,EAAE,GAAGsc,QAAQ,EAAE;UACjB,IAAIxI,OAAO,GAAG3a,KAAK,CAAC8X,QAAQ,CAAClG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;EACrCmD,QAAAA,SAAS,CAAC2W,OAAO,EAAuB9Z,oBAAAA,GAAAA,GAAK,CAAC,CAAA;EAC9C,QAAA,IAAI8Z,OAAO,CAAC3a,KAAK,KAAK,SAAS,EAAE;YAC/B0f,YAAY,CAAC7e,GAAG,CAAC,CAAA;EACjBkY,UAAAA,cAAc,CAACjH,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC1BuiB,UAAAA,UAAU,CAACrhB,IAAI,CAAClB,GAAG,CAAC,CAAA;EACtB,SAAA;EACF,OAAA;EACF,KAAA;MACAoiB,gBAAgB,CAACG,UAAU,CAAC,CAAA;EAC5B,IAAA,OAAOA,UAAU,CAACjjB,MAAM,GAAG,CAAC,CAAA;EAC9B,GAAA;EAEA,EAAA,SAASkjB,UAAUA,CAACxiB,GAAW,EAAE4B,EAAmB,EAAE;MACpD,IAAI6gB,OAAgB,GAAGtjB,KAAK,CAACgY,QAAQ,CAACpG,GAAG,CAAC/Q,GAAG,CAAC,IAAI0T,YAAY,CAAA;MAE9D,IAAI8E,gBAAgB,CAACzH,GAAG,CAAC/Q,GAAG,CAAC,KAAK4B,EAAE,EAAE;EACpC4W,MAAAA,gBAAgB,CAACzJ,GAAG,CAAC/O,GAAG,EAAE4B,EAAE,CAAC,CAAA;EAC/B,KAAA;EAEA,IAAA,OAAO6gB,OAAO,CAAA;EAChB,GAAA;IAEA,SAAShJ,aAAaA,CAACzZ,GAAW,EAAE;EAClCb,IAAAA,KAAK,CAACgY,QAAQ,CAAClG,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC1BwY,IAAAA,gBAAgB,CAACvH,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC9B,GAAA;;EAEA;EACA,EAAA,SAAS+Y,aAAaA,CAAC/Y,GAAW,EAAE0iB,UAAmB,EAAE;MACvD,IAAID,OAAO,GAAGtjB,KAAK,CAACgY,QAAQ,CAACpG,GAAG,CAAC/Q,GAAG,CAAC,IAAI0T,YAAY,CAAA;;EAErD;EACA;EACAvQ,IAAAA,SAAS,CACNsf,OAAO,CAACtjB,KAAK,KAAK,WAAW,IAAIujB,UAAU,CAACvjB,KAAK,KAAK,SAAS,IAC7DsjB,OAAO,CAACtjB,KAAK,KAAK,SAAS,IAAIujB,UAAU,CAACvjB,KAAK,KAAK,SAAU,IAC9DsjB,OAAO,CAACtjB,KAAK,KAAK,SAAS,IAAIujB,UAAU,CAACvjB,KAAK,KAAK,YAAa,IACjEsjB,OAAO,CAACtjB,KAAK,KAAK,SAAS,IAAIujB,UAAU,CAACvjB,KAAK,KAAK,WAAY,IAChEsjB,OAAO,CAACtjB,KAAK,KAAK,YAAY,IAAIujB,UAAU,CAACvjB,KAAK,KAAK,WAAY,EAAA,oCAAA,GACjCsjB,OAAO,CAACtjB,KAAK,GAAA,MAAA,GAAOujB,UAAU,CAACvjB,KACtE,CAAC,CAAA;MAED,IAAIgY,QAAQ,GAAG,IAAID,GAAG,CAAC/X,KAAK,CAACgY,QAAQ,CAAC,CAAA;EACtCA,IAAAA,QAAQ,CAACpI,GAAG,CAAC/O,GAAG,EAAE0iB,UAAU,CAAC,CAAA;EAC7B1J,IAAAA,WAAW,CAAC;EAAE7B,MAAAA,QAAAA;EAAS,KAAC,CAAC,CAAA;EAC3B,GAAA;IAEA,SAASyB,qBAAqBA,CAAAvI,KAAA,EAQP;MAAA,IARQ;QAC7BwI,eAAe;QACfzX,YAAY;EACZuV,MAAAA,aAAAA;EAKF,KAAC,GAAAtG,KAAA,CAAA;EACC,IAAA,IAAImI,gBAAgB,CAAC5G,IAAI,KAAK,CAAC,EAAE;EAC/B,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA,IAAA,IAAI4G,gBAAgB,CAAC5G,IAAI,GAAG,CAAC,EAAE;EAC7BxR,MAAAA,OAAO,CAAC,KAAK,EAAE,8CAA8C,CAAC,CAAA;EAChE,KAAA;MAEA,IAAItB,OAAO,GAAG2Q,KAAK,CAACzB,IAAI,CAACwK,gBAAgB,CAAC1Z,OAAO,EAAE,CAAC,CAAA;EACpD,IAAA,IAAI,CAAC6Z,UAAU,EAAEgK,eAAe,CAAC,GAAG7jB,OAAO,CAACA,OAAO,CAACQ,MAAM,GAAG,CAAC,CAAC,CAAA;MAC/D,IAAImjB,OAAO,GAAGtjB,KAAK,CAACgY,QAAQ,CAACpG,GAAG,CAAC4H,UAAU,CAAC,CAAA;EAE5C,IAAA,IAAI8J,OAAO,IAAIA,OAAO,CAACtjB,KAAK,KAAK,YAAY,EAAE;EAC7C;EACA;EACA,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA,IAAA,IAAIwjB,eAAe,CAAC;QAAE9J,eAAe;QAAEzX,YAAY;EAAEuV,MAAAA,aAAAA;EAAc,KAAC,CAAC,EAAE;EACrE,MAAA,OAAOgC,UAAU,CAAA;EACnB,KAAA;EACF,GAAA;IAEA,SAASsD,qBAAqBA,CAAC9b,QAAgB,EAAE;EAC/C,IAAA,IAAI0E,KAAK,GAAGiR,sBAAsB,CAAC,GAAG,EAAE;EAAE3V,MAAAA,QAAAA;EAAS,KAAC,CAAC,CAAA;EACrD,IAAA,IAAI0b,WAAW,GAAGpH,kBAAkB,IAAID,UAAU,CAAA;MAClD,IAAI;QAAE1N,OAAO;EAAEtB,MAAAA,KAAAA;EAAM,KAAC,GAAGuQ,sBAAsB,CAAC8F,WAAW,CAAC,CAAA;;EAE5D;EACA0C,IAAAA,qBAAqB,EAAE,CAAA;MAEvB,OAAO;EAAEvC,MAAAA,eAAe,EAAElV,OAAO;QAAEtB,KAAK;EAAEX,MAAAA,KAAAA;OAAO,CAAA;EACnD,GAAA;IAEA,SAAS0Z,qBAAqBA,CAC5BqE,SAAwC,EAC9B;MACV,IAAIC,iBAA2B,GAAG,EAAE,CAAA;EACpCtK,IAAAA,eAAe,CAACnQ,OAAO,CAAC,CAAC0a,GAAG,EAAErG,OAAO,KAAK;EACxC,MAAA,IAAI,CAACmG,SAAS,IAAIA,SAAS,CAACnG,OAAO,CAAC,EAAE;EACpC;EACA;EACA;UACAqG,GAAG,CAACvR,MAAM,EAAE,CAAA;EACZsR,QAAAA,iBAAiB,CAAC3hB,IAAI,CAACub,OAAO,CAAC,CAAA;EAC/BlE,QAAAA,eAAe,CAACtH,MAAM,CAACwL,OAAO,CAAC,CAAA;EACjC,OAAA;EACF,KAAC,CAAC,CAAA;EACF,IAAA,OAAOoG,iBAAiB,CAAA;EAC1B,GAAA;;EAEA;EACA;EACA,EAAA,SAASE,uBAAuBA,CAC9BC,SAAiC,EACjCC,WAAsC,EACtCC,MAAwC,EACxC;EACA5N,IAAAA,oBAAoB,GAAG0N,SAAS,CAAA;EAChCxN,IAAAA,iBAAiB,GAAGyN,WAAW,CAAA;MAC/B1N,uBAAuB,GAAG2N,MAAM,IAAI,IAAI,CAAA;;EAExC;EACA;EACA;MACA,IAAI,CAACzN,qBAAqB,IAAItW,KAAK,CAACyX,UAAU,KAAKzD,eAAe,EAAE;EAClEsC,MAAAA,qBAAqB,GAAG,IAAI,CAAA;QAC5B,IAAI0N,CAAC,GAAGvI,sBAAsB,CAACzb,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAAC2H,OAAO,CAAC,CAAA;QAC7D,IAAIqc,CAAC,IAAI,IAAI,EAAE;EACbnK,QAAAA,WAAW,CAAC;EAAEnC,UAAAA,qBAAqB,EAAEsM,CAAAA;EAAE,SAAC,CAAC,CAAA;EAC3C,OAAA;EACF,KAAA;EAEA,IAAA,OAAO,MAAM;EACX7N,MAAAA,oBAAoB,GAAG,IAAI,CAAA;EAC3BE,MAAAA,iBAAiB,GAAG,IAAI,CAAA;EACxBD,MAAAA,uBAAuB,GAAG,IAAI,CAAA;OAC/B,CAAA;EACH,GAAA;EAEA,EAAA,SAAS6N,YAAYA,CAACnjB,QAAkB,EAAE6G,OAAiC,EAAE;EAC3E,IAAA,IAAIyO,uBAAuB,EAAE;QAC3B,IAAIvV,GAAG,GAAGuV,uBAAuB,CAC/BtV,QAAQ,EACR6G,OAAO,CAAC/H,GAAG,CAAEqX,CAAC,IAAKjP,0BAA0B,CAACiP,CAAC,EAAEjX,KAAK,CAACkI,UAAU,CAAC,CACpE,CAAC,CAAA;EACD,MAAA,OAAOrH,GAAG,IAAIC,QAAQ,CAACD,GAAG,CAAA;EAC5B,KAAA;MACA,OAAOC,QAAQ,CAACD,GAAG,CAAA;EACrB,GAAA;EAEA,EAAA,SAAS4b,kBAAkBA,CACzB3b,QAAkB,EAClB6G,OAAiC,EAC3B;MACN,IAAIwO,oBAAoB,IAAIE,iBAAiB,EAAE;EAC7C,MAAA,IAAIxV,GAAG,GAAGojB,YAAY,CAACnjB,QAAQ,EAAE6G,OAAO,CAAC,CAAA;EACzCwO,MAAAA,oBAAoB,CAACtV,GAAG,CAAC,GAAGwV,iBAAiB,EAAE,CAAA;EACjD,KAAA;EACF,GAAA;EAEA,EAAA,SAASoF,sBAAsBA,CAC7B3a,QAAkB,EAClB6G,OAAiC,EAClB;EACf,IAAA,IAAIwO,oBAAoB,EAAE;EACxB,MAAA,IAAItV,GAAG,GAAGojB,YAAY,CAACnjB,QAAQ,EAAE6G,OAAO,CAAC,CAAA;EACzC,MAAA,IAAIqc,CAAC,GAAG7N,oBAAoB,CAACtV,GAAG,CAAC,CAAA;EACjC,MAAA,IAAI,OAAOmjB,CAAC,KAAK,QAAQ,EAAE;EACzB,QAAA,OAAOA,CAAC,CAAA;EACV,OAAA;EACF,KAAA;EACA,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;EAEA,EAAA,SAASlN,aAAaA,CACpBnP,OAAwC,EACxC+U,WAAsC,EACtC1b,QAAgB,EAC+C;EAC/D,IAAA,IAAI0U,2BAA2B,EAAE;QAC/B,IAAI,CAAC/N,OAAO,EAAE;UACZ,IAAIuc,UAAU,GAAG7c,eAAe,CAC9BqV,WAAW,EACX1b,QAAQ,EACRoG,QAAQ,EACR,IACF,CAAC,CAAA;UAED,OAAO;EAAE2P,UAAAA,MAAM,EAAE,IAAI;YAAEpP,OAAO,EAAEuc,UAAU,IAAI,EAAA;WAAI,CAAA;EACpD,OAAC,MAAM;EACL,QAAA,IAAIxY,MAAM,CAAC2P,IAAI,CAAC1T,OAAO,CAAC,CAAC,CAAC,CAACQ,MAAM,CAAC,CAAChI,MAAM,GAAG,CAAC,EAAE;EAC7C;EACA;EACA;YACA,IAAI+d,cAAc,GAAG7W,eAAe,CAClCqV,WAAW,EACX1b,QAAQ,EACRoG,QAAQ,EACR,IACF,CAAC,CAAA;YACD,OAAO;EAAE2P,YAAAA,MAAM,EAAE,IAAI;EAAEpP,YAAAA,OAAO,EAAEuW,cAAAA;aAAgB,CAAA;EAClD,SAAA;EACF,OAAA;EACF,KAAA;MAEA,OAAO;EAAEnH,MAAAA,MAAM,EAAE,KAAK;EAAEpP,MAAAA,OAAO,EAAE,IAAA;OAAM,CAAA;EACzC,GAAA;IAiBA,eAAeqW,cAAcA,CAC3BrW,OAAiC,EACjC3G,QAAgB,EAChBgQ,MAAmB,EACnBiR,UAAmB,EACY;MAC/B,IAAI,CAACvM,2BAA2B,EAAE;QAChC,OAAO;EAAE1F,QAAAA,IAAI,EAAE,SAAS;EAAErI,QAAAA,OAAAA;SAAS,CAAA;EACrC,KAAA;MAEA,IAAIuW,cAA+C,GAAGvW,OAAO,CAAA;EAC7D,IAAA,OAAO,IAAI,EAAE;EACX,MAAA,IAAIwc,QAAQ,GAAG7O,kBAAkB,IAAI,IAAI,CAAA;EACzC,MAAA,IAAIoH,WAAW,GAAGpH,kBAAkB,IAAID,UAAU,CAAA;QAClD,IAAI+O,aAAa,GAAG1d,QAAQ,CAAA;QAC5B,IAAI;EACF,QAAA,MAAMgP,2BAA2B,CAAC;YAChC1E,MAAM;EACNrP,UAAAA,IAAI,EAAEX,QAAQ;EACd2G,UAAAA,OAAO,EAAEuW,cAAc;YACvB+D,UAAU;EACVoC,UAAAA,KAAK,EAAEA,CAAC/G,OAAO,EAAEvW,QAAQ,KAAK;cAC5B,IAAIiK,MAAM,CAACa,OAAO,EAAE,OAAA;cACpByS,eAAe,CACbhH,OAAO,EACPvW,QAAQ,EACR2V,WAAW,EACX0H,aAAa,EACb5d,kBACF,CAAC,CAAA;EACH,WAAA;EACF,SAAC,CAAC,CAAA;SACH,CAAC,OAAOjC,CAAC,EAAE;UACV,OAAO;EAAEyL,UAAAA,IAAI,EAAE,OAAO;EAAEtK,UAAAA,KAAK,EAAEnB,CAAC;EAAE2Z,UAAAA,cAAAA;WAAgB,CAAA;EACpD,OAAC,SAAS;EACR;EACA;EACA;EACA;EACA;EACA;EACA,QAAA,IAAIiG,QAAQ,IAAI,CAACnT,MAAM,CAACa,OAAO,EAAE;EAC/BwD,UAAAA,UAAU,GAAG,CAAC,GAAGA,UAAU,CAAC,CAAA;EAC9B,SAAA;EACF,OAAA;QAEA,IAAIrE,MAAM,CAACa,OAAO,EAAE;UAClB,OAAO;EAAE7B,UAAAA,IAAI,EAAE,SAAA;WAAW,CAAA;EAC5B,OAAA;QAEA,IAAIuU,UAAU,GAAGrd,WAAW,CAACwV,WAAW,EAAE1b,QAAQ,EAAEoG,QAAQ,CAAC,CAAA;EAC7D,MAAA,IAAImd,UAAU,EAAE;UACd,OAAO;EAAEvU,UAAAA,IAAI,EAAE,SAAS;EAAErI,UAAAA,OAAO,EAAE4c,UAAAA;WAAY,CAAA;EACjD,OAAA;QAEA,IAAIC,iBAAiB,GAAGnd,eAAe,CACrCqV,WAAW,EACX1b,QAAQ,EACRoG,QAAQ,EACR,IACF,CAAC,CAAA;;EAED;EACA,MAAA,IACE,CAACod,iBAAiB,IACjBtG,cAAc,CAAC/d,MAAM,KAAKqkB,iBAAiB,CAACrkB,MAAM,IACjD+d,cAAc,CAAC/S,KAAK,CAClB,CAAC8L,CAAC,EAAErP,CAAC,KAAKqP,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,KAAK2d,iBAAiB,CAAE5c,CAAC,CAAC,CAACvB,KAAK,CAACQ,EACvD,CAAE,EACJ;UACA,OAAO;EAAEmJ,UAAAA,IAAI,EAAE,SAAS;EAAErI,UAAAA,OAAO,EAAE,IAAA;WAAM,CAAA;EAC3C,OAAA;EAEAuW,MAAAA,cAAc,GAAGsG,iBAAiB,CAAA;EACpC,KAAA;EACF,GAAA;IAEA,SAASC,kBAAkBA,CAACC,SAAoC,EAAE;MAChEhe,QAAQ,GAAG,EAAE,CAAA;MACb4O,kBAAkB,GAAGhP,yBAAyB,CAC5Coe,SAAS,EACTle,kBAAkB,EAClBvG,SAAS,EACTyG,QACF,CAAC,CAAA;EACH,GAAA;EAEA,EAAA,SAASie,WAAWA,CAClBrH,OAAsB,EACtBvW,QAA+B,EACzB;EACN,IAAA,IAAIod,QAAQ,GAAG7O,kBAAkB,IAAI,IAAI,CAAA;EACzC,IAAA,IAAIoH,WAAW,GAAGpH,kBAAkB,IAAID,UAAU,CAAA;MAClDiP,eAAe,CACbhH,OAAO,EACPvW,QAAQ,EACR2V,WAAW,EACXhW,QAAQ,EACRF,kBACF,CAAC,CAAA;;EAED;EACA;EACA;EACA;EACA;EACA,IAAA,IAAI2d,QAAQ,EAAE;EACZ9O,MAAAA,UAAU,GAAG,CAAC,GAAGA,UAAU,CAAC,CAAA;QAC5BwE,WAAW,CAAC,EAAE,CAAC,CAAA;EACjB,KAAA;EACF,GAAA;EAEAtC,EAAAA,MAAM,GAAG;MACP,IAAInQ,QAAQA,GAAG;EACb,MAAA,OAAOA,QAAQ,CAAA;OAChB;MACD,IAAIwO,MAAMA,GAAG;EACX,MAAA,OAAOA,MAAM,CAAA;OACd;MACD,IAAI5V,KAAKA,GAAG;EACV,MAAA,OAAOA,KAAK,CAAA;OACb;MACD,IAAIuG,MAAMA,GAAG;EACX,MAAA,OAAO8O,UAAU,CAAA;OAClB;MACD,IAAIzS,MAAMA,GAAG;EACX,MAAA,OAAOoS,YAAY,CAAA;OACpB;MACDuE,UAAU;MACVpH,SAAS;MACTyR,uBAAuB;MACvBlI,QAAQ;MACR8E,KAAK;MACLnE,UAAU;EACV;EACA;MACAhb,UAAU,EAAGT,EAAM,IAAK0O,IAAI,CAAC/N,OAAO,CAACF,UAAU,CAACT,EAAE,CAAC;MACnDc,cAAc,EAAGd,EAAM,IAAK0O,IAAI,CAAC/N,OAAO,CAACG,cAAc,CAACd,EAAE,CAAC;MAC3DkiB,UAAU;EACVzI,IAAAA,aAAa,EAAE0I,2BAA2B;MAC1C5I,OAAO;MACPkJ,UAAU;MACV/I,aAAa;MACbqK,WAAW;EACXC,IAAAA,yBAAyB,EAAEhM,gBAAgB;EAC3CiM,IAAAA,wBAAwB,EAAEzL,eAAe;EACzC;EACA;EACAqL,IAAAA,kBAAAA;KACD,CAAA;EAED,EAAA,OAAOlN,MAAM,CAAA;EACf,CAAA;EACA;;EAEA;EACA;EACA;;QAEauN,sBAAsB,GAAGC,MAAM,CAAC,UAAU,EAAC;;EAExD;EACA;EACA;;EAgBO,SAASC,mBAAmBA,CACjCze,MAA6B,EAC7BiU,IAAiC,EAClB;IACfxW,SAAS,CACPuC,MAAM,CAACpG,MAAM,GAAG,CAAC,EACjB,kEACF,CAAC,CAAA;IAED,IAAIuG,QAAuB,GAAG,EAAE,CAAA;IAChC,IAAIU,QAAQ,GAAG,CAACoT,IAAI,GAAGA,IAAI,CAACpT,QAAQ,GAAG,IAAI,KAAK,GAAG,CAAA;EACnD,EAAA,IAAIZ,kBAA8C,CAAA;EAClD,EAAA,IAAIgU,IAAI,IAAA,IAAA,IAAJA,IAAI,CAAEhU,kBAAkB,EAAE;MAC5BA,kBAAkB,GAAGgU,IAAI,CAAChU,kBAAkB,CAAA;EAC9C,GAAC,MAAM,IAAIgU,IAAI,YAAJA,IAAI,CAAEpF,mBAAmB,EAAE;EACpC;EACA,IAAA,IAAIA,mBAAmB,GAAGoF,IAAI,CAACpF,mBAAmB,CAAA;MAClD5O,kBAAkB,GAAIH,KAAK,KAAM;QAC/BuO,gBAAgB,EAAEQ,mBAAmB,CAAC/O,KAAK,CAAA;EAC7C,KAAC,CAAC,CAAA;EACJ,GAAC,MAAM;EACLG,IAAAA,kBAAkB,GAAGmO,yBAAyB,CAAA;EAChD,GAAA;EACA;IACA,IAAIiB,MAAiC,GAAA9Q,QAAA,CAAA;EACnCuJ,IAAAA,oBAAoB,EAAE,KAAK;EAC3B4W,IAAAA,mBAAmB,EAAE,KAAA;EAAK,GAAA,EACtBzK,IAAI,GAAGA,IAAI,CAAC5E,MAAM,GAAG,IAAI,CAC9B,CAAA;IAED,IAAIP,UAAU,GAAG/O,yBAAyB,CACxCC,MAAM,EACNC,kBAAkB,EAClBvG,SAAS,EACTyG,QACF,CAAC,CAAA;;EAED;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACE,EAAA,eAAewe,KAAKA,CAClBnI,OAAgB,EAAAoI,MAAA,EAU0B;MAAA,IAT1C;QACEC,cAAc;QACdC,uBAAuB;EACvB7P,MAAAA,YAAAA;EAKF,KAAC,GAAA2P,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;MAEN,IAAIxhB,GAAG,GAAG,IAAIlC,GAAG,CAACsb,OAAO,CAACpZ,GAAG,CAAC,CAAA;EAC9B,IAAA,IAAI0a,MAAM,GAAGtB,OAAO,CAACsB,MAAM,CAAA;EAC3B,IAAA,IAAIvd,QAAQ,GAAGC,cAAc,CAAC,EAAE,EAAEO,UAAU,CAACqC,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;MACnE,IAAIgE,OAAO,GAAGT,WAAW,CAACmO,UAAU,EAAEvU,QAAQ,EAAEsG,QAAQ,CAAC,CAAA;;EAEzD;MACA,IAAI,CAACke,aAAa,CAACjH,MAAM,CAAC,IAAIA,MAAM,KAAK,MAAM,EAAE;EAC/C,MAAA,IAAI3Y,KAAK,GAAGiR,sBAAsB,CAAC,GAAG,EAAE;EAAE0H,QAAAA,MAAAA;EAAO,OAAC,CAAC,CAAA;QACnD,IAAI;EAAE1W,QAAAA,OAAO,EAAE4d,uBAAuB;EAAElf,QAAAA,KAAAA;EAAM,OAAC,GAC7CuQ,sBAAsB,CAACvB,UAAU,CAAC,CAAA;QACpC,OAAO;UACLjO,QAAQ;UACRtG,QAAQ;EACR6G,QAAAA,OAAO,EAAE4d,uBAAuB;UAChCrd,UAAU,EAAE,EAAE;EACd2P,QAAAA,UAAU,EAAE,IAAI;EAChBT,QAAAA,MAAM,EAAE;YACN,CAAC/Q,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;WACb;UACD8f,UAAU,EAAE9f,KAAK,CAAC8J,MAAM;UACxBiW,aAAa,EAAE,EAAE;UACjBC,aAAa,EAAE,EAAE;EACjBtM,QAAAA,eAAe,EAAE,IAAA;SAClB,CAAA;EACH,KAAC,MAAM,IAAI,CAACzR,OAAO,EAAE;EACnB,MAAA,IAAIjC,KAAK,GAAGiR,sBAAsB,CAAC,GAAG,EAAE;UAAE3V,QAAQ,EAAEF,QAAQ,CAACE,QAAAA;EAAS,OAAC,CAAC,CAAA;QACxE,IAAI;EAAE2G,QAAAA,OAAO,EAAEkV,eAAe;EAAExW,QAAAA,KAAAA;EAAM,OAAC,GACrCuQ,sBAAsB,CAACvB,UAAU,CAAC,CAAA;QACpC,OAAO;UACLjO,QAAQ;UACRtG,QAAQ;EACR6G,QAAAA,OAAO,EAAEkV,eAAe;UACxB3U,UAAU,EAAE,EAAE;EACd2P,QAAAA,UAAU,EAAE,IAAI;EAChBT,QAAAA,MAAM,EAAE;YACN,CAAC/Q,KAAK,CAACQ,EAAE,GAAGnB,KAAAA;WACb;UACD8f,UAAU,EAAE9f,KAAK,CAAC8J,MAAM;UACxBiW,aAAa,EAAE,EAAE;UACjBC,aAAa,EAAE,EAAE;EACjBtM,QAAAA,eAAe,EAAE,IAAA;SAClB,CAAA;EACH,KAAA;MAEA,IAAItP,MAAM,GAAG,MAAM6b,SAAS,CAC1B5I,OAAO,EACPjc,QAAQ,EACR6G,OAAO,EACPyd,cAAc,EACd5P,YAAY,IAAI,IAAI,EACpB6P,uBAAuB,KAAK,IAAI,EAChC,IACF,CAAC,CAAA;EACD,IAAA,IAAIO,UAAU,CAAC9b,MAAM,CAAC,EAAE;EACtB,MAAA,OAAOA,MAAM,CAAA;EACf,KAAA;;EAEA;EACA;EACA;EACA,IAAA,OAAAhF,QAAA,CAAA;QAAShE,QAAQ;EAAEsG,MAAAA,QAAAA;EAAQ,KAAA,EAAK0C,MAAM,CAAA,CAAA;EACxC,GAAA;;EAEA;EACF;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACE,EAAA,eAAe+b,UAAUA,CACvB9I,OAAgB,EAAA+I,MAAA,EAUF;MAAA,IATd;QACExI,OAAO;QACP8H,cAAc;EACd5P,MAAAA,YAAAA;EAKF,KAAC,GAAAsQ,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;MAEN,IAAIniB,GAAG,GAAG,IAAIlC,GAAG,CAACsb,OAAO,CAACpZ,GAAG,CAAC,CAAA;EAC9B,IAAA,IAAI0a,MAAM,GAAGtB,OAAO,CAACsB,MAAM,CAAA;EAC3B,IAAA,IAAIvd,QAAQ,GAAGC,cAAc,CAAC,EAAE,EAAEO,UAAU,CAACqC,GAAG,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;MACnE,IAAIgE,OAAO,GAAGT,WAAW,CAACmO,UAAU,EAAEvU,QAAQ,EAAEsG,QAAQ,CAAC,CAAA;;EAEzD;EACA,IAAA,IAAI,CAACke,aAAa,CAACjH,MAAM,CAAC,IAAIA,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,SAAS,EAAE;QACvE,MAAM1H,sBAAsB,CAAC,GAAG,EAAE;EAAE0H,QAAAA,MAAAA;EAAO,OAAC,CAAC,CAAA;EAC/C,KAAC,MAAM,IAAI,CAAC1W,OAAO,EAAE;QACnB,MAAMgP,sBAAsB,CAAC,GAAG,EAAE;UAAE3V,QAAQ,EAAEF,QAAQ,CAACE,QAAAA;EAAS,OAAC,CAAC,CAAA;EACpE,KAAA;MAEA,IAAIiH,KAAK,GAAGqV,OAAO,GACf3V,OAAO,CAACoe,IAAI,CAAE9O,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,KAAKyW,OAAO,CAAC,GAC3Cc,cAAc,CAACzW,OAAO,EAAE7G,QAAQ,CAAC,CAAA;EAErC,IAAA,IAAIwc,OAAO,IAAI,CAACrV,KAAK,EAAE;QACrB,MAAM0O,sBAAsB,CAAC,GAAG,EAAE;UAChC3V,QAAQ,EAAEF,QAAQ,CAACE,QAAQ;EAC3Bsc,QAAAA,OAAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAC,MAAM,IAAI,CAACrV,KAAK,EAAE;EACjB;QACA,MAAM0O,sBAAsB,CAAC,GAAG,EAAE;UAAE3V,QAAQ,EAAEF,QAAQ,CAACE,QAAAA;EAAS,OAAC,CAAC,CAAA;EACpE,KAAA;MAEA,IAAI8I,MAAM,GAAG,MAAM6b,SAAS,CAC1B5I,OAAO,EACPjc,QAAQ,EACR6G,OAAO,EACPyd,cAAc,EACd5P,YAAY,IAAI,IAAI,EACpB,KAAK,EACLvN,KACF,CAAC,CAAA;EAED,IAAA,IAAI2d,UAAU,CAAC9b,MAAM,CAAC,EAAE;EACtB,MAAA,OAAOA,MAAM,CAAA;EACf,KAAA;EAEA,IAAA,IAAIpE,KAAK,GAAGoE,MAAM,CAACsN,MAAM,GAAG1L,MAAM,CAACsa,MAAM,CAAClc,MAAM,CAACsN,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGnX,SAAS,CAAA;MACvE,IAAIyF,KAAK,KAAKzF,SAAS,EAAE;EACvB;EACA;EACA;EACA;EACA,MAAA,MAAMyF,KAAK,CAAA;EACb,KAAA;;EAEA;MACA,IAAIoE,MAAM,CAAC+N,UAAU,EAAE;QACrB,OAAOnM,MAAM,CAACsa,MAAM,CAAClc,MAAM,CAAC+N,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;EAC5C,KAAA;MAEA,IAAI/N,MAAM,CAAC5B,UAAU,EAAE;EAAA,MAAA,IAAA+d,qBAAA,CAAA;EACrB,MAAA,IAAI7d,IAAI,GAAGsD,MAAM,CAACsa,MAAM,CAAClc,MAAM,CAAC5B,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;EAC9C,MAAA,IAAA,CAAA+d,qBAAA,GAAInc,MAAM,CAACsP,eAAe,KAAtB6M,IAAAA,IAAAA,qBAAA,CAAyBhe,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,EAAE;EAC5CuB,QAAAA,IAAI,CAAC0c,sBAAsB,CAAC,GAAGhb,MAAM,CAACsP,eAAe,CAACnR,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;EACvE,OAAA;EACA,MAAA,OAAOuB,IAAI,CAAA;EACb,KAAA;EAEA,IAAA,OAAOnI,SAAS,CAAA;EAClB,GAAA;EAEA,EAAA,eAAe0lB,SAASA,CACtB5I,OAAgB,EAChBjc,QAAkB,EAClB6G,OAAiC,EACjCyd,cAAuB,EACvB5P,YAAyC,EACzC6P,uBAAgC,EAChCa,UAAyC,EACgC;EACzEliB,IAAAA,SAAS,CACP+Y,OAAO,CAAC/L,MAAM,EACd,sEACF,CAAC,CAAA;MAED,IAAI;QACF,IAAImK,gBAAgB,CAAC4B,OAAO,CAACsB,MAAM,CAACjR,WAAW,EAAE,CAAC,EAAE;UAClD,IAAItD,MAAM,GAAG,MAAMqc,MAAM,CACvBpJ,OAAO,EACPpV,OAAO,EACPue,UAAU,IAAI9H,cAAc,CAACzW,OAAO,EAAE7G,QAAQ,CAAC,EAC/CskB,cAAc,EACd5P,YAAY,EACZ6P,uBAAuB,EACvBa,UAAU,IAAI,IAChB,CAAC,CAAA;EACD,QAAA,OAAOpc,MAAM,CAAA;EACf,OAAA;EAEA,MAAA,IAAIA,MAAM,GAAG,MAAMsc,aAAa,CAC9BrJ,OAAO,EACPpV,OAAO,EACPyd,cAAc,EACd5P,YAAY,EACZ6P,uBAAuB,EACvBa,UACF,CAAC,CAAA;QACD,OAAON,UAAU,CAAC9b,MAAM,CAAC,GACrBA,MAAM,GAAAhF,QAAA,CAAA,EAAA,EAEDgF,MAAM,EAAA;EACT+N,QAAAA,UAAU,EAAE,IAAI;EAChB6N,QAAAA,aAAa,EAAE,EAAC;SACjB,CAAA,CAAA;OACN,CAAC,OAAOnhB,CAAC,EAAE;EACV;EACA;EACA;QACA,IAAI8hB,oBAAoB,CAAC9hB,CAAC,CAAC,IAAIqhB,UAAU,CAACrhB,CAAC,CAACuF,MAAM,CAAC,EAAE;EACnD,QAAA,IAAIvF,CAAC,CAACyL,IAAI,KAAK/J,UAAU,CAACP,KAAK,EAAE;YAC/B,MAAMnB,CAAC,CAACuF,MAAM,CAAA;EAChB,SAAA;UACA,OAAOvF,CAAC,CAACuF,MAAM,CAAA;EACjB,OAAA;EACA;EACA;EACA,MAAA,IAAIwc,kBAAkB,CAAC/hB,CAAC,CAAC,EAAE;EACzB,QAAA,OAAOA,CAAC,CAAA;EACV,OAAA;EACA,MAAA,MAAMA,CAAC,CAAA;EACT,KAAA;EACF,GAAA;EAEA,EAAA,eAAe4hB,MAAMA,CACnBpJ,OAAgB,EAChBpV,OAAiC,EACjCwW,WAAmC,EACnCiH,cAAuB,EACvB5P,YAAyC,EACzC6P,uBAAgC,EAChCkB,cAAuB,EACkD;EACzE,IAAA,IAAIzc,MAAkB,CAAA;EAEtB,IAAA,IAAI,CAACqU,WAAW,CAAC9X,KAAK,CAACjG,MAAM,IAAI,CAAC+d,WAAW,CAAC9X,KAAK,CAAC6Q,IAAI,EAAE;EACxD,MAAA,IAAIxR,KAAK,GAAGiR,sBAAsB,CAAC,GAAG,EAAE;UACtC0H,MAAM,EAAEtB,OAAO,CAACsB,MAAM;UACtBrd,QAAQ,EAAE,IAAIS,GAAG,CAACsb,OAAO,CAACpZ,GAAG,CAAC,CAAC3C,QAAQ;EACvCsc,QAAAA,OAAO,EAAEa,WAAW,CAAC9X,KAAK,CAACQ,EAAAA;EAC7B,OAAC,CAAC,CAAA;EACF,MAAA,IAAI0f,cAAc,EAAE;EAClB,QAAA,MAAM7gB,KAAK,CAAA;EACb,OAAA;EACAoE,MAAAA,MAAM,GAAG;UACPkG,IAAI,EAAE/J,UAAU,CAACP,KAAK;EACtBA,QAAAA,KAAAA;SACD,CAAA;EACH,KAAC,MAAM;QACL,IAAI4Y,OAAO,GAAG,MAAMC,gBAAgB,CAClC,QAAQ,EACRxB,OAAO,EACP,CAACoB,WAAW,CAAC,EACbxW,OAAO,EACP4e,cAAc,EACdnB,cAAc,EACd5P,YACF,CAAC,CAAA;QACD1L,MAAM,GAAGwU,OAAO,CAACH,WAAW,CAAC9X,KAAK,CAACQ,EAAE,CAAC,CAAA;EAEtC,MAAA,IAAIkW,OAAO,CAAC/L,MAAM,CAACa,OAAO,EAAE;EAC1B2U,QAAAA,8BAA8B,CAACzJ,OAAO,EAAEwJ,cAAc,EAAE3Q,MAAM,CAAC,CAAA;EACjE,OAAA;EACF,KAAA;EAEA,IAAA,IAAI4I,gBAAgB,CAAC1U,MAAM,CAAC,EAAE;EAC5B;EACA;EACA;EACA;EACA,MAAA,MAAM,IAAI+F,QAAQ,CAAC,IAAI,EAAE;EACvBL,QAAAA,MAAM,EAAE1F,MAAM,CAACuJ,QAAQ,CAAC7D,MAAM;EAC9BC,QAAAA,OAAO,EAAE;YACPgX,QAAQ,EAAE3c,MAAM,CAACuJ,QAAQ,CAAC5D,OAAO,CAACmC,GAAG,CAAC,UAAU,CAAA;EAClD,SAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAA;EAEA,IAAA,IAAI+M,gBAAgB,CAAC7U,MAAM,CAAC,EAAE;EAC5B,MAAA,IAAIpE,KAAK,GAAGiR,sBAAsB,CAAC,GAAG,EAAE;EAAE3G,QAAAA,IAAI,EAAE,cAAA;EAAe,OAAC,CAAC,CAAA;EACjE,MAAA,IAAIuW,cAAc,EAAE;EAClB,QAAA,MAAM7gB,KAAK,CAAA;EACb,OAAA;EACAoE,MAAAA,MAAM,GAAG;UACPkG,IAAI,EAAE/J,UAAU,CAACP,KAAK;EACtBA,QAAAA,KAAAA;SACD,CAAA;EACH,KAAA;EAEA,IAAA,IAAI6gB,cAAc,EAAE;EAClB;EACA;EACA,MAAA,IAAIhJ,aAAa,CAACzT,MAAM,CAAC,EAAE;UACzB,MAAMA,MAAM,CAACpE,KAAK,CAAA;EACpB,OAAA;QAEA,OAAO;UACLiC,OAAO,EAAE,CAACwW,WAAW,CAAC;UACtBjW,UAAU,EAAE,EAAE;EACd2P,QAAAA,UAAU,EAAE;EAAE,UAAA,CAACsG,WAAW,CAAC9X,KAAK,CAACQ,EAAE,GAAGiD,MAAM,CAAC1B,IAAAA;WAAM;EACnDgP,QAAAA,MAAM,EAAE,IAAI;EACZ;EACA;EACAoO,QAAAA,UAAU,EAAE,GAAG;UACfC,aAAa,EAAE,EAAE;UACjBC,aAAa,EAAE,EAAE;EACjBtM,QAAAA,eAAe,EAAE,IAAA;SAClB,CAAA;EACH,KAAA;;EAEA;MACA,IAAIsN,aAAa,GAAG,IAAIC,OAAO,CAAC5J,OAAO,CAACpZ,GAAG,EAAE;QAC3C8L,OAAO,EAAEsN,OAAO,CAACtN,OAAO;QACxB0D,QAAQ,EAAE4J,OAAO,CAAC5J,QAAQ;QAC1BnC,MAAM,EAAE+L,OAAO,CAAC/L,MAAAA;EAClB,KAAC,CAAC,CAAA;EAEF,IAAA,IAAIuM,aAAa,CAACzT,MAAM,CAAC,EAAE;EACzB;EACA;EACA,MAAA,IAAI8U,aAAa,GAAGyG,uBAAuB,GACvClH,WAAW,GACXjB,mBAAmB,CAACvV,OAAO,EAAEwW,WAAW,CAAC9X,KAAK,CAACQ,EAAE,CAAC,CAAA;QAEtD,IAAI+f,OAAO,GAAG,MAAMR,aAAa,CAC/BM,aAAa,EACb/e,OAAO,EACPyd,cAAc,EACd5P,YAAY,EACZ6P,uBAAuB,EACvB,IAAI,EACJ,CAACzG,aAAa,CAACvY,KAAK,CAACQ,EAAE,EAAEiD,MAAM,CACjC,CAAC,CAAA;;EAED;QACA,OAAAhF,QAAA,KACK8hB,OAAO,EAAA;UACVpB,UAAU,EAAE/R,oBAAoB,CAAC3J,MAAM,CAACpE,KAAK,CAAC,GAC1CoE,MAAM,CAACpE,KAAK,CAAC8J,MAAM,GACnB1F,MAAM,CAAC0b,UAAU,IAAI,IAAI,GACzB1b,MAAM,CAAC0b,UAAU,GACjB,GAAG;EACP3N,QAAAA,UAAU,EAAE,IAAI;EAChB6N,QAAAA,aAAa,EAAA5gB,QAAA,CAAA,EAAA,EACPgF,MAAM,CAAC2F,OAAO,GAAG;EAAE,UAAA,CAAC0O,WAAW,CAAC9X,KAAK,CAACQ,EAAE,GAAGiD,MAAM,CAAC2F,OAAAA;WAAS,GAAG,EAAE,CAAA;EACrE,OAAA,CAAA,CAAA;EAEL,KAAA;EAEA,IAAA,IAAImX,OAAO,GAAG,MAAMR,aAAa,CAC/BM,aAAa,EACb/e,OAAO,EACPyd,cAAc,EACd5P,YAAY,EACZ6P,uBAAuB,EACvB,IACF,CAAC,CAAA;MAED,OAAAvgB,QAAA,KACK8hB,OAAO,EAAA;EACV/O,MAAAA,UAAU,EAAE;EACV,QAAA,CAACsG,WAAW,CAAC9X,KAAK,CAACQ,EAAE,GAAGiD,MAAM,CAAC1B,IAAAA;EACjC,OAAA;OAEI0B,EAAAA,MAAM,CAAC0b,UAAU,GAAG;QAAEA,UAAU,EAAE1b,MAAM,CAAC0b,UAAAA;OAAY,GAAG,EAAE,EAAA;EAC9DE,MAAAA,aAAa,EAAE5b,MAAM,CAAC2F,OAAO,GACzB;EAAE,QAAA,CAAC0O,WAAW,CAAC9X,KAAK,CAACQ,EAAE,GAAGiD,MAAM,CAAC2F,OAAAA;EAAQ,OAAC,GAC1C,EAAC;EAAC,KAAA,CAAA,CAAA;EAEV,GAAA;EAEA,EAAA,eAAe2W,aAAaA,CAC1BrJ,OAAgB,EAChBpV,OAAiC,EACjCyd,cAAuB,EACvB5P,YAAyC,EACzC6P,uBAAgC,EAChCa,UAAyC,EACzCjJ,mBAAyC,EAOzC;EACA,IAAA,IAAIsJ,cAAc,GAAGL,UAAU,IAAI,IAAI,CAAA;;EAEvC;EACA,IAAA,IACEK,cAAc,IACd,EAACL,UAAU,IAAVA,IAAAA,IAAAA,UAAU,CAAE7f,KAAK,CAAC8Q,MAAM,CACzB,IAAA,EAAC+O,UAAU,IAAVA,IAAAA,IAAAA,UAAU,CAAE7f,KAAK,CAAC6Q,IAAI,CACvB,EAAA;QACA,MAAMP,sBAAsB,CAAC,GAAG,EAAE;UAChC0H,MAAM,EAAEtB,OAAO,CAACsB,MAAM;UACtBrd,QAAQ,EAAE,IAAIS,GAAG,CAACsb,OAAO,CAACpZ,GAAG,CAAC,CAAC3C,QAAQ;EACvCsc,QAAAA,OAAO,EAAE4I,UAAU,IAAA,IAAA,GAAA,KAAA,CAAA,GAAVA,UAAU,CAAE7f,KAAK,CAACQ,EAAAA;EAC7B,OAAC,CAAC,CAAA;EACJ,KAAA;EAEA,IAAA,IAAI+Z,cAAc,GAAGsF,UAAU,GAC3B,CAACA,UAAU,CAAC,GACZjJ,mBAAmB,IAAIM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GAC5D4J,6BAA6B,CAAClf,OAAO,EAAEsV,mBAAmB,CAAC,CAAC,CAAC,CAAC,GAC9DtV,OAAO,CAAA;EACX,IAAA,IAAIsX,aAAa,GAAG2B,cAAc,CAAC9V,MAAM,CACtCmM,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAAC8Q,MAAM,IAAIF,CAAC,CAAC5Q,KAAK,CAAC6Q,IACnC,CAAC,CAAA;;EAED;EACA,IAAA,IAAI+H,aAAa,CAAC9e,MAAM,KAAK,CAAC,EAAE;QAC9B,OAAO;UACLwH,OAAO;EACP;EACAO,QAAAA,UAAU,EAAEP,OAAO,CAACoD,MAAM,CACxB,CAACkG,GAAG,EAAEgG,CAAC,KAAKvL,MAAM,CAAC7F,MAAM,CAACoL,GAAG,EAAE;EAAE,UAAA,CAACgG,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,GAAG,IAAA;EAAK,SAAC,CAAC,EACtD,EACF,CAAC;UACDuQ,MAAM,EACJ6F,mBAAmB,IAAIM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GACxD;YACE,CAACA,mBAAmB,CAAC,CAAC,CAAC,GAAGA,mBAAmB,CAAC,CAAC,CAAC,CAACvX,KAAAA;EACnD,SAAC,GACD,IAAI;EACV8f,QAAAA,UAAU,EAAE,GAAG;UACfC,aAAa,EAAE,EAAE;EACjBrM,QAAAA,eAAe,EAAE,IAAA;SAClB,CAAA;EACH,KAAA;EAEA,IAAA,IAAIkF,OAAO,GAAG,MAAMC,gBAAgB,CAClC,QAAQ,EACRxB,OAAO,EACPkC,aAAa,EACbtX,OAAO,EACP4e,cAAc,EACdnB,cAAc,EACd5P,YACF,CAAC,CAAA;EAED,IAAA,IAAIuH,OAAO,CAAC/L,MAAM,CAACa,OAAO,EAAE;EAC1B2U,MAAAA,8BAA8B,CAACzJ,OAAO,EAAEwJ,cAAc,EAAE3Q,MAAM,CAAC,CAAA;EACjE,KAAA;;EAEA;EACA,IAAA,IAAIwD,eAAe,GAAG,IAAIrB,GAAG,EAAwB,CAAA;EACrD,IAAA,IAAI6O,OAAO,GAAGE,sBAAsB,CAClCnf,OAAO,EACP2W,OAAO,EACPrB,mBAAmB,EACnB7D,eAAe,EACfiM,uBACF,CAAC,CAAA;;EAED;EACA,IAAA,IAAI0B,eAAe,GAAG,IAAI5gB,GAAG,CAC3B8Y,aAAa,CAACrf,GAAG,CAAEqI,KAAK,IAAKA,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAC7C,CAAC,CAAA;EACDc,IAAAA,OAAO,CAACsB,OAAO,CAAEhB,KAAK,IAAK;QACzB,IAAI,CAAC8e,eAAe,CAACpX,GAAG,CAAC1H,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,EAAE;UACxC+f,OAAO,CAAC1e,UAAU,CAACD,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,GAAG,IAAI,CAAA;EAC3C,OAAA;EACF,KAAC,CAAC,CAAA;MAEF,OAAA/B,QAAA,KACK8hB,OAAO,EAAA;QACVjf,OAAO;EACPyR,MAAAA,eAAe,EACbA,eAAe,CAAC3G,IAAI,GAAG,CAAC,GACpB/G,MAAM,CAACsb,WAAW,CAAC5N,eAAe,CAACzZ,OAAO,EAAE,CAAC,GAC7C,IAAA;EAAI,KAAA,CAAA,CAAA;EAEd,GAAA;;EAEA;EACA;EACA,EAAA,eAAe4e,gBAAgBA,CAC7BvO,IAAyB,EACzB+M,OAAgB,EAChBkC,aAAuC,EACvCtX,OAAiC,EACjC4e,cAAuB,EACvBnB,cAAuB,EACvB5P,YAAyC,EACJ;MACrC,IAAI8I,OAAO,GAAG,MAAM6D,oBAAoB,CACtC3M,YAAY,IAAIC,mBAAmB,EACnCzF,IAAI,EACJ,IAAI,EACJ+M,OAAO,EACPkC,aAAa,EACbtX,OAAO,EACP,IAAI,EACJjB,QAAQ,EACRF,kBAAkB,EAClB4e,cACF,CAAC,CAAA;MAED,IAAIlD,WAAuC,GAAG,EAAE,CAAA;MAChD,MAAMxR,OAAO,CAACiS,GAAG,CACfhb,OAAO,CAAC/H,GAAG,CAAC,MAAOqI,KAAK,IAAK;QAC3B,IAAI,EAAEA,KAAK,CAAC5B,KAAK,CAACQ,EAAE,IAAIyX,OAAO,CAAC,EAAE;EAChC,QAAA,OAAA;EACF,OAAA;QACA,IAAIxU,MAAM,GAAGwU,OAAO,CAACrW,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;EACpC,MAAA,IAAIub,kCAAkC,CAACtY,MAAM,CAAC,EAAE;EAC9C,QAAA,IAAIuJ,QAAQ,GAAGvJ,MAAM,CAACA,MAAkB,CAAA;EACxC;EACA,QAAA,MAAMuY,wCAAwC,CAC5ChP,QAAQ,EACR0J,OAAO,EACP9U,KAAK,CAAC5B,KAAK,CAACQ,EAAE,EACdc,OAAO,EACPP,QAAQ,EACRwO,MAAM,CAACvH,oBACT,CAAC,CAAA;EACH,OAAA;QACA,IAAIuX,UAAU,CAAC9b,MAAM,CAACA,MAAM,CAAC,IAAIyc,cAAc,EAAE;EAC/C;EACA;EACA,QAAA,MAAMzc,MAAM,CAAA;EACd,OAAA;EAEAoY,MAAAA,WAAW,CAACja,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,GACzB,MAAMyb,qCAAqC,CAACxY,MAAM,CAAC,CAAA;EACvD,KAAC,CACH,CAAC,CAAA;EACD,IAAA,OAAOoY,WAAW,CAAA;EACpB,GAAA;IAEA,OAAO;MACL7M,UAAU;MACV6P,KAAK;EACLW,IAAAA,UAAAA;KACD,CAAA;EACH,CAAA;;EAEA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACO,SAASoB,yBAAyBA,CACvC1gB,MAAiC,EACjCqgB,OAA6B,EAC7BlhB,KAAU,EACV;EACA,EAAA,IAAIwhB,UAAgC,GAAApiB,QAAA,CAAA,EAAA,EAC/B8hB,OAAO,EAAA;MACVpB,UAAU,EAAE/R,oBAAoB,CAAC/N,KAAK,CAAC,GAAGA,KAAK,CAAC8J,MAAM,GAAG,GAAG;EAC5D4H,IAAAA,MAAM,EAAE;QACN,CAACwP,OAAO,CAACO,0BAA0B,IAAI5gB,MAAM,CAAC,CAAC,CAAC,CAACM,EAAE,GAAGnB,KAAAA;EACxD,KAAA;KACD,CAAA,CAAA;EACD,EAAA,OAAOwhB,UAAU,CAAA;EACnB,CAAA;EAEA,SAASV,8BAA8BA,CACrCzJ,OAAgB,EAChBwJ,cAAuB,EACvB3Q,MAAiC,EACjC;IACA,IAAIA,MAAM,CAACqP,mBAAmB,IAAIlI,OAAO,CAAC/L,MAAM,CAACoW,MAAM,KAAKnnB,SAAS,EAAE;EACrE,IAAA,MAAM8c,OAAO,CAAC/L,MAAM,CAACoW,MAAM,CAAA;EAC7B,GAAA;EAEA,EAAA,IAAI/I,MAAM,GAAGkI,cAAc,GAAG,YAAY,GAAG,OAAO,CAAA;EACpD,EAAA,MAAM,IAAIpiB,KAAK,CAAIka,MAAM,GAAoBtB,mBAAAA,GAAAA,OAAO,CAACsB,MAAM,GAAItB,GAAAA,GAAAA,OAAO,CAACpZ,GAAK,CAAC,CAAA;EAC/E,CAAA;EAEA,SAAS0jB,sBAAsBA,CAC7B7M,IAAgC,EACG;IACnC,OACEA,IAAI,IAAI,IAAI,KACV,UAAU,IAAIA,IAAI,IAAIA,IAAI,CAACpG,QAAQ,IAAI,IAAI,IAC1C,MAAM,IAAIoG,IAAI,IAAIA,IAAI,CAAC8M,IAAI,KAAKrnB,SAAU,CAAC,CAAA;EAElD,CAAA;EAEA,SAAS2b,WAAWA,CAClB9a,QAAc,EACd6G,OAAiC,EACjCP,QAAgB,EAChBmgB,eAAwB,EACxB3mB,EAAa,EACbyN,oBAA6B,EAC7BwN,WAAoB,EACpBC,QAA8B,EAC9B;EACA,EAAA,IAAI0L,iBAA2C,CAAA;EAC/C,EAAA,IAAIC,gBAAoD,CAAA;EACxD,EAAA,IAAI5L,WAAW,EAAE;EACf;EACA;EACA2L,IAAAA,iBAAiB,GAAG,EAAE,CAAA;EACtB,IAAA,KAAK,IAAIvf,KAAK,IAAIN,OAAO,EAAE;EACzB6f,MAAAA,iBAAiB,CAACzlB,IAAI,CAACkG,KAAK,CAAC,CAAA;EAC7B,MAAA,IAAIA,KAAK,CAAC5B,KAAK,CAACQ,EAAE,KAAKgV,WAAW,EAAE;EAClC4L,QAAAA,gBAAgB,GAAGxf,KAAK,CAAA;EACxB,QAAA,MAAA;EACF,OAAA;EACF,KAAA;EACF,GAAC,MAAM;EACLuf,IAAAA,iBAAiB,GAAG7f,OAAO,CAAA;MAC3B8f,gBAAgB,GAAG9f,OAAO,CAACA,OAAO,CAACxH,MAAM,GAAG,CAAC,CAAC,CAAA;EAChD,GAAA;;EAEA;EACA,EAAA,IAAIwB,IAAI,GAAG4M,SAAS,CAClB3N,EAAE,GAAGA,EAAE,GAAG,GAAG,EACbwN,mBAAmB,CAACoZ,iBAAiB,EAAEnZ,oBAAoB,CAAC,EAC5D9G,aAAa,CAACzG,QAAQ,CAACE,QAAQ,EAAEoG,QAAQ,CAAC,IAAItG,QAAQ,CAACE,QAAQ,EAC/D8a,QAAQ,KAAK,MACf,CAAC,CAAA;;EAED;EACA;EACA;IACA,IAAIlb,EAAE,IAAI,IAAI,EAAE;EACde,IAAAA,IAAI,CAACE,MAAM,GAAGf,QAAQ,CAACe,MAAM,CAAA;EAC7BF,IAAAA,IAAI,CAACG,IAAI,GAAGhB,QAAQ,CAACgB,IAAI,CAAA;EAC3B,GAAA;;EAEA;EACA,EAAA,IAAI,CAAClB,EAAE,IAAI,IAAI,IAAIA,EAAE,KAAK,EAAE,IAAIA,EAAE,KAAK,GAAG,KAAK6mB,gBAAgB,EAAE;EAC/D,IAAA,IAAIC,UAAU,GAAGC,kBAAkB,CAAChmB,IAAI,CAACE,MAAM,CAAC,CAAA;MAChD,IAAI4lB,gBAAgB,CAACphB,KAAK,CAACvG,KAAK,IAAI,CAAC4nB,UAAU,EAAE;EAC/C;EACA/lB,MAAAA,IAAI,CAACE,MAAM,GAAGF,IAAI,CAACE,MAAM,GACrBF,IAAI,CAACE,MAAM,CAACO,OAAO,CAAC,KAAK,EAAE,SAAS,CAAC,GACrC,QAAQ,CAAA;OACb,MAAM,IAAI,CAACqlB,gBAAgB,CAACphB,KAAK,CAACvG,KAAK,IAAI4nB,UAAU,EAAE;EACtD;QACA,IAAIvf,MAAM,GAAG,IAAIyf,eAAe,CAACjmB,IAAI,CAACE,MAAM,CAAC,CAAA;EAC7C,MAAA,IAAIgmB,WAAW,GAAG1f,MAAM,CAAC2f,MAAM,CAAC,OAAO,CAAC,CAAA;EACxC3f,MAAAA,MAAM,CAAC2J,MAAM,CAAC,OAAO,CAAC,CAAA;QACtB+V,WAAW,CAAC/c,MAAM,CAAEoC,CAAC,IAAKA,CAAC,CAAC,CAACjE,OAAO,CAAEiE,CAAC,IAAK/E,MAAM,CAAC4f,MAAM,CAAC,OAAO,EAAE7a,CAAC,CAAC,CAAC,CAAA;EACtE,MAAA,IAAI8a,EAAE,GAAG7f,MAAM,CAACzD,QAAQ,EAAE,CAAA;EAC1B/C,MAAAA,IAAI,CAACE,MAAM,GAAGmmB,EAAE,GAAOA,GAAAA,GAAAA,EAAE,GAAK,EAAE,CAAA;EAClC,KAAA;EACF,GAAA;;EAEA;EACA;EACA;EACA;EACA,EAAA,IAAIT,eAAe,IAAIngB,QAAQ,KAAK,GAAG,EAAE;MACvCzF,IAAI,CAACX,QAAQ,GACXW,IAAI,CAACX,QAAQ,KAAK,GAAG,GAAGoG,QAAQ,GAAGwB,SAAS,CAAC,CAACxB,QAAQ,EAAEzF,IAAI,CAACX,QAAQ,CAAC,CAAC,CAAA;EAC3E,GAAA;IAEA,OAAOM,UAAU,CAACK,IAAI,CAAC,CAAA;EACzB,CAAA;;EAEA;EACA;EACA,SAASqa,wBAAwBA,CAC/BiM,mBAA4B,EAC5BC,SAAkB,EAClBvmB,IAAY,EACZ6Y,IAAiC,EAKjC;EACA;IACA,IAAI,CAACA,IAAI,IAAI,CAAC6M,sBAAsB,CAAC7M,IAAI,CAAC,EAAE;MAC1C,OAAO;EAAE7Y,MAAAA,IAAAA;OAAM,CAAA;EACjB,GAAA;IAEA,IAAI6Y,IAAI,CAACvG,UAAU,IAAI,CAACqR,aAAa,CAAC9K,IAAI,CAACvG,UAAU,CAAC,EAAE;MACtD,OAAO;QACLtS,IAAI;EACJ+D,MAAAA,KAAK,EAAEiR,sBAAsB,CAAC,GAAG,EAAE;UAAE0H,MAAM,EAAE7D,IAAI,CAACvG,UAAAA;SAAY,CAAA;OAC/D,CAAA;EACH,GAAA;IAEA,IAAIkU,mBAAmB,GAAGA,OAAO;MAC/BxmB,IAAI;EACJ+D,IAAAA,KAAK,EAAEiR,sBAAsB,CAAC,GAAG,EAAE;EAAE3G,MAAAA,IAAI,EAAE,cAAA;OAAgB,CAAA;EAC7D,GAAC,CAAC,CAAA;;EAEF;EACA,EAAA,IAAIoY,aAAa,GAAG5N,IAAI,CAACvG,UAAU,IAAI,KAAK,CAAA;EAC5C,EAAA,IAAIA,UAAU,GAAGgU,mBAAmB,GAC/BG,aAAa,CAACC,WAAW,EAAE,GAC3BD,aAAa,CAAChb,WAAW,EAAiB,CAAA;EAC/C,EAAA,IAAI8G,UAAU,GAAGoU,iBAAiB,CAAC3mB,IAAI,CAAC,CAAA;EAExC,EAAA,IAAI6Y,IAAI,CAAC8M,IAAI,KAAKrnB,SAAS,EAAE;EAC3B,IAAA,IAAIua,IAAI,CAACrG,WAAW,KAAK,YAAY,EAAE;EACrC;EACA,MAAA,IAAI,CAACgH,gBAAgB,CAAClH,UAAU,CAAC,EAAE;UACjC,OAAOkU,mBAAmB,EAAE,CAAA;EAC9B,OAAA;QAEA,IAAI9T,IAAI,GACN,OAAOmG,IAAI,CAAC8M,IAAI,KAAK,QAAQ,GACzB9M,IAAI,CAAC8M,IAAI,GACT9M,IAAI,CAAC8M,IAAI,YAAYiB,QAAQ,IAC7B/N,IAAI,CAAC8M,IAAI,YAAYM,eAAe;EACpC;EACAtX,MAAAA,KAAK,CAACzB,IAAI,CAAC2L,IAAI,CAAC8M,IAAI,CAAC3nB,OAAO,EAAE,CAAC,CAACoL,MAAM,CACpC,CAACkG,GAAG,EAAA0B,KAAA,KAAA;EAAA,QAAA,IAAE,CAAC/M,IAAI,EAAE3B,KAAK,CAAC,GAAA0O,KAAA,CAAA;EAAA,QAAA,OAAA,EAAA,GAAQ1B,GAAG,GAAGrL,IAAI,GAAA,GAAA,GAAI3B,KAAK,GAAA,IAAA,CAAA;SAAI,EAClD,EACF,CAAC,GACD2C,MAAM,CAAC4T,IAAI,CAAC8M,IAAI,CAAC,CAAA;QAEvB,OAAO;UACL3lB,IAAI;EACJoa,QAAAA,UAAU,EAAE;YACV9H,UAAU;YACVC,UAAU;YACVC,WAAW,EAAEqG,IAAI,CAACrG,WAAW;EAC7BC,UAAAA,QAAQ,EAAEnU,SAAS;EACnBoP,UAAAA,IAAI,EAAEpP,SAAS;EACfoU,UAAAA,IAAAA;EACF,SAAA;SACD,CAAA;EACH,KAAC,MAAM,IAAImG,IAAI,CAACrG,WAAW,KAAK,kBAAkB,EAAE;EAClD;EACA,MAAA,IAAI,CAACgH,gBAAgB,CAAClH,UAAU,CAAC,EAAE;UACjC,OAAOkU,mBAAmB,EAAE,CAAA;EAC9B,OAAA;QAEA,IAAI;UACF,IAAI9Y,IAAI,GACN,OAAOmL,IAAI,CAAC8M,IAAI,KAAK,QAAQ,GAAGnmB,IAAI,CAACqnB,KAAK,CAAChO,IAAI,CAAC8M,IAAI,CAAC,GAAG9M,IAAI,CAAC8M,IAAI,CAAA;UAEnE,OAAO;YACL3lB,IAAI;EACJoa,UAAAA,UAAU,EAAE;cACV9H,UAAU;cACVC,UAAU;cACVC,WAAW,EAAEqG,IAAI,CAACrG,WAAW;EAC7BC,YAAAA,QAAQ,EAAEnU,SAAS;cACnBoP,IAAI;EACJgF,YAAAA,IAAI,EAAEpU,SAAAA;EACR,WAAA;WACD,CAAA;SACF,CAAC,OAAOsE,CAAC,EAAE;UACV,OAAO4jB,mBAAmB,EAAE,CAAA;EAC9B,OAAA;EACF,KAAA;EACF,GAAA;EAEAnkB,EAAAA,SAAS,CACP,OAAOukB,QAAQ,KAAK,UAAU,EAC9B,+CACF,CAAC,CAAA;EAED,EAAA,IAAIE,YAA6B,CAAA;EACjC,EAAA,IAAIrU,QAAkB,CAAA;IAEtB,IAAIoG,IAAI,CAACpG,QAAQ,EAAE;EACjBqU,IAAAA,YAAY,GAAGC,6BAA6B,CAAClO,IAAI,CAACpG,QAAQ,CAAC,CAAA;MAC3DA,QAAQ,GAAGoG,IAAI,CAACpG,QAAQ,CAAA;EAC1B,GAAC,MAAM,IAAIoG,IAAI,CAAC8M,IAAI,YAAYiB,QAAQ,EAAE;EACxCE,IAAAA,YAAY,GAAGC,6BAA6B,CAAClO,IAAI,CAAC8M,IAAI,CAAC,CAAA;MACvDlT,QAAQ,GAAGoG,IAAI,CAAC8M,IAAI,CAAA;EACtB,GAAC,MAAM,IAAI9M,IAAI,CAAC8M,IAAI,YAAYM,eAAe,EAAE;MAC/Ca,YAAY,GAAGjO,IAAI,CAAC8M,IAAI,CAAA;EACxBlT,IAAAA,QAAQ,GAAGuU,6BAA6B,CAACF,YAAY,CAAC,CAAA;EACxD,GAAC,MAAM,IAAIjO,IAAI,CAAC8M,IAAI,IAAI,IAAI,EAAE;EAC5BmB,IAAAA,YAAY,GAAG,IAAIb,eAAe,EAAE,CAAA;EACpCxT,IAAAA,QAAQ,GAAG,IAAImU,QAAQ,EAAE,CAAA;EAC3B,GAAC,MAAM;MACL,IAAI;EACFE,MAAAA,YAAY,GAAG,IAAIb,eAAe,CAACpN,IAAI,CAAC8M,IAAI,CAAC,CAAA;EAC7ClT,MAAAA,QAAQ,GAAGuU,6BAA6B,CAACF,YAAY,CAAC,CAAA;OACvD,CAAC,OAAOlkB,CAAC,EAAE;QACV,OAAO4jB,mBAAmB,EAAE,CAAA;EAC9B,KAAA;EACF,GAAA;EAEA,EAAA,IAAIpM,UAAsB,GAAG;MAC3B9H,UAAU;MACVC,UAAU;EACVC,IAAAA,WAAW,EACRqG,IAAI,IAAIA,IAAI,CAACrG,WAAW,IAAK,mCAAmC;MACnEC,QAAQ;EACR/E,IAAAA,IAAI,EAAEpP,SAAS;EACfoU,IAAAA,IAAI,EAAEpU,SAAAA;KACP,CAAA;EAED,EAAA,IAAIkb,gBAAgB,CAACY,UAAU,CAAC9H,UAAU,CAAC,EAAE;MAC3C,OAAO;QAAEtS,IAAI;EAAEoa,MAAAA,UAAAA;OAAY,CAAA;EAC7B,GAAA;;EAEA;EACA,EAAA,IAAI/W,UAAU,GAAGpD,SAAS,CAACD,IAAI,CAAC,CAAA;EAChC;EACA;EACA;EACA,EAAA,IAAIumB,SAAS,IAAIljB,UAAU,CAACnD,MAAM,IAAI8lB,kBAAkB,CAAC3iB,UAAU,CAACnD,MAAM,CAAC,EAAE;EAC3E4mB,IAAAA,YAAY,CAACV,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;EAClC,GAAA;IACA/iB,UAAU,CAACnD,MAAM,GAAA,GAAA,GAAO4mB,YAAc,CAAA;IAEtC,OAAO;EAAE9mB,IAAAA,IAAI,EAAEL,UAAU,CAAC0D,UAAU,CAAC;EAAE+W,IAAAA,UAAAA;KAAY,CAAA;EACrD,CAAA;;EAEA;EACA;EACA,SAAS8K,6BAA6BA,CACpClf,OAAiC,EACjCsW,UAAkB,EAClB2K,eAAe,EACf;EAAA,EAAA,IADAA,eAAe,KAAA,KAAA,CAAA,EAAA;EAAfA,IAAAA,eAAe,GAAG,KAAK,CAAA;EAAA,GAAA;EAEvB,EAAA,IAAI9oB,KAAK,GAAG6H,OAAO,CAAC0P,SAAS,CAAEJ,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,KAAKoX,UAAU,CAAC,CAAA;IAC/D,IAAIne,KAAK,IAAI,CAAC,EAAE;EACd,IAAA,OAAO6H,OAAO,CAAC7D,KAAK,CAAC,CAAC,EAAE8kB,eAAe,GAAG9oB,KAAK,GAAG,CAAC,GAAGA,KAAK,CAAC,CAAA;EAC9D,GAAA;EACA,EAAA,OAAO6H,OAAO,CAAA;EAChB,CAAA;EAEA,SAASwX,gBAAgBA,CACvB5d,OAAgB,EAChBvB,KAAkB,EAClB2H,OAAiC,EACjCoU,UAAkC,EAClCjb,QAAkB,EAClBoZ,gBAAyB,EACzB2O,2BAAoC,EACpCpQ,sBAA+B,EAC/BC,uBAAiC,EACjCC,qBAAkC,EAClCQ,eAA4B,EAC5BF,gBAA6C,EAC7CD,gBAA6B,EAC7B0D,WAAsC,EACtCtV,QAA4B,EAC5B6V,mBAAyC,EACU;IACnD,IAAIE,YAAY,GAAGF,mBAAmB,GAClCM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GACnCA,mBAAmB,CAAC,CAAC,CAAC,CAACvX,KAAK,GAC5BuX,mBAAmB,CAAC,CAAC,CAAC,CAAC7U,IAAI,GAC7BnI,SAAS,CAAA;IACb,IAAI6oB,UAAU,GAAGvnB,OAAO,CAACC,SAAS,CAACxB,KAAK,CAACc,QAAQ,CAAC,CAAA;EAClD,EAAA,IAAIioB,OAAO,GAAGxnB,OAAO,CAACC,SAAS,CAACV,QAAQ,CAAC,CAAA;;EAEzC;IACA,IAAIkoB,eAAe,GAAGrhB,OAAO,CAAA;EAC7B,EAAA,IAAIuS,gBAAgB,IAAIla,KAAK,CAACoX,MAAM,EAAE;EACpC;EACA;EACA;EACA;EACA;EACA4R,IAAAA,eAAe,GAAGnC,6BAA6B,CAC7Clf,OAAO,EACP+D,MAAM,CAAC2P,IAAI,CAACrb,KAAK,CAACoX,MAAM,CAAC,CAAC,CAAC,CAAC,EAC5B,IACF,CAAC,CAAA;KACF,MAAM,IAAI6F,mBAAmB,IAAIM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,EAAE;EACvE;EACA;MACA+L,eAAe,GAAGnC,6BAA6B,CAC7Clf,OAAO,EACPsV,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAA;EACH,GAAA;;EAEA;EACA;EACA;IACA,IAAIgM,YAAY,GAAGhM,mBAAmB,GAClCA,mBAAmB,CAAC,CAAC,CAAC,CAACuI,UAAU,GACjCvlB,SAAS,CAAA;IACb,IAAIipB,sBAAsB,GACxBL,2BAA2B,IAAII,YAAY,IAAIA,YAAY,IAAI,GAAG,CAAA;IAEpE,IAAIE,iBAAiB,GAAGH,eAAe,CAACle,MAAM,CAAC,CAAC7C,KAAK,EAAEnI,KAAK,KAAK;MAC/D,IAAI;EAAEuG,MAAAA,KAAAA;EAAM,KAAC,GAAG4B,KAAK,CAAA;MACrB,IAAI5B,KAAK,CAAC6Q,IAAI,EAAE;EACd;EACA,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;EAEA,IAAA,IAAI7Q,KAAK,CAAC8Q,MAAM,IAAI,IAAI,EAAE;EACxB,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;EAEA,IAAA,IAAI+C,gBAAgB,EAAE;QACpB,OAAO5C,0BAA0B,CAACjR,KAAK,EAAErG,KAAK,CAACkI,UAAU,EAAElI,KAAK,CAACoX,MAAM,CAAC,CAAA;EAC1E,KAAA;;EAEA;EACA,IAAA,IACEgS,WAAW,CAACppB,KAAK,CAACkI,UAAU,EAAElI,KAAK,CAAC2H,OAAO,CAAC7H,KAAK,CAAC,EAAEmI,KAAK,CAAC,IAC1DyQ,uBAAuB,CAAC7N,IAAI,CAAEhE,EAAE,IAAKA,EAAE,KAAKoB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,EAC3D;EACA,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;;EAEA;EACA;EACA;EACA;EACA,IAAA,IAAIwiB,iBAAiB,GAAGrpB,KAAK,CAAC2H,OAAO,CAAC7H,KAAK,CAAC,CAAA;MAC5C,IAAIwpB,cAAc,GAAGrhB,KAAK,CAAA;EAE1B,IAAA,OAAOshB,sBAAsB,CAACthB,KAAK,EAAAnD,QAAA,CAAA;QACjCgkB,UAAU;QACVU,aAAa,EAAEH,iBAAiB,CAAClhB,MAAM;QACvC4gB,OAAO;QACPU,UAAU,EAAEH,cAAc,CAACnhB,MAAAA;EAAM,KAAA,EAC9B4T,UAAU,EAAA;QACboB,YAAY;QACZ8L,YAAY;QACZS,uBAAuB,EAAER,sBAAsB,GAC3C,KAAK;EACL;EACAzQ,MAAAA,sBAAsB,IACtBqQ,UAAU,CAAC9nB,QAAQ,GAAG8nB,UAAU,CAACjnB,MAAM,KACrCknB,OAAO,CAAC/nB,QAAQ,GAAG+nB,OAAO,CAAClnB,MAAM;EACnC;QACAinB,UAAU,CAACjnB,MAAM,KAAKknB,OAAO,CAAClnB,MAAM,IACpC8nB,kBAAkB,CAACN,iBAAiB,EAAEC,cAAc,CAAA;EAAC,KAAA,CAC1D,CAAC,CAAA;EACJ,GAAC,CAAC,CAAA;;EAEF;IACA,IAAIpK,oBAA2C,GAAG,EAAE,CAAA;EACpDjG,EAAAA,gBAAgB,CAAChQ,OAAO,CAAC,CAAC2W,CAAC,EAAE/e,GAAG,KAAK;EACnC;EACA;EACA;EACA;EACA;MACA,IACEqZ,gBAAgB,IAChB,CAACvS,OAAO,CAACkD,IAAI,CAAEoM,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,KAAK+Y,CAAC,CAACtC,OAAO,CAAC,IAC9CnE,eAAe,CAACxJ,GAAG,CAAC9O,GAAG,CAAC,EACxB;EACA,MAAA,OAAA;EACF,KAAA;MAEA,IAAI+oB,cAAc,GAAG1iB,WAAW,CAACwV,WAAW,EAAEkD,CAAC,CAACje,IAAI,EAAEyF,QAAQ,CAAC,CAAA;;EAE/D;EACA;EACA;EACA;MACA,IAAI,CAACwiB,cAAc,EAAE;QACnB1K,oBAAoB,CAACnd,IAAI,CAAC;UACxBlB,GAAG;UACHyc,OAAO,EAAEsC,CAAC,CAACtC,OAAO;UAClB3b,IAAI,EAAEie,CAAC,CAACje,IAAI;EACZgG,QAAAA,OAAO,EAAE,IAAI;EACbM,QAAAA,KAAK,EAAE,IAAI;EACX2I,QAAAA,UAAU,EAAE,IAAA;EACd,OAAC,CAAC,CAAA;EACF,MAAA,OAAA;EACF,KAAA;;EAEA;EACA;EACA;MACA,IAAI+J,OAAO,GAAG3a,KAAK,CAAC8X,QAAQ,CAAClG,GAAG,CAAC/Q,GAAG,CAAC,CAAA;MACrC,IAAIgpB,YAAY,GAAGzL,cAAc,CAACwL,cAAc,EAAEhK,CAAC,CAACje,IAAI,CAAC,CAAA;MAEzD,IAAImoB,gBAAgB,GAAG,KAAK,CAAA;EAC5B,IAAA,IAAI9Q,gBAAgB,CAACrJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;EAC7B;EACAipB,MAAAA,gBAAgB,GAAG,KAAK,CAAA;OACzB,MAAM,IAAInR,qBAAqB,CAAChJ,GAAG,CAAC9O,GAAG,CAAC,EAAE;EACzC;EACA8X,MAAAA,qBAAqB,CAAC7G,MAAM,CAACjR,GAAG,CAAC,CAAA;EACjCipB,MAAAA,gBAAgB,GAAG,IAAI,CAAA;EACzB,KAAC,MAAM,IACLnP,OAAO,IACPA,OAAO,CAAC3a,KAAK,KAAK,MAAM,IACxB2a,OAAO,CAACvS,IAAI,KAAKnI,SAAS,EAC1B;EACA;EACA;EACA;EACA6pB,MAAAA,gBAAgB,GAAGrR,sBAAsB,CAAA;EAC3C,KAAC,MAAM;EACL;EACA;EACAqR,MAAAA,gBAAgB,GAAGP,sBAAsB,CAACM,YAAY,EAAA/kB,QAAA,CAAA;UACpDgkB,UAAU;EACVU,QAAAA,aAAa,EAAExpB,KAAK,CAAC2H,OAAO,CAAC3H,KAAK,CAAC2H,OAAO,CAACxH,MAAM,GAAG,CAAC,CAAC,CAACgI,MAAM;UAC7D4gB,OAAO;UACPU,UAAU,EAAE9hB,OAAO,CAACA,OAAO,CAACxH,MAAM,GAAG,CAAC,CAAC,CAACgI,MAAAA;EAAM,OAAA,EAC3C4T,UAAU,EAAA;UACboB,YAAY;UACZ8L,YAAY;EACZS,QAAAA,uBAAuB,EAAER,sBAAsB,GAC3C,KAAK,GACLzQ,sBAAAA;EAAsB,OAAA,CAC3B,CAAC,CAAA;EACJ,KAAA;EAEA,IAAA,IAAIqR,gBAAgB,EAAE;QACpB5K,oBAAoB,CAACnd,IAAI,CAAC;UACxBlB,GAAG;UACHyc,OAAO,EAAEsC,CAAC,CAACtC,OAAO;UAClB3b,IAAI,EAAEie,CAAC,CAACje,IAAI;EACZgG,QAAAA,OAAO,EAAEiiB,cAAc;EACvB3hB,QAAAA,KAAK,EAAE4hB,YAAY;UACnBjZ,UAAU,EAAE,IAAIC,eAAe,EAAC;EAClC,OAAC,CAAC,CAAA;EACJ,KAAA;EACF,GAAC,CAAC,CAAA;EAEF,EAAA,OAAO,CAACsY,iBAAiB,EAAEjK,oBAAoB,CAAC,CAAA;EAClD,CAAA;EAEA,SAAS5H,0BAA0BA,CACjCjR,KAA8B,EAC9B6B,UAAwC,EACxCkP,MAAoC,EACpC;EACA;IACA,IAAI/Q,KAAK,CAAC6Q,IAAI,EAAE;EACd,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;EACA,EAAA,IAAI,CAAC7Q,KAAK,CAAC8Q,MAAM,EAAE;EACjB,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EAEA,EAAA,IAAI4S,OAAO,GAAG7hB,UAAU,IAAI,IAAI,IAAIA,UAAU,CAAC7B,KAAK,CAACQ,EAAE,CAAC,KAAK5G,SAAS,CAAA;EACtE,EAAA,IAAI+pB,QAAQ,GAAG5S,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC/Q,KAAK,CAACQ,EAAE,CAAC,KAAK5G,SAAS,CAAA;;EAE/D;EACA,EAAA,IAAI,CAAC8pB,OAAO,IAAIC,QAAQ,EAAE;EACxB,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;;EAEA;EACA,EAAA,IAAI,OAAO3jB,KAAK,CAAC8Q,MAAM,KAAK,UAAU,IAAI9Q,KAAK,CAAC8Q,MAAM,CAAC8S,OAAO,KAAK,IAAI,EAAE;EACvE,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;EACA,EAAA,OAAO,CAACF,OAAO,IAAI,CAACC,QAAQ,CAAA;EAC9B,CAAA;EAEA,SAASZ,WAAWA,CAClBc,iBAA4B,EAC5BC,YAAoC,EACpCliB,KAA6B,EAC7B;EACA,EAAA,IAAImiB,KAAK;EACP;EACA,EAAA,CAACD,YAAY;EACb;IACAliB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,KAAKsjB,YAAY,CAAC9jB,KAAK,CAACQ,EAAE,CAAA;;EAE1C;EACA;IACA,IAAIwjB,aAAa,GAAGH,iBAAiB,CAACjiB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,KAAK5G,SAAS,CAAA;;EAEnE;IACA,OAAOmqB,KAAK,IAAIC,aAAa,CAAA;EAC/B,CAAA;EAEA,SAASV,kBAAkBA,CACzBQ,YAAoC,EACpCliB,KAA6B,EAC7B;EACA,EAAA,IAAIqiB,WAAW,GAAGH,YAAY,CAAC9jB,KAAK,CAAC1E,IAAI,CAAA;EACzC,EAAA;EACE;EACAwoB,IAAAA,YAAY,CAACnpB,QAAQ,KAAKiH,KAAK,CAACjH,QAAQ;EACxC;EACA;MACCspB,WAAW,IAAI,IAAI,IAClBA,WAAW,CAAC3gB,QAAQ,CAAC,GAAG,CAAC,IACzBwgB,YAAY,CAAChiB,MAAM,CAAC,GAAG,CAAC,KAAKF,KAAK,CAACE,MAAM,CAAC,GAAG,CAAA;EAAE,IAAA;EAErD,CAAA;EAEA,SAASohB,sBAAsBA,CAC7BgB,WAAmC,EACnCC,GAAiC,EACjC;EACA,EAAA,IAAID,WAAW,CAAClkB,KAAK,CAACyjB,gBAAgB,EAAE;MACtC,IAAIW,WAAW,GAAGF,WAAW,CAAClkB,KAAK,CAACyjB,gBAAgB,CAACU,GAAG,CAAC,CAAA;EACzD,IAAA,IAAI,OAAOC,WAAW,KAAK,SAAS,EAAE;EACpC,MAAA,OAAOA,WAAW,CAAA;EACpB,KAAA;EACF,GAAA;IAEA,OAAOD,GAAG,CAACd,uBAAuB,CAAA;EACpC,CAAA;EAEA,SAASpF,eAAeA,CACtBhH,OAAsB,EACtBvW,QAA+B,EAC/B2V,WAAsC,EACtChW,QAAuB,EACvBF,kBAA8C,EAC9C;EAAA,EAAA,IAAAkkB,gBAAA,CAAA;EACA,EAAA,IAAIC,eAA0C,CAAA;EAC9C,EAAA,IAAIrN,OAAO,EAAE;EACX,IAAA,IAAIjX,KAAK,GAAGK,QAAQ,CAAC4W,OAAO,CAAC,CAAA;EAC7BtZ,IAAAA,SAAS,CACPqC,KAAK,EAC+CiX,mDAAAA,GAAAA,OACtD,CAAC,CAAA;EACD,IAAA,IAAI,CAACjX,KAAK,CAACU,QAAQ,EAAE;QACnBV,KAAK,CAACU,QAAQ,GAAG,EAAE,CAAA;EACrB,KAAA;MACA4jB,eAAe,GAAGtkB,KAAK,CAACU,QAAQ,CAAA;EAClC,GAAC,MAAM;EACL4jB,IAAAA,eAAe,GAAGjO,WAAW,CAAA;EAC/B,GAAA;;EAEA;EACA;EACA;IACA,IAAIkO,cAAc,GAAG7jB,QAAQ,CAAC+D,MAAM,CACjC+f,QAAQ,IACP,CAACF,eAAe,CAAC9f,IAAI,CAAEigB,aAAa,IAClCC,WAAW,CAACF,QAAQ,EAAEC,aAAa,CACrC,CACJ,CAAC,CAAA;EAED,EAAA,IAAIpG,SAAS,GAAGpe,yBAAyB,CACvCskB,cAAc,EACdpkB,kBAAkB,EAClB,CAAC8W,OAAO,IAAI,GAAG,EAAE,OAAO,EAAE1W,MAAM,CAAC,CAAA8jB,CAAAA,gBAAA,GAAAC,eAAe,qBAAfD,gBAAA,CAAiBvqB,MAAM,KAAI,GAAG,CAAC,CAAC,EACjEuG,QACF,CAAC,CAAA;EAEDikB,EAAAA,eAAe,CAAC5oB,IAAI,CAAC,GAAG2iB,SAAS,CAAC,CAAA;EACpC,CAAA;EAEA,SAASqG,WAAWA,CAClBF,QAA6B,EAC7BC,aAAkC,EACzB;EACT;EACA,EAAA,IACE,IAAI,IAAID,QAAQ,IAChB,IAAI,IAAIC,aAAa,IACrBD,QAAQ,CAAChkB,EAAE,KAAKikB,aAAa,CAACjkB,EAAE,EAChC;EACA,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;IACA,IACE,EACEgkB,QAAQ,CAAC/qB,KAAK,KAAKgrB,aAAa,CAAChrB,KAAK,IACtC+qB,QAAQ,CAAClpB,IAAI,KAAKmpB,aAAa,CAACnpB,IAAI,IACpCkpB,QAAQ,CAACniB,aAAa,KAAKoiB,aAAa,CAACpiB,aAAa,CACvD,EACD;EACA,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;;EAEA;EACA;IACA,IACE,CAAC,CAACmiB,QAAQ,CAAC9jB,QAAQ,IAAI8jB,QAAQ,CAAC9jB,QAAQ,CAAC5G,MAAM,KAAK,CAAC,MACpD,CAAC2qB,aAAa,CAAC/jB,QAAQ,IAAI+jB,aAAa,CAAC/jB,QAAQ,CAAC5G,MAAM,KAAK,CAAC,CAAC,EAChE;EACA,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;EACA;IACA,OAAO0qB,QAAQ,CAAC9jB,QAAQ,CAAEoE,KAAK,CAAC,CAAC6f,MAAM,EAAEpjB,CAAC,KAAA;EAAA,IAAA,IAAAqjB,qBAAA,CAAA;EAAA,IAAA,OAAA,CAAAA,qBAAA,GACxCH,aAAa,CAAC/jB,QAAQ,KAAA,IAAA,GAAA,KAAA,CAAA,GAAtBkkB,qBAAA,CAAwBpgB,IAAI,CAAEqgB,MAAM,IAAKH,WAAW,CAACC,MAAM,EAAEE,MAAM,CAAC,CAAC,CAAA;EAAA,GACvE,CAAC,CAAA;EACH,CAAA;;EAEA;EACA;EACA;EACA;EACA;EACA,eAAeC,mBAAmBA,CAChC9kB,KAA8B,EAC9BG,kBAA8C,EAC9CE,QAAuB,EACvB;EACA,EAAA,IAAI,CAACL,KAAK,CAAC6Q,IAAI,EAAE;EACf,IAAA,OAAA;EACF,GAAA;EAEA,EAAA,IAAIkU,SAAS,GAAG,MAAM/kB,KAAK,CAAC6Q,IAAI,EAAE,CAAA;;EAElC;EACA;EACA;EACA,EAAA,IAAI,CAAC7Q,KAAK,CAAC6Q,IAAI,EAAE;EACf,IAAA,OAAA;EACF,GAAA;EAEA,EAAA,IAAImU,aAAa,GAAG3kB,QAAQ,CAACL,KAAK,CAACQ,EAAE,CAAC,CAAA;EACtC7C,EAAAA,SAAS,CAACqnB,aAAa,EAAE,4BAA4B,CAAC,CAAA;;EAEtD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;IACA,IAAIC,YAAiC,GAAG,EAAE,CAAA;EAC1C,EAAA,KAAK,IAAIC,iBAAiB,IAAIH,SAAS,EAAE;EACvC,IAAA,IAAII,gBAAgB,GAClBH,aAAa,CAACE,iBAAiB,CAA+B,CAAA;EAEhE,IAAA,IAAIE,2BAA2B,GAC7BD,gBAAgB,KAAKvrB,SAAS;EAC9B;EACA;EACAsrB,IAAAA,iBAAiB,KAAK,kBAAkB,CAAA;EAE1CtqB,IAAAA,OAAO,CACL,CAACwqB,2BAA2B,EAC5B,aAAUJ,aAAa,CAACxkB,EAAE,GAAA,6BAAA,GAA4B0kB,iBAAiB,GAAA,KAAA,GAAA,6EACQ,IACjDA,4BAAAA,GAAAA,iBAAiB,yBACjD,CAAC,CAAA;MAED,IACE,CAACE,2BAA2B,IAC5B,CAACvlB,kBAAkB,CAACyJ,GAAG,CAAC4b,iBAAsC,CAAC,EAC/D;EACAD,MAAAA,YAAY,CAACC,iBAAiB,CAAC,GAC7BH,SAAS,CAACG,iBAAiB,CAA2B,CAAA;EAC1D,KAAA;EACF,GAAA;;EAEA;EACA;EACA7f,EAAAA,MAAM,CAAC7F,MAAM,CAACwlB,aAAa,EAAEC,YAAY,CAAC,CAAA;;EAE1C;EACA;EACA;IACA5f,MAAM,CAAC7F,MAAM,CAACwlB,aAAa,EAAAvmB,QAAA,CAKtB0B,EAAAA,EAAAA,kBAAkB,CAAC6kB,aAAa,CAAC,EAAA;EACpCnU,IAAAA,IAAI,EAAEjX,SAAAA;EAAS,GAAA,CAChB,CAAC,CAAA;EACJ,CAAA;;EAEA;EACA,eAAewV,mBAAmBA,CAAAiW,KAAA,EAE6B;IAAA,IAF5B;EACjC/jB,IAAAA,OAAAA;EACwB,GAAC,GAAA+jB,KAAA,CAAA;IACzB,IAAIzM,aAAa,GAAGtX,OAAO,CAACmD,MAAM,CAAEmM,CAAC,IAAKA,CAAC,CAAC0U,UAAU,CAAC,CAAA;EACvD,EAAA,IAAIrN,OAAO,GAAG,MAAM5N,OAAO,CAACiS,GAAG,CAAC1D,aAAa,CAACrf,GAAG,CAAEqX,CAAC,IAAKA,CAAC,CAACzE,OAAO,EAAE,CAAC,CAAC,CAAA;EACtE,EAAA,OAAO8L,OAAO,CAACvT,MAAM,CACnB,CAACkG,GAAG,EAAEnH,MAAM,EAAElC,CAAC,KACb8D,MAAM,CAAC7F,MAAM,CAACoL,GAAG,EAAE;MAAE,CAACgO,aAAa,CAACrX,CAAC,CAAC,CAACvB,KAAK,CAACQ,EAAE,GAAGiD,MAAAA;EAAO,GAAC,CAAC,EAC7D,EACF,CAAC,CAAA;EACH,CAAA;EAEA,eAAeqY,oBAAoBA,CACjC5M,gBAAsC,EACtCvF,IAAyB,EACzBhQ,KAAyB,EACzB+c,OAAgB,EAChBkC,aAAuC,EACvCtX,OAAiC,EACjCsa,UAAyB,EACzBvb,QAAuB,EACvBF,kBAA8C,EAC9C4e,cAAwB,EACqB;IAC7C,IAAIwG,4BAA4B,GAAGjkB,OAAO,CAAC/H,GAAG,CAAEqX,CAAC,IAC/CA,CAAC,CAAC5Q,KAAK,CAAC6Q,IAAI,GACRiU,mBAAmB,CAAClU,CAAC,CAAC5Q,KAAK,EAAEG,kBAAkB,EAAEE,QAAQ,CAAC,GAC1DzG,SACN,CAAC,CAAA;IAED,IAAI4rB,SAAS,GAAGlkB,OAAO,CAAC/H,GAAG,CAAC,CAACqI,KAAK,EAAEL,CAAC,KAAK;EACxC,IAAA,IAAIkkB,gBAAgB,GAAGF,4BAA4B,CAAChkB,CAAC,CAAC,CAAA;EACtD,IAAA,IAAI+jB,UAAU,GAAG1M,aAAa,CAACpU,IAAI,CAAEoM,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,KAAKoB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,CAAA;EACzE;EACA;EACA;EACA;EACA,IAAA,IAAI2L,OAAqC,GAAG,MAAOuZ,eAAe,IAAK;QACrE,IACEA,eAAe,IACfhP,OAAO,CAACsB,MAAM,KAAK,KAAK,KACvBpW,KAAK,CAAC5B,KAAK,CAAC6Q,IAAI,IAAIjP,KAAK,CAAC5B,KAAK,CAAC8Q,MAAM,CAAC,EACxC;EACAwU,QAAAA,UAAU,GAAG,IAAI,CAAA;EACnB,OAAA;QACA,OAAOA,UAAU,GACbK,kBAAkB,CAChBhc,IAAI,EACJ+M,OAAO,EACP9U,KAAK,EACL6jB,gBAAgB,EAChBC,eAAe,EACf3G,cACF,CAAC,GACD1U,OAAO,CAAC8B,OAAO,CAAC;UAAExC,IAAI,EAAE/J,UAAU,CAACmC,IAAI;EAAE0B,QAAAA,MAAM,EAAE7J,SAAAA;EAAU,OAAC,CAAC,CAAA;OAClE,CAAA;MAED,OAAA6E,QAAA,KACKmD,KAAK,EAAA;QACR0jB,UAAU;EACVnZ,MAAAA,OAAAA;EAAO,KAAA,CAAA,CAAA;EAEX,GAAC,CAAC,CAAA;;EAEF;EACA;EACA;EACA,EAAA,IAAI8L,OAAO,GAAG,MAAM/I,gBAAgB,CAAC;EACnC5N,IAAAA,OAAO,EAAEkkB,SAAS;MAClB9O,OAAO;EACP5U,IAAAA,MAAM,EAAER,OAAO,CAAC,CAAC,CAAC,CAACQ,MAAM;MACzB8Z,UAAU;EACV2E,IAAAA,OAAO,EAAExB,cAAAA;EACX,GAAC,CAAC,CAAA;;EAEF;EACA;EACA;IACA,IAAI;EACF,IAAA,MAAM1U,OAAO,CAACiS,GAAG,CAACiJ,4BAA4B,CAAC,CAAA;KAChD,CAAC,OAAOrnB,CAAC,EAAE;EACV;EAAA,GAAA;EAGF,EAAA,OAAO+Z,OAAO,CAAA;EAChB,CAAA;;EAEA;EACA,eAAe0N,kBAAkBA,CAC/Bhc,IAAyB,EACzB+M,OAAgB,EAChB9U,KAA6B,EAC7B6jB,gBAA2C,EAC3CC,eAA4D,EAC5DE,aAAuB,EACM;EAC7B,EAAA,IAAIniB,MAA0B,CAAA;EAC9B,EAAA,IAAIoiB,QAAkC,CAAA;IAEtC,IAAIC,UAAU,GACZC,OAAsE,IACtC;EAChC;EACA,IAAA,IAAI5b,MAAkB,CAAA;EACtB;EACA;EACA,IAAA,IAAIC,YAAY,GAAG,IAAIC,OAAO,CAAqB,CAAC1D,CAAC,EAAE2D,CAAC,KAAMH,MAAM,GAAGG,CAAE,CAAC,CAAA;EAC1Eub,IAAAA,QAAQ,GAAGA,MAAM1b,MAAM,EAAE,CAAA;MACzBuM,OAAO,CAAC/L,MAAM,CAACjL,gBAAgB,CAAC,OAAO,EAAEmmB,QAAQ,CAAC,CAAA;MAElD,IAAIG,aAAa,GAAIC,GAAa,IAAK;EACrC,MAAA,IAAI,OAAOF,OAAO,KAAK,UAAU,EAAE;EACjC,QAAA,OAAO1b,OAAO,CAACF,MAAM,CACnB,IAAIrM,KAAK,CACP,kEAAA,IAAA,IAAA,GACM6L,IAAI,GAAA,eAAA,GAAe/H,KAAK,CAAC5B,KAAK,CAACQ,EAAE,GAAA,GAAA,CACzC,CACF,CAAC,CAAA;EACH,OAAA;EACA,MAAA,OAAOulB,OAAO,CACZ;UACErP,OAAO;UACP5U,MAAM,EAAEF,KAAK,CAACE,MAAM;EACpBye,QAAAA,OAAO,EAAEqF,aAAAA;EACX,OAAC,EACD,IAAIK,GAAG,KAAKrsB,SAAS,GAAG,CAACqsB,GAAG,CAAC,GAAG,EAAE,CACpC,CAAC,CAAA;OACF,CAAA;MAED,IAAIC,cAA2C,GAAG,CAAC,YAAY;QAC7D,IAAI;EACF,QAAA,IAAIC,GAAG,GAAG,OAAOT,eAAe,GAC5BA,eAAe,CAAEO,GAAY,IAAKD,aAAa,CAACC,GAAG,CAAC,CAAC,GACrDD,aAAa,EAAE,CAAC,CAAA;UACpB,OAAO;EAAErc,UAAAA,IAAI,EAAE,MAAM;EAAElG,UAAAA,MAAM,EAAE0iB,GAAAA;WAAK,CAAA;SACrC,CAAC,OAAOjoB,CAAC,EAAE;UACV,OAAO;EAAEyL,UAAAA,IAAI,EAAE,OAAO;EAAElG,UAAAA,MAAM,EAAEvF,CAAAA;WAAG,CAAA;EACrC,OAAA;EACF,KAAC,GAAG,CAAA;MAEJ,OAAOmM,OAAO,CAACa,IAAI,CAAC,CAACgb,cAAc,EAAE9b,YAAY,CAAC,CAAC,CAAA;KACpD,CAAA;IAED,IAAI;EACF,IAAA,IAAI2b,OAAO,GAAGnkB,KAAK,CAAC5B,KAAK,CAAC2J,IAAI,CAAC,CAAA;;EAE/B;EACA,IAAA,IAAI8b,gBAAgB,EAAE;EACpB,MAAA,IAAIM,OAAO,EAAE;EACX;EACA,QAAA,IAAIK,YAAY,CAAA;UAChB,IAAI,CAACxoB,KAAK,CAAC,GAAG,MAAMyM,OAAO,CAACiS,GAAG,CAAC;EAC9B;EACA;EACA;EACAwJ,QAAAA,UAAU,CAACC,OAAO,CAAC,CAAC1a,KAAK,CAAEnN,CAAC,IAAK;EAC/BkoB,UAAAA,YAAY,GAAGloB,CAAC,CAAA;EAClB,SAAC,CAAC,EACFunB,gBAAgB,CACjB,CAAC,CAAA;UACF,IAAIW,YAAY,KAAKxsB,SAAS,EAAE;EAC9B,UAAA,MAAMwsB,YAAY,CAAA;EACpB,SAAA;EACA3iB,QAAAA,MAAM,GAAG7F,KAAM,CAAA;EACjB,OAAC,MAAM;EACL;EACA,QAAA,MAAM6nB,gBAAgB,CAAA;EAEtBM,QAAAA,OAAO,GAAGnkB,KAAK,CAAC5B,KAAK,CAAC2J,IAAI,CAAC,CAAA;EAC3B,QAAA,IAAIoc,OAAO,EAAE;EACX;EACA;EACA;EACAtiB,UAAAA,MAAM,GAAG,MAAMqiB,UAAU,CAACC,OAAO,CAAC,CAAA;EACpC,SAAC,MAAM,IAAIpc,IAAI,KAAK,QAAQ,EAAE;YAC5B,IAAIrM,GAAG,GAAG,IAAIlC,GAAG,CAACsb,OAAO,CAACpZ,GAAG,CAAC,CAAA;YAC9B,IAAI3C,QAAQ,GAAG2C,GAAG,CAAC3C,QAAQ,GAAG2C,GAAG,CAAC9B,MAAM,CAAA;YACxC,MAAM8U,sBAAsB,CAAC,GAAG,EAAE;cAChC0H,MAAM,EAAEtB,OAAO,CAACsB,MAAM;cACtBrd,QAAQ;EACRsc,YAAAA,OAAO,EAAErV,KAAK,CAAC5B,KAAK,CAACQ,EAAAA;EACvB,WAAC,CAAC,CAAA;EACJ,SAAC,MAAM;EACL;EACA;YACA,OAAO;cAAEmJ,IAAI,EAAE/J,UAAU,CAACmC,IAAI;EAAE0B,YAAAA,MAAM,EAAE7J,SAAAA;aAAW,CAAA;EACrD,SAAA;EACF,OAAA;EACF,KAAC,MAAM,IAAI,CAACmsB,OAAO,EAAE;QACnB,IAAIzoB,GAAG,GAAG,IAAIlC,GAAG,CAACsb,OAAO,CAACpZ,GAAG,CAAC,CAAA;QAC9B,IAAI3C,QAAQ,GAAG2C,GAAG,CAAC3C,QAAQ,GAAG2C,GAAG,CAAC9B,MAAM,CAAA;QACxC,MAAM8U,sBAAsB,CAAC,GAAG,EAAE;EAChC3V,QAAAA,QAAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAC,MAAM;EACL8I,MAAAA,MAAM,GAAG,MAAMqiB,UAAU,CAACC,OAAO,CAAC,CAAA;EACpC,KAAA;MAEApoB,SAAS,CACP8F,MAAM,CAACA,MAAM,KAAK7J,SAAS,EAC3B,cAAA,IAAe+P,IAAI,KAAK,QAAQ,GAAG,WAAW,GAAG,UAAU,CACrD/H,GAAAA,aAAAA,IAAAA,IAAAA,GAAAA,KAAK,CAAC5B,KAAK,CAACQ,EAAE,GAA4CmJ,2CAAAA,GAAAA,IAAI,GAAK,IAAA,CAAA,GAAA,4CAE3E,CAAC,CAAA;KACF,CAAC,OAAOzL,CAAC,EAAE;EACV;EACA;EACA;MACA,OAAO;QAAEyL,IAAI,EAAE/J,UAAU,CAACP,KAAK;EAAEoE,MAAAA,MAAM,EAAEvF,CAAAA;OAAG,CAAA;EAC9C,GAAC,SAAS;EACR,IAAA,IAAI2nB,QAAQ,EAAE;QACZnP,OAAO,CAAC/L,MAAM,CAAChL,mBAAmB,CAAC,OAAO,EAAEkmB,QAAQ,CAAC,CAAA;EACvD,KAAA;EACF,GAAA;EAEA,EAAA,OAAOpiB,MAAM,CAAA;EACf,CAAA;EAEA,eAAewY,qCAAqCA,CAClDoK,kBAAsC,EACjB;IACrB,IAAI;MAAE5iB,MAAM;EAAEkG,IAAAA,IAAAA;EAAK,GAAC,GAAG0c,kBAAkB,CAAA;EAEzC,EAAA,IAAI9G,UAAU,CAAC9b,MAAM,CAAC,EAAE;EACtB,IAAA,IAAI1B,IAAS,CAAA;MAEb,IAAI;QACF,IAAIukB,WAAW,GAAG7iB,MAAM,CAAC2F,OAAO,CAACmC,GAAG,CAAC,cAAc,CAAC,CAAA;EACpD;EACA;QACA,IAAI+a,WAAW,IAAI,uBAAuB,CAAC1hB,IAAI,CAAC0hB,WAAW,CAAC,EAAE;EAC5D,QAAA,IAAI7iB,MAAM,CAACwd,IAAI,IAAI,IAAI,EAAE;EACvBlf,UAAAA,IAAI,GAAG,IAAI,CAAA;EACb,SAAC,MAAM;EACLA,UAAAA,IAAI,GAAG,MAAM0B,MAAM,CAACuF,IAAI,EAAE,CAAA;EAC5B,SAAA;EACF,OAAC,MAAM;EACLjH,QAAAA,IAAI,GAAG,MAAM0B,MAAM,CAACuK,IAAI,EAAE,CAAA;EAC5B,OAAA;OACD,CAAC,OAAO9P,CAAC,EAAE;QACV,OAAO;UAAEyL,IAAI,EAAE/J,UAAU,CAACP,KAAK;EAAEA,QAAAA,KAAK,EAAEnB,CAAAA;SAAG,CAAA;EAC7C,KAAA;EAEA,IAAA,IAAIyL,IAAI,KAAK/J,UAAU,CAACP,KAAK,EAAE;QAC7B,OAAO;UACLsK,IAAI,EAAE/J,UAAU,CAACP,KAAK;EACtBA,QAAAA,KAAK,EAAE,IAAI4N,iBAAiB,CAACxJ,MAAM,CAAC0F,MAAM,EAAE1F,MAAM,CAACyJ,UAAU,EAAEnL,IAAI,CAAC;UACpEod,UAAU,EAAE1b,MAAM,CAAC0F,MAAM;UACzBC,OAAO,EAAE3F,MAAM,CAAC2F,OAAAA;SACjB,CAAA;EACH,KAAA;MAEA,OAAO;QACLO,IAAI,EAAE/J,UAAU,CAACmC,IAAI;QACrBA,IAAI;QACJod,UAAU,EAAE1b,MAAM,CAAC0F,MAAM;QACzBC,OAAO,EAAE3F,MAAM,CAAC2F,OAAAA;OACjB,CAAA;EACH,GAAA;EAEA,EAAA,IAAIO,IAAI,KAAK/J,UAAU,CAACP,KAAK,EAAE;EAC7B,IAAA,IAAIknB,sBAAsB,CAAC9iB,MAAM,CAAC,EAAE;QAAA,IAAA+iB,aAAA,EAAAC,aAAA,CAAA;EAClC,MAAA,IAAIhjB,MAAM,CAAC1B,IAAI,YAAYjE,KAAK,EAAE;UAAA,IAAA4oB,YAAA,EAAAC,aAAA,CAAA;UAChC,OAAO;YACLhd,IAAI,EAAE/J,UAAU,CAACP,KAAK;YACtBA,KAAK,EAAEoE,MAAM,CAAC1B,IAAI;YAClBod,UAAU,EAAA,CAAAuH,YAAA,GAAEjjB,MAAM,CAACwF,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAXyd,YAAA,CAAavd,MAAM;YAC/BC,OAAO,EAAE,CAAAud,aAAA,GAAAljB,MAAM,CAACwF,IAAI,aAAX0d,aAAA,CAAavd,OAAO,GACzB,IAAIC,OAAO,CAAC5F,MAAM,CAACwF,IAAI,CAACG,OAAO,CAAC,GAChCxP,SAAAA;WACL,CAAA;EACH,OAAA;;EAEA;QACA,OAAO;UACL+P,IAAI,EAAE/J,UAAU,CAACP,KAAK;UACtBA,KAAK,EAAE,IAAI4N,iBAAiB,CAC1B,EAAAuZ,aAAA,GAAA/iB,MAAM,CAACwF,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAXud,aAAA,CAAard,MAAM,KAAI,GAAG,EAC1BvP,SAAS,EACT6J,MAAM,CAAC1B,IACT,CAAC;UACDod,UAAU,EAAE/R,oBAAoB,CAAC3J,MAAM,CAAC,GAAGA,MAAM,CAAC0F,MAAM,GAAGvP,SAAS;UACpEwP,OAAO,EAAE,CAAAqd,aAAA,GAAAhjB,MAAM,CAACwF,IAAI,aAAXwd,aAAA,CAAard,OAAO,GACzB,IAAIC,OAAO,CAAC5F,MAAM,CAACwF,IAAI,CAACG,OAAO,CAAC,GAChCxP,SAAAA;SACL,CAAA;EACH,KAAA;MACA,OAAO;QACL+P,IAAI,EAAE/J,UAAU,CAACP,KAAK;EACtBA,MAAAA,KAAK,EAAEoE,MAAM;QACb0b,UAAU,EAAE/R,oBAAoB,CAAC3J,MAAM,CAAC,GAAGA,MAAM,CAAC0F,MAAM,GAAGvP,SAAAA;OAC5D,CAAA;EACH,GAAA;EAEA,EAAA,IAAIgtB,cAAc,CAACnjB,MAAM,CAAC,EAAE;MAAA,IAAAojB,aAAA,EAAAC,aAAA,CAAA;MAC1B,OAAO;QACLnd,IAAI,EAAE/J,UAAU,CAACmnB,QAAQ;EACzBlN,MAAAA,YAAY,EAAEpW,MAAM;QACpB0b,UAAU,EAAA,CAAA0H,aAAA,GAAEpjB,MAAM,CAACwF,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAX4d,aAAA,CAAa1d,MAAM;EAC/BC,MAAAA,OAAO,EAAE,CAAA0d,CAAAA,aAAA,GAAArjB,MAAM,CAACwF,IAAI,KAAX6d,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,aAAA,CAAa1d,OAAO,KAAI,IAAIC,OAAO,CAAC5F,MAAM,CAACwF,IAAI,CAACG,OAAO,CAAA;OACjE,CAAA;EACH,GAAA;EAEA,EAAA,IAAImd,sBAAsB,CAAC9iB,MAAM,CAAC,EAAE;MAAA,IAAAujB,aAAA,EAAAC,aAAA,CAAA;MAClC,OAAO;QACLtd,IAAI,EAAE/J,UAAU,CAACmC,IAAI;QACrBA,IAAI,EAAE0B,MAAM,CAAC1B,IAAI;QACjBod,UAAU,EAAA,CAAA6H,aAAA,GAAEvjB,MAAM,CAACwF,IAAI,KAAA,IAAA,GAAA,KAAA,CAAA,GAAX+d,aAAA,CAAa7d,MAAM;QAC/BC,OAAO,EAAE,CAAA6d,aAAA,GAAAxjB,MAAM,CAACwF,IAAI,aAAXge,aAAA,CAAa7d,OAAO,GACzB,IAAIC,OAAO,CAAC5F,MAAM,CAACwF,IAAI,CAACG,OAAO,CAAC,GAChCxP,SAAAA;OACL,CAAA;EACH,GAAA;IAEA,OAAO;MAAE+P,IAAI,EAAE/J,UAAU,CAACmC,IAAI;EAAEA,IAAAA,IAAI,EAAE0B,MAAAA;KAAQ,CAAA;EAChD,CAAA;;EAEA;EACA,SAASuY,wCAAwCA,CAC/ChP,QAAkB,EAClB0J,OAAgB,EAChBO,OAAe,EACf3V,OAAiC,EACjCP,QAAgB,EAChBiH,oBAA6B,EAC7B;IACA,IAAIvN,QAAQ,GAAGuS,QAAQ,CAAC5D,OAAO,CAACmC,GAAG,CAAC,UAAU,CAAC,CAAA;EAC/C5N,EAAAA,SAAS,CACPlD,QAAQ,EACR,4EACF,CAAC,CAAA;EAED,EAAA,IAAI,CAAC4T,kBAAkB,CAACzJ,IAAI,CAACnK,QAAQ,CAAC,EAAE;MACtC,IAAIysB,cAAc,GAAG5lB,OAAO,CAAC7D,KAAK,CAChC,CAAC,EACD6D,OAAO,CAAC0P,SAAS,CAAEJ,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,KAAKyW,OAAO,CAAC,GAAG,CACrD,CAAC,CAAA;MACDxc,QAAQ,GAAG8a,WAAW,CACpB,IAAIna,GAAG,CAACsb,OAAO,CAACpZ,GAAG,CAAC,EACpB4pB,cAAc,EACdnmB,QAAQ,EACR,IAAI,EACJtG,QAAQ,EACRuN,oBACF,CAAC,CAAA;MACDgF,QAAQ,CAAC5D,OAAO,CAACG,GAAG,CAAC,UAAU,EAAE9O,QAAQ,CAAC,CAAA;EAC5C,GAAA;EAEA,EAAA,OAAOuS,QAAQ,CAAA;EACjB,CAAA;EAEA,SAASoL,yBAAyBA,CAChC3d,QAAgB,EAChBgoB,UAAe,EACf1hB,QAAgB,EACR;EACR,EAAA,IAAIsN,kBAAkB,CAACzJ,IAAI,CAACnK,QAAQ,CAAC,EAAE;EACrC;MACA,IAAI0sB,kBAAkB,GAAG1sB,QAAQ,CAAA;MACjC,IAAI6C,GAAG,GAAG6pB,kBAAkB,CAACpqB,UAAU,CAAC,IAAI,CAAC,GACzC,IAAI3B,GAAG,CAACqnB,UAAU,CAAC2E,QAAQ,GAAGD,kBAAkB,CAAC,GACjD,IAAI/rB,GAAG,CAAC+rB,kBAAkB,CAAC,CAAA;MAC/B,IAAIE,cAAc,GAAGnmB,aAAa,CAAC5D,GAAG,CAAC3C,QAAQ,EAAEoG,QAAQ,CAAC,IAAI,IAAI,CAAA;MAClE,IAAIzD,GAAG,CAACmC,MAAM,KAAKgjB,UAAU,CAAChjB,MAAM,IAAI4nB,cAAc,EAAE;QACtD,OAAO/pB,GAAG,CAAC3C,QAAQ,GAAG2C,GAAG,CAAC9B,MAAM,GAAG8B,GAAG,CAAC7B,IAAI,CAAA;EAC7C,KAAA;EACF,GAAA;EACA,EAAA,OAAOhB,QAAQ,CAAA;EACjB,CAAA;;EAEA;EACA;EACA;EACA,SAASkc,uBAAuBA,CAC9Bzb,OAAgB,EAChBT,QAA2B,EAC3BkQ,MAAmB,EACnB+K,UAAuB,EACd;EACT,EAAA,IAAIpY,GAAG,GAAGpC,OAAO,CAACC,SAAS,CAAC8mB,iBAAiB,CAACxnB,QAAQ,CAAC,CAAC,CAAC4D,QAAQ,EAAE,CAAA;EACnE,EAAA,IAAI4K,IAAiB,GAAG;EAAE0B,IAAAA,MAAAA;KAAQ,CAAA;IAElC,IAAI+K,UAAU,IAAIZ,gBAAgB,CAACY,UAAU,CAAC9H,UAAU,CAAC,EAAE;MACzD,IAAI;QAAEA,UAAU;EAAEE,MAAAA,WAAAA;EAAY,KAAC,GAAG4H,UAAU,CAAA;EAC5C;EACA;EACA;EACAzM,IAAAA,IAAI,CAAC+O,MAAM,GAAGpK,UAAU,CAACoU,WAAW,EAAE,CAAA;MAEtC,IAAIlU,WAAW,KAAK,kBAAkB,EAAE;EACtC7E,MAAAA,IAAI,CAACG,OAAO,GAAG,IAAIC,OAAO,CAAC;EAAE,QAAA,cAAc,EAAEyE,WAAAA;EAAY,OAAC,CAAC,CAAA;QAC3D7E,IAAI,CAACgY,IAAI,GAAGnmB,IAAI,CAACC,SAAS,CAAC2a,UAAU,CAAC1M,IAAI,CAAC,CAAA;EAC7C,KAAC,MAAM,IAAI8E,WAAW,KAAK,YAAY,EAAE;EACvC;EACA7E,MAAAA,IAAI,CAACgY,IAAI,GAAGvL,UAAU,CAAC1H,IAAI,CAAA;OAC5B,MAAM,IACLF,WAAW,KAAK,mCAAmC,IACnD4H,UAAU,CAAC3H,QAAQ,EACnB;EACA;QACA9E,IAAI,CAACgY,IAAI,GAAGoB,6BAA6B,CAAC3M,UAAU,CAAC3H,QAAQ,CAAC,CAAA;EAChE,KAAC,MAAM;EACL;EACA9E,MAAAA,IAAI,CAACgY,IAAI,GAAGvL,UAAU,CAAC3H,QAAQ,CAAA;EACjC,KAAA;EACF,GAAA;EAEA,EAAA,OAAO,IAAIuS,OAAO,CAAChjB,GAAG,EAAE2L,IAAI,CAAC,CAAA;EAC/B,CAAA;EAEA,SAASoZ,6BAA6BA,CAACtU,QAAkB,EAAmB;EAC1E,EAAA,IAAIqU,YAAY,GAAG,IAAIb,eAAe,EAAE,CAAA;EAExC,EAAA,KAAK,IAAI,CAAC/mB,GAAG,EAAEoD,KAAK,CAAC,IAAImQ,QAAQ,CAACzU,OAAO,EAAE,EAAE;EAC3C;EACA8oB,IAAAA,YAAY,CAACV,MAAM,CAAClnB,GAAG,EAAE,OAAOoD,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGA,KAAK,CAAC2B,IAAI,CAAC,CAAA;EAC1E,GAAA;EAEA,EAAA,OAAO6iB,YAAY,CAAA;EACrB,CAAA;EAEA,SAASE,6BAA6BA,CACpCF,YAA6B,EACnB;EACV,EAAA,IAAIrU,QAAQ,GAAG,IAAImU,QAAQ,EAAE,CAAA;EAC7B,EAAA,KAAK,IAAI,CAAC1nB,GAAG,EAAEoD,KAAK,CAAC,IAAIwkB,YAAY,CAAC9oB,OAAO,EAAE,EAAE;EAC/CyU,IAAAA,QAAQ,CAAC2T,MAAM,CAAClnB,GAAG,EAAEoD,KAAK,CAAC,CAAA;EAC7B,GAAA;EACA,EAAA,OAAOmQ,QAAQ,CAAA;EACjB,CAAA;EAEA,SAAS0S,sBAAsBA,CAC7Bnf,OAAiC,EACjC2W,OAAmC,EACnCrB,mBAAoD,EACpD7D,eAA0C,EAC1CiM,uBAAgC,EAMhC;EACA;IACA,IAAInd,UAAqC,GAAG,EAAE,CAAA;IAC9C,IAAIkP,MAAoC,GAAG,IAAI,CAAA;EAC/C,EAAA,IAAIoO,UAA8B,CAAA;IAClC,IAAImI,UAAU,GAAG,KAAK,CAAA;IACtB,IAAIlI,aAAsC,GAAG,EAAE,CAAA;EAC/C,EAAA,IAAIvJ,YAAY,GACde,mBAAmB,IAAIM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GACxDA,mBAAmB,CAAC,CAAC,CAAC,CAACvX,KAAK,GAC5BzF,SAAS,CAAA;;EAEf;EACA0H,EAAAA,OAAO,CAACsB,OAAO,CAAEhB,KAAK,IAAK;MACzB,IAAI,EAAEA,KAAK,CAAC5B,KAAK,CAACQ,EAAE,IAAIyX,OAAO,CAAC,EAAE;EAChC,MAAA,OAAA;EACF,KAAA;EACA,IAAA,IAAIzX,EAAE,GAAGoB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAA;EACvB,IAAA,IAAIiD,MAAM,GAAGwU,OAAO,CAACzX,EAAE,CAAC,CAAA;MACxB7C,SAAS,CACP,CAACwa,gBAAgB,CAAC1U,MAAM,CAAC,EACzB,qDACF,CAAC,CAAA;EACD,IAAA,IAAIyT,aAAa,CAACzT,MAAM,CAAC,EAAE;EACzB,MAAA,IAAIpE,KAAK,GAAGoE,MAAM,CAACpE,KAAK,CAAA;EACxB;EACA;EACA;QACA,IAAIwW,YAAY,KAAKjc,SAAS,EAAE;EAC9ByF,QAAAA,KAAK,GAAGwW,YAAY,CAAA;EACpBA,QAAAA,YAAY,GAAGjc,SAAS,CAAA;EAC1B,OAAA;EAEAmX,MAAAA,MAAM,GAAGA,MAAM,IAAI,EAAE,CAAA;EAErB,MAAA,IAAIiO,uBAAuB,EAAE;EAC3BjO,QAAAA,MAAM,CAACvQ,EAAE,CAAC,GAAGnB,KAAK,CAAA;EACpB,OAAC,MAAM;EACL;EACA;EACA;EACA,QAAA,IAAIkZ,aAAa,GAAG1B,mBAAmB,CAACvV,OAAO,EAAEd,EAAE,CAAC,CAAA;UACpD,IAAIuQ,MAAM,CAACwH,aAAa,CAACvY,KAAK,CAACQ,EAAE,CAAC,IAAI,IAAI,EAAE;YAC1CuQ,MAAM,CAACwH,aAAa,CAACvY,KAAK,CAACQ,EAAE,CAAC,GAAGnB,KAAK,CAAA;EACxC,SAAA;EACF,OAAA;;EAEA;EACAwC,MAAAA,UAAU,CAACrB,EAAE,CAAC,GAAG5G,SAAS,CAAA;;EAE1B;EACA;QACA,IAAI,CAAC0tB,UAAU,EAAE;EACfA,QAAAA,UAAU,GAAG,IAAI,CAAA;EACjBnI,QAAAA,UAAU,GAAG/R,oBAAoB,CAAC3J,MAAM,CAACpE,KAAK,CAAC,GAC3CoE,MAAM,CAACpE,KAAK,CAAC8J,MAAM,GACnB,GAAG,CAAA;EACT,OAAA;QACA,IAAI1F,MAAM,CAAC2F,OAAO,EAAE;EAClBgW,QAAAA,aAAa,CAAC5e,EAAE,CAAC,GAAGiD,MAAM,CAAC2F,OAAO,CAAA;EACpC,OAAA;EACF,KAAC,MAAM;EACL,MAAA,IAAIkP,gBAAgB,CAAC7U,MAAM,CAAC,EAAE;UAC5BsP,eAAe,CAACxJ,GAAG,CAAC/I,EAAE,EAAEiD,MAAM,CAACoW,YAAY,CAAC,CAAA;UAC5ChY,UAAU,CAACrB,EAAE,CAAC,GAAGiD,MAAM,CAACoW,YAAY,CAAC9X,IAAI,CAAA;EACzC;EACA;EACA,QAAA,IACE0B,MAAM,CAAC0b,UAAU,IAAI,IAAI,IACzB1b,MAAM,CAAC0b,UAAU,KAAK,GAAG,IACzB,CAACmI,UAAU,EACX;YACAnI,UAAU,GAAG1b,MAAM,CAAC0b,UAAU,CAAA;EAChC,SAAA;UACA,IAAI1b,MAAM,CAAC2F,OAAO,EAAE;EAClBgW,UAAAA,aAAa,CAAC5e,EAAE,CAAC,GAAGiD,MAAM,CAAC2F,OAAO,CAAA;EACpC,SAAA;EACF,OAAC,MAAM;EACLvH,QAAAA,UAAU,CAACrB,EAAE,CAAC,GAAGiD,MAAM,CAAC1B,IAAI,CAAA;EAC5B;EACA;EACA,QAAA,IAAI0B,MAAM,CAAC0b,UAAU,IAAI1b,MAAM,CAAC0b,UAAU,KAAK,GAAG,IAAI,CAACmI,UAAU,EAAE;YACjEnI,UAAU,GAAG1b,MAAM,CAAC0b,UAAU,CAAA;EAChC,SAAA;UACA,IAAI1b,MAAM,CAAC2F,OAAO,EAAE;EAClBgW,UAAAA,aAAa,CAAC5e,EAAE,CAAC,GAAGiD,MAAM,CAAC2F,OAAO,CAAA;EACpC,SAAA;EACF,OAAA;EACF,KAAA;EACF,GAAC,CAAC,CAAA;;EAEF;EACA;EACA;EACA,EAAA,IAAIyM,YAAY,KAAKjc,SAAS,IAAIgd,mBAAmB,EAAE;EACrD7F,IAAAA,MAAM,GAAG;EAAE,MAAA,CAAC6F,mBAAmB,CAAC,CAAC,CAAC,GAAGf,YAAAA;OAAc,CAAA;EACnDhU,IAAAA,UAAU,CAAC+U,mBAAmB,CAAC,CAAC,CAAC,CAAC,GAAGhd,SAAS,CAAA;EAChD,GAAA;IAEA,OAAO;MACLiI,UAAU;MACVkP,MAAM;MACNoO,UAAU,EAAEA,UAAU,IAAI,GAAG;EAC7BC,IAAAA,aAAAA;KACD,CAAA;EACH,CAAA;EAEA,SAASxF,iBAAiBA,CACxBjgB,KAAkB,EAClB2H,OAAiC,EACjC2W,OAAmC,EACnCrB,mBAAoD,EACpDiC,oBAA2C,EAC3CY,cAA0C,EAC1C1G,eAA0C,EAI1C;IACA,IAAI;MAAElR,UAAU;EAAEkP,IAAAA,MAAAA;EAAO,GAAC,GAAG0P,sBAAsB,CACjDnf,OAAO,EACP2W,OAAO,EACPrB,mBAAmB,EACnB7D,eAAe,EACf,KAAK;KACN,CAAA;;EAED;EACA8F,EAAAA,oBAAoB,CAACjW,OAAO,CAAEwW,EAAE,IAAK;MACnC,IAAI;QAAE5e,GAAG;QAAEoH,KAAK;EAAE2I,MAAAA,UAAAA;EAAW,KAAC,GAAG6O,EAAE,CAAA;EACnC,IAAA,IAAI3V,MAAM,GAAGgW,cAAc,CAACjf,GAAG,CAAC,CAAA;EAChCmD,IAAAA,SAAS,CAAC8F,MAAM,EAAE,2CAA2C,CAAC,CAAA;;EAE9D;EACA,IAAA,IAAI8G,UAAU,IAAIA,UAAU,CAACI,MAAM,CAACa,OAAO,EAAE;EAC3C;EACA,MAAA,OAAA;EACF,KAAC,MAAM,IAAI0L,aAAa,CAACzT,MAAM,CAAC,EAAE;EAChC,MAAA,IAAI8U,aAAa,GAAG1B,mBAAmB,CAACld,KAAK,CAAC2H,OAAO,EAAEM,KAAK,oBAALA,KAAK,CAAE5B,KAAK,CAACQ,EAAE,CAAC,CAAA;EACvE,MAAA,IAAI,EAAEuQ,MAAM,IAAIA,MAAM,CAACwH,aAAa,CAACvY,KAAK,CAACQ,EAAE,CAAC,CAAC,EAAE;UAC/CuQ,MAAM,GAAAtS,QAAA,CAAA,EAAA,EACDsS,MAAM,EAAA;EACT,UAAA,CAACwH,aAAa,CAACvY,KAAK,CAACQ,EAAE,GAAGiD,MAAM,CAACpE,KAAAA;WAClC,CAAA,CAAA;EACH,OAAA;EACA1F,MAAAA,KAAK,CAAC8X,QAAQ,CAAChG,MAAM,CAACjR,GAAG,CAAC,CAAA;EAC5B,KAAC,MAAM,IAAI2d,gBAAgB,CAAC1U,MAAM,CAAC,EAAE;EACnC;EACA;EACA9F,MAAAA,SAAS,CAAC,KAAK,EAAE,yCAAyC,CAAC,CAAA;EAC7D,KAAC,MAAM,IAAI2a,gBAAgB,CAAC7U,MAAM,CAAC,EAAE;EACnC;EACA;EACA9F,MAAAA,SAAS,CAAC,KAAK,EAAE,iCAAiC,CAAC,CAAA;EACrD,KAAC,MAAM;EACL,MAAA,IAAI0d,WAAW,GAAGL,cAAc,CAACvX,MAAM,CAAC1B,IAAI,CAAC,CAAA;QAC7CpI,KAAK,CAAC8X,QAAQ,CAAClI,GAAG,CAAC/O,GAAG,EAAE6gB,WAAW,CAAC,CAAA;EACtC,KAAA;EACF,GAAC,CAAC,CAAA;IAEF,OAAO;MAAExZ,UAAU;EAAEkP,IAAAA,MAAAA;KAAQ,CAAA;EAC/B,CAAA;EAEA,SAASkE,eAAeA,CACtBpT,UAAqB,EACrB0lB,aAAwB,EACxBjmB,OAAiC,EACjCyP,MAAoC,EACzB;EACX,EAAA,IAAIyW,gBAAgB,GAAA/oB,QAAA,CAAA,EAAA,EAAQ8oB,aAAa,CAAE,CAAA;EAC3C,EAAA,KAAK,IAAI3lB,KAAK,IAAIN,OAAO,EAAE;EACzB,IAAA,IAAId,EAAE,GAAGoB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAA;EACvB,IAAA,IAAI+mB,aAAa,CAACE,cAAc,CAACjnB,EAAE,CAAC,EAAE;EACpC,MAAA,IAAI+mB,aAAa,CAAC/mB,EAAE,CAAC,KAAK5G,SAAS,EAAE;EACnC4tB,QAAAA,gBAAgB,CAAChnB,EAAE,CAAC,GAAG+mB,aAAa,CAAC/mB,EAAE,CAAC,CAAA;EAC1C,OAGE;EAEJ,KAAC,MAAM,IAAIqB,UAAU,CAACrB,EAAE,CAAC,KAAK5G,SAAS,IAAIgI,KAAK,CAAC5B,KAAK,CAAC8Q,MAAM,EAAE;EAC7D;EACA;EACA0W,MAAAA,gBAAgB,CAAChnB,EAAE,CAAC,GAAGqB,UAAU,CAACrB,EAAE,CAAC,CAAA;EACvC,KAAA;MAEA,IAAIuQ,MAAM,IAAIA,MAAM,CAAC0W,cAAc,CAACjnB,EAAE,CAAC,EAAE;EACvC;EACA,MAAA,MAAA;EACF,KAAA;EACF,GAAA;EACA,EAAA,OAAOgnB,gBAAgB,CAAA;EACzB,CAAA;EAEA,SAASjQ,sBAAsBA,CAC7BX,mBAAoD,EACpD;IACA,IAAI,CAACA,mBAAmB,EAAE;EACxB,IAAA,OAAO,EAAE,CAAA;EACX,GAAA;EACA,EAAA,OAAOM,aAAa,CAACN,mBAAmB,CAAC,CAAC,CAAC,CAAC,GACxC;EACE;EACApF,IAAAA,UAAU,EAAE,EAAC;EACf,GAAC,GACD;EACEA,IAAAA,UAAU,EAAE;QACV,CAACoF,mBAAmB,CAAC,CAAC,CAAC,GAAGA,mBAAmB,CAAC,CAAC,CAAC,CAAC7U,IAAAA;EACnD,KAAA;KACD,CAAA;EACP,CAAA;;EAEA;EACA;EACA;EACA,SAAS8U,mBAAmBA,CAC1BvV,OAAiC,EACjC2V,OAAgB,EACQ;EACxB,EAAA,IAAIyQ,eAAe,GAAGzQ,OAAO,GACzB3V,OAAO,CAAC7D,KAAK,CAAC,CAAC,EAAE6D,OAAO,CAAC0P,SAAS,CAAEJ,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,KAAKyW,OAAO,CAAC,GAAG,CAAC,CAAC,GACtE,CAAC,GAAG3V,OAAO,CAAC,CAAA;IAChB,OACEomB,eAAe,CAACC,OAAO,EAAE,CAACjI,IAAI,CAAE9O,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAACuO,gBAAgB,KAAK,IAAI,CAAC,IACxEjN,OAAO,CAAC,CAAC,CAAC,CAAA;EAEd,CAAA;EAEA,SAASiP,sBAAsBA,CAACrQ,MAAiC,EAG/D;EACA;EACA,EAAA,IAAIF,KAAK,GACPE,MAAM,CAACpG,MAAM,KAAK,CAAC,GACfoG,MAAM,CAAC,CAAC,CAAC,GACTA,MAAM,CAACwf,IAAI,CAAEpV,CAAC,IAAKA,CAAC,CAAC7Q,KAAK,IAAI,CAAC6Q,CAAC,CAAChP,IAAI,IAAIgP,CAAC,CAAChP,IAAI,KAAK,GAAG,CAAC,IAAI;MAC1DkF,EAAE,EAAA,sBAAA;KACH,CAAA;IAEP,OAAO;EACLc,IAAAA,OAAO,EAAE,CACP;QACEQ,MAAM,EAAE,EAAE;EACVnH,MAAAA,QAAQ,EAAE,EAAE;EACZ2K,MAAAA,YAAY,EAAE,EAAE;EAChBtF,MAAAA,KAAAA;EACF,KAAC,CACF;EACDA,IAAAA,KAAAA;KACD,CAAA;EACH,CAAA;EAEA,SAASsQ,sBAAsBA,CAC7BnH,MAAc,EAAAye,MAAA,EAcd;IAAA,IAbA;MACEjtB,QAAQ;MACRsc,OAAO;MACPe,MAAM;MACNrO,IAAI;EACJ9L,IAAAA,OAAAA;EAOF,GAAC,GAAA+pB,MAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAAA,MAAA,CAAA;IAEN,IAAI1a,UAAU,GAAG,sBAAsB,CAAA;IACvC,IAAI2a,YAAY,GAAG,iCAAiC,CAAA;IAEpD,IAAI1e,MAAM,KAAK,GAAG,EAAE;EAClB+D,IAAAA,UAAU,GAAG,aAAa,CAAA;EAC1B,IAAA,IAAI8K,MAAM,IAAIrd,QAAQ,IAAIsc,OAAO,EAAE;QACjC4Q,YAAY,GACV,gBAAc7P,MAAM,GAAA,gBAAA,GAAgBrd,QAAQ,GACDsc,SAAAA,IAAAA,yCAAAA,GAAAA,OAAO,UAAK,GACZ,2CAAA,CAAA;EAC/C,KAAC,MAAM,IAAItN,IAAI,KAAK,cAAc,EAAE;EAClCke,MAAAA,YAAY,GAAG,qCAAqC,CAAA;EACtD,KAAC,MAAM,IAAIle,IAAI,KAAK,cAAc,EAAE;EAClCke,MAAAA,YAAY,GAAG,kCAAkC,CAAA;EACnD,KAAA;EACF,GAAC,MAAM,IAAI1e,MAAM,KAAK,GAAG,EAAE;EACzB+D,IAAAA,UAAU,GAAG,WAAW,CAAA;EACxB2a,IAAAA,YAAY,GAAa5Q,UAAAA,GAAAA,OAAO,GAAyBtc,0BAAAA,GAAAA,QAAQ,GAAG,IAAA,CAAA;EACtE,GAAC,MAAM,IAAIwO,MAAM,KAAK,GAAG,EAAE;EACzB+D,IAAAA,UAAU,GAAG,WAAW,CAAA;MACxB2a,YAAY,GAAA,yBAAA,GAA4BltB,QAAQ,GAAG,IAAA,CAAA;EACrD,GAAC,MAAM,IAAIwO,MAAM,KAAK,GAAG,EAAE;EACzB+D,IAAAA,UAAU,GAAG,oBAAoB,CAAA;EACjC,IAAA,IAAI8K,MAAM,IAAIrd,QAAQ,IAAIsc,OAAO,EAAE;EACjC4Q,MAAAA,YAAY,GACV,aAAA,GAAc7P,MAAM,CAACgK,WAAW,EAAE,GAAA,gBAAA,GAAgBrnB,QAAQ,GAAA,SAAA,IAAA,0CAAA,GACdsc,OAAO,GAAA,MAAA,CAAK,GACb,2CAAA,CAAA;OAC9C,MAAM,IAAIe,MAAM,EAAE;EACjB6P,MAAAA,YAAY,iCAA8B7P,MAAM,CAACgK,WAAW,EAAE,GAAG,IAAA,CAAA;EACnE,KAAA;EACF,GAAA;EAEA,EAAA,OAAO,IAAI/U,iBAAiB,CAC1B9D,MAAM,IAAI,GAAG,EACb+D,UAAU,EACV,IAAIpP,KAAK,CAAC+pB,YAAY,CAAC,EACvB,IACF,CAAC,CAAA;EACH,CAAA;;EAEA;EACA,SAASlO,YAAYA,CACnB1B,OAAmC,EACkB;EACrD,EAAA,IAAI3e,OAAO,GAAG+L,MAAM,CAAC/L,OAAO,CAAC2e,OAAO,CAAC,CAAA;EACrC,EAAA,KAAK,IAAI1W,CAAC,GAAGjI,OAAO,CAACQ,MAAM,GAAG,CAAC,EAAEyH,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC5C,IAAI,CAAC/G,GAAG,EAAEiJ,MAAM,CAAC,GAAGnK,OAAO,CAACiI,CAAC,CAAC,CAAA;EAC9B,IAAA,IAAI4W,gBAAgB,CAAC1U,MAAM,CAAC,EAAE;QAC5B,OAAO;UAAEjJ,GAAG;EAAEiJ,QAAAA,MAAAA;SAAQ,CAAA;EACxB,KAAA;EACF,GAAA;EACF,CAAA;EAEA,SAASwe,iBAAiBA,CAAC3mB,IAAQ,EAAE;EACnC,EAAA,IAAIqD,UAAU,GAAG,OAAOrD,IAAI,KAAK,QAAQ,GAAGC,SAAS,CAACD,IAAI,CAAC,GAAGA,IAAI,CAAA;EAClE,EAAA,OAAOL,UAAU,CAAAwD,QAAA,CAAA,EAAA,EAAME,UAAU,EAAA;EAAElD,IAAAA,IAAI,EAAE,EAAA;EAAE,GAAA,CAAE,CAAC,CAAA;EAChD,CAAA;EAEA,SAAS8a,gBAAgBA,CAAC3S,CAAW,EAAEC,CAAW,EAAW;EAC3D,EAAA,IAAID,CAAC,CAACjJ,QAAQ,KAAKkJ,CAAC,CAAClJ,QAAQ,IAAIiJ,CAAC,CAACpI,MAAM,KAAKqI,CAAC,CAACrI,MAAM,EAAE;EACtD,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EAEA,EAAA,IAAIoI,CAAC,CAACnI,IAAI,KAAK,EAAE,EAAE;EACjB;EACA,IAAA,OAAOoI,CAAC,CAACpI,IAAI,KAAK,EAAE,CAAA;KACrB,MAAM,IAAImI,CAAC,CAACnI,IAAI,KAAKoI,CAAC,CAACpI,IAAI,EAAE;EAC5B;EACA,IAAA,OAAO,IAAI,CAAA;EACb,GAAC,MAAM,IAAIoI,CAAC,CAACpI,IAAI,KAAK,EAAE,EAAE;EACxB;EACA,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;;EAEA;EACA;EACA,EAAA,OAAO,KAAK,CAAA;EACd,CAAA;EAMA,SAASukB,oBAAoBA,CAACvc,MAAe,EAAgC;EAC3E,EAAA,OACEA,MAAM,IAAI,IAAI,IACd,OAAOA,MAAM,KAAK,QAAQ,IAC1B,MAAM,IAAIA,MAAM,IAChB,QAAQ,IAAIA,MAAM,KACjBA,MAAM,CAACkG,IAAI,KAAK/J,UAAU,CAACmC,IAAI,IAAI0B,MAAM,CAACkG,IAAI,KAAK/J,UAAU,CAACP,KAAK,CAAC,CAAA;EAEzE,CAAA;EAEA,SAAS0c,kCAAkCA,CAACtY,MAA0B,EAAE;EACtE,EAAA,OACE8b,UAAU,CAAC9b,MAAM,CAACA,MAAM,CAAC,IAAIgK,mBAAmB,CAACnE,GAAG,CAAC7F,MAAM,CAACA,MAAM,CAAC0F,MAAM,CAAC,CAAA;EAE9E,CAAA;EAEA,SAASmP,gBAAgBA,CAAC7U,MAAkB,EAA4B;EACtE,EAAA,OAAOA,MAAM,CAACkG,IAAI,KAAK/J,UAAU,CAACmnB,QAAQ,CAAA;EAC5C,CAAA;EAEA,SAAS7P,aAAaA,CAACzT,MAAkB,EAAyB;EAChE,EAAA,OAAOA,MAAM,CAACkG,IAAI,KAAK/J,UAAU,CAACP,KAAK,CAAA;EACzC,CAAA;EAEA,SAAS8Y,gBAAgBA,CAAC1U,MAAmB,EAA4B;IACvE,OAAO,CAACA,MAAM,IAAIA,MAAM,CAACkG,IAAI,MAAM/J,UAAU,CAACkN,QAAQ,CAAA;EACxD,CAAA;EAEO,SAASyZ,sBAAsBA,CACpC3oB,KAAU,EAC8B;IACxC,OACE,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,IAAI,IAAI,IACb,MAAM,IAAIA,KAAK,IACf,MAAM,IAAIA,KAAK,IACf,MAAM,IAAIA,KAAK,IACfA,KAAK,CAAC+L,IAAI,KAAK,sBAAsB,CAAA;EAEzC,CAAA;EAEO,SAASid,cAAcA,CAAChpB,KAAU,EAAyB;IAChE,IAAImpB,QAAsB,GAAGnpB,KAAK,CAAA;EAClC,EAAA,OACEmpB,QAAQ,IACR,OAAOA,QAAQ,KAAK,QAAQ,IAC5B,OAAOA,QAAQ,CAAChlB,IAAI,KAAK,QAAQ,IACjC,OAAOglB,QAAQ,CAACjb,SAAS,KAAK,UAAU,IACxC,OAAOib,QAAQ,CAAChb,MAAM,KAAK,UAAU,IACrC,OAAOgb,QAAQ,CAAC7a,WAAW,KAAK,UAAU,CAAA;EAE9C,CAAA;EAEA,SAASqT,UAAUA,CAAC3hB,KAAU,EAAqB;EACjD,EAAA,OACEA,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,CAACuL,MAAM,KAAK,QAAQ,IAChC,OAAOvL,KAAK,CAACsP,UAAU,KAAK,QAAQ,IACpC,OAAOtP,KAAK,CAACwL,OAAO,KAAK,QAAQ,IACjC,OAAOxL,KAAK,CAACqjB,IAAI,KAAK,WAAW,CAAA;EAErC,CAAA;EAEA,SAAShB,kBAAkBA,CAACxc,MAAW,EAAsB;EAC3D,EAAA,IAAI,CAAC8b,UAAU,CAAC9b,MAAM,CAAC,EAAE;EACvB,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EAEA,EAAA,IAAI0F,MAAM,GAAG1F,MAAM,CAAC0F,MAAM,CAAA;IAC1B,IAAI1O,QAAQ,GAAGgJ,MAAM,CAAC2F,OAAO,CAACmC,GAAG,CAAC,UAAU,CAAC,CAAA;IAC7C,OAAOpC,MAAM,IAAI,GAAG,IAAIA,MAAM,IAAI,GAAG,IAAI1O,QAAQ,IAAI,IAAI,CAAA;EAC3D,CAAA;EAEA,SAASwkB,aAAaA,CAACjH,MAAc,EAAwC;IAC3E,OAAOxK,mBAAmB,CAAClE,GAAG,CAAC0O,MAAM,CAACjR,WAAW,EAAgB,CAAC,CAAA;EACpE,CAAA;EAEA,SAAS+N,gBAAgBA,CACvBkD,MAAc,EACwC;IACtD,OAAO1K,oBAAoB,CAAChE,GAAG,CAAC0O,MAAM,CAACjR,WAAW,EAAwB,CAAC,CAAA;EAC7E,CAAA;EAEA,eAAewV,gCAAgCA,CAC7Cjb,OAA0C,EAC1C2W,OAAmC,EACnCtN,MAAmB,EACnBwR,cAAwC,EACxC0H,iBAA4B,EAC5B;EACA,EAAA,IAAIvqB,OAAO,GAAG+L,MAAM,CAAC/L,OAAO,CAAC2e,OAAO,CAAC,CAAA;EACrC,EAAA,KAAK,IAAIxe,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,OAAO,CAACQ,MAAM,EAAEL,KAAK,EAAE,EAAE;MACnD,IAAI,CAACwd,OAAO,EAAExT,MAAM,CAAC,GAAGnK,OAAO,CAACG,KAAK,CAAC,CAAA;EACtC,IAAA,IAAImI,KAAK,GAAGN,OAAO,CAACoe,IAAI,CAAE9O,CAAC,IAAK,CAAAA,CAAC,IAAA,IAAA,GAAA,KAAA,CAAA,GAADA,CAAC,CAAE5Q,KAAK,CAACQ,EAAE,MAAKyW,OAAO,CAAC,CAAA;EACxD;EACA;EACA;MACA,IAAI,CAACrV,KAAK,EAAE;EACV,MAAA,SAAA;EACF,KAAA;EAEA,IAAA,IAAIkiB,YAAY,GAAG3H,cAAc,CAACuD,IAAI,CACnC9O,CAAC,IAAKA,CAAC,CAAC5Q,KAAK,CAACQ,EAAE,KAAKoB,KAAK,CAAE5B,KAAK,CAACQ,EACrC,CAAC,CAAA;MACD,IAAIsnB,oBAAoB,GACtBhE,YAAY,IAAI,IAAI,IACpB,CAACR,kBAAkB,CAACQ,YAAY,EAAEliB,KAAK,CAAC,IACxC,CAACiiB,iBAAiB,IAAIA,iBAAiB,CAACjiB,KAAK,CAAC5B,KAAK,CAACQ,EAAE,CAAC,MAAM5G,SAAS,CAAA;EAExE,IAAA,IAAI0e,gBAAgB,CAAC7U,MAAM,CAAC,IAAIqkB,oBAAoB,EAAE;EACpD;EACA;EACA;EACA,MAAA,MAAMxM,mBAAmB,CAAC7X,MAAM,EAAEkH,MAAM,EAAE,KAAK,CAAC,CAACQ,IAAI,CAAE1H,MAAM,IAAK;EAChE,QAAA,IAAIA,MAAM,EAAE;EACVwU,UAAAA,OAAO,CAAChB,OAAO,CAAC,GAAGxT,MAAM,CAAA;EAC3B,SAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAA;EACF,GAAA;EACF,CAAA;EAEA,eAAe+Y,6BAA6BA,CAC1Clb,OAA0C,EAC1C2W,OAAmC,EACnCY,oBAA2C,EAC3C;EACA,EAAA,KAAK,IAAIpf,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGof,oBAAoB,CAAC/e,MAAM,EAAEL,KAAK,EAAE,EAAE;MAChE,IAAI;QAAEe,GAAG;QAAEyc,OAAO;EAAE1M,MAAAA,UAAAA;EAAW,KAAC,GAAGsO,oBAAoB,CAACpf,KAAK,CAAC,CAAA;EAC9D,IAAA,IAAIgK,MAAM,GAAGwU,OAAO,CAACzd,GAAG,CAAC,CAAA;EACzB,IAAA,IAAIoH,KAAK,GAAGN,OAAO,CAACoe,IAAI,CAAE9O,CAAC,IAAK,CAAAA,CAAC,IAAA,IAAA,GAAA,KAAA,CAAA,GAADA,CAAC,CAAE5Q,KAAK,CAACQ,EAAE,MAAKyW,OAAO,CAAC,CAAA;EACxD;EACA;EACA;MACA,IAAI,CAACrV,KAAK,EAAE;EACV,MAAA,SAAA;EACF,KAAA;EAEA,IAAA,IAAI0W,gBAAgB,CAAC7U,MAAM,CAAC,EAAE;EAC5B;EACA;EACA;EACA9F,MAAAA,SAAS,CACP4M,UAAU,EACV,sEACF,CAAC,CAAA;EACD,MAAA,MAAM+Q,mBAAmB,CAAC7X,MAAM,EAAE8G,UAAU,CAACI,MAAM,EAAE,IAAI,CAAC,CAACQ,IAAI,CAC5D1H,MAAM,IAAK;EACV,QAAA,IAAIA,MAAM,EAAE;EACVwU,UAAAA,OAAO,CAACzd,GAAG,CAAC,GAAGiJ,MAAM,CAAA;EACvB,SAAA;EACF,OACF,CAAC,CAAA;EACH,KAAA;EACF,GAAA;EACF,CAAA;EAEA,eAAe6X,mBAAmBA,CAChC7X,MAAsB,EACtBkH,MAAmB,EACnBod,MAAM,EAC4C;EAAA,EAAA,IADlDA,MAAM,KAAA,KAAA,CAAA,EAAA;EAANA,IAAAA,MAAM,GAAG,KAAK,CAAA;EAAA,GAAA;IAEd,IAAIvc,OAAO,GAAG,MAAM/H,MAAM,CAACoW,YAAY,CAAC3N,WAAW,CAACvB,MAAM,CAAC,CAAA;EAC3D,EAAA,IAAIa,OAAO,EAAE;EACX,IAAA,OAAA;EACF,GAAA;EAEA,EAAA,IAAIuc,MAAM,EAAE;MACV,IAAI;QACF,OAAO;UACLpe,IAAI,EAAE/J,UAAU,CAACmC,IAAI;EACrBA,QAAAA,IAAI,EAAE0B,MAAM,CAACoW,YAAY,CAACxN,aAAAA;SAC3B,CAAA;OACF,CAAC,OAAOnO,CAAC,EAAE;EACV;QACA,OAAO;UACLyL,IAAI,EAAE/J,UAAU,CAACP,KAAK;EACtBA,QAAAA,KAAK,EAAEnB,CAAAA;SACR,CAAA;EACH,KAAA;EACF,GAAA;IAEA,OAAO;MACLyL,IAAI,EAAE/J,UAAU,CAACmC,IAAI;EACrBA,IAAAA,IAAI,EAAE0B,MAAM,CAACoW,YAAY,CAAC9X,IAAAA;KAC3B,CAAA;EACH,CAAA;EAEA,SAASuf,kBAAkBA,CAAC9lB,MAAc,EAAW;EACnD,EAAA,OAAO,IAAI+lB,eAAe,CAAC/lB,MAAM,CAAC,CAACimB,MAAM,CAAC,OAAO,CAAC,CAACjd,IAAI,CAAEqC,CAAC,IAAKA,CAAC,KAAK,EAAE,CAAC,CAAA;EAC1E,CAAA;EAEA,SAASkR,cAAcA,CACrBzW,OAAiC,EACjC7G,QAA2B,EAC3B;EACA,EAAA,IAAIe,MAAM,GACR,OAAOf,QAAQ,KAAK,QAAQ,GAAGc,SAAS,CAACd,QAAQ,CAAC,CAACe,MAAM,GAAGf,QAAQ,CAACe,MAAM,CAAA;EAC7E,EAAA,IACE8F,OAAO,CAACA,OAAO,CAACxH,MAAM,GAAG,CAAC,CAAC,CAACkG,KAAK,CAACvG,KAAK,IACvC6nB,kBAAkB,CAAC9lB,MAAM,IAAI,EAAE,CAAC,EAChC;EACA;EACA,IAAA,OAAO8F,OAAO,CAACA,OAAO,CAACxH,MAAM,GAAG,CAAC,CAAC,CAAA;EACpC,GAAA;EACA;EACA;EACA,EAAA,IAAImO,WAAW,GAAGH,0BAA0B,CAACxG,OAAO,CAAC,CAAA;EACrD,EAAA,OAAO2G,WAAW,CAACA,WAAW,CAACnO,MAAM,GAAG,CAAC,CAAC,CAAA;EAC5C,CAAA;EAEA,SAAS2e,2BAA2BA,CAClCrH,UAAsB,EACE;IACxB,IAAI;MAAExD,UAAU;MAAEC,UAAU;MAAEC,WAAW;MAAEE,IAAI;MAAED,QAAQ;EAAE/E,IAAAA,IAAAA;EAAK,GAAC,GAC/DoI,UAAU,CAAA;IACZ,IAAI,CAACxD,UAAU,IAAI,CAACC,UAAU,IAAI,CAACC,WAAW,EAAE;EAC9C,IAAA,OAAA;EACF,GAAA;IAEA,IAAIE,IAAI,IAAI,IAAI,EAAE;MAChB,OAAO;QACLJ,UAAU;QACVC,UAAU;QACVC,WAAW;EACXC,MAAAA,QAAQ,EAAEnU,SAAS;EACnBoP,MAAAA,IAAI,EAAEpP,SAAS;EACfoU,MAAAA,IAAAA;OACD,CAAA;EACH,GAAC,MAAM,IAAID,QAAQ,IAAI,IAAI,EAAE;MAC3B,OAAO;QACLH,UAAU;QACVC,UAAU;QACVC,WAAW;QACXC,QAAQ;EACR/E,MAAAA,IAAI,EAAEpP,SAAS;EACfoU,MAAAA,IAAI,EAAEpU,SAAAA;OACP,CAAA;EACH,GAAC,MAAM,IAAIoP,IAAI,KAAKpP,SAAS,EAAE;MAC7B,OAAO;QACLgU,UAAU;QACVC,UAAU;QACVC,WAAW;EACXC,MAAAA,QAAQ,EAAEnU,SAAS;QACnBoP,IAAI;EACJgF,MAAAA,IAAI,EAAEpU,SAAAA;OACP,CAAA;EACH,GAAA;EACF,CAAA;EAEA,SAASud,oBAAoBA,CAC3B1c,QAAkB,EAClBib,UAAuB,EACM;EAC7B,EAAA,IAAIA,UAAU,EAAE;EACd,IAAA,IAAItE,UAAuC,GAAG;EAC5CzX,MAAAA,KAAK,EAAE,SAAS;QAChBc,QAAQ;QACRmT,UAAU,EAAE8H,UAAU,CAAC9H,UAAU;QACjCC,UAAU,EAAE6H,UAAU,CAAC7H,UAAU;QACjCC,WAAW,EAAE4H,UAAU,CAAC5H,WAAW;QACnCC,QAAQ,EAAE2H,UAAU,CAAC3H,QAAQ;QAC7B/E,IAAI,EAAE0M,UAAU,CAAC1M,IAAI;QACrBgF,IAAI,EAAE0H,UAAU,CAAC1H,IAAAA;OAClB,CAAA;EACD,IAAA,OAAOoD,UAAU,CAAA;EACnB,GAAC,MAAM;EACL,IAAA,IAAIA,UAAuC,GAAG;EAC5CzX,MAAAA,KAAK,EAAE,SAAS;QAChBc,QAAQ;EACRmT,MAAAA,UAAU,EAAEhU,SAAS;EACrBiU,MAAAA,UAAU,EAAEjU,SAAS;EACrBkU,MAAAA,WAAW,EAAElU,SAAS;EACtBmU,MAAAA,QAAQ,EAAEnU,SAAS;EACnBoP,MAAAA,IAAI,EAAEpP,SAAS;EACfoU,MAAAA,IAAI,EAAEpU,SAAAA;OACP,CAAA;EACD,IAAA,OAAOwX,UAAU,CAAA;EACnB,GAAA;EACF,CAAA;EAEA,SAASqG,uBAAuBA,CAC9Bhd,QAAkB,EAClBib,UAAsB,EACU;EAChC,EAAA,IAAItE,UAA0C,GAAG;EAC/CzX,IAAAA,KAAK,EAAE,YAAY;MACnBc,QAAQ;MACRmT,UAAU,EAAE8H,UAAU,CAAC9H,UAAU;MACjCC,UAAU,EAAE6H,UAAU,CAAC7H,UAAU;MACjCC,WAAW,EAAE4H,UAAU,CAAC5H,WAAW;MACnCC,QAAQ,EAAE2H,UAAU,CAAC3H,QAAQ;MAC7B/E,IAAI,EAAE0M,UAAU,CAAC1M,IAAI;MACrBgF,IAAI,EAAE0H,UAAU,CAAC1H,IAAAA;KAClB,CAAA;EACD,EAAA,OAAOoD,UAAU,CAAA;EACnB,CAAA;EAEA,SAAS8I,iBAAiBA,CACxBxE,UAAuB,EACvB3T,IAAsB,EACI;EAC1B,EAAA,IAAI2T,UAAU,EAAE;EACd,IAAA,IAAIpB,OAAiC,GAAG;EACtC3a,MAAAA,KAAK,EAAE,SAAS;QAChBiU,UAAU,EAAE8H,UAAU,CAAC9H,UAAU;QACjCC,UAAU,EAAE6H,UAAU,CAAC7H,UAAU;QACjCC,WAAW,EAAE4H,UAAU,CAAC5H,WAAW;QACnCC,QAAQ,EAAE2H,UAAU,CAAC3H,QAAQ;QAC7B/E,IAAI,EAAE0M,UAAU,CAAC1M,IAAI;QACrBgF,IAAI,EAAE0H,UAAU,CAAC1H,IAAI;EACrBjM,MAAAA,IAAAA;OACD,CAAA;EACD,IAAA,OAAOuS,OAAO,CAAA;EAChB,GAAC,MAAM;EACL,IAAA,IAAIA,OAAiC,GAAG;EACtC3a,MAAAA,KAAK,EAAE,SAAS;EAChBiU,MAAAA,UAAU,EAAEhU,SAAS;EACrBiU,MAAAA,UAAU,EAAEjU,SAAS;EACrBkU,MAAAA,WAAW,EAAElU,SAAS;EACtBmU,MAAAA,QAAQ,EAAEnU,SAAS;EACnBoP,MAAAA,IAAI,EAAEpP,SAAS;EACfoU,MAAAA,IAAI,EAAEpU,SAAS;EACfmI,MAAAA,IAAAA;OACD,CAAA;EACD,IAAA,OAAOuS,OAAO,CAAA;EAChB,GAAA;EACF,CAAA;EAEA,SAASqG,oBAAoBA,CAC3BjF,UAAsB,EACtB+E,eAAyB,EACI;EAC7B,EAAA,IAAInG,OAAoC,GAAG;EACzC3a,IAAAA,KAAK,EAAE,YAAY;MACnBiU,UAAU,EAAE8H,UAAU,CAAC9H,UAAU;MACjCC,UAAU,EAAE6H,UAAU,CAAC7H,UAAU;MACjCC,WAAW,EAAE4H,UAAU,CAAC5H,WAAW;MACnCC,QAAQ,EAAE2H,UAAU,CAAC3H,QAAQ;MAC7B/E,IAAI,EAAE0M,UAAU,CAAC1M,IAAI;MACrBgF,IAAI,EAAE0H,UAAU,CAAC1H,IAAI;EACrBjM,IAAAA,IAAI,EAAE0Y,eAAe,GAAGA,eAAe,CAAC1Y,IAAI,GAAGnI,SAAAA;KAChD,CAAA;EACD,EAAA,OAAO0a,OAAO,CAAA;EAChB,CAAA;EAEA,SAAS0G,cAAcA,CAACjZ,IAAqB,EAAyB;EACpE,EAAA,IAAIuS,OAA8B,GAAG;EACnC3a,IAAAA,KAAK,EAAE,MAAM;EACbiU,IAAAA,UAAU,EAAEhU,SAAS;EACrBiU,IAAAA,UAAU,EAAEjU,SAAS;EACrBkU,IAAAA,WAAW,EAAElU,SAAS;EACtBmU,IAAAA,QAAQ,EAAEnU,SAAS;EACnBoP,IAAAA,IAAI,EAAEpP,SAAS;EACfoU,IAAAA,IAAI,EAAEpU,SAAS;EACfmI,IAAAA,IAAAA;KACD,CAAA;EACD,EAAA,OAAOuS,OAAO,CAAA;EAChB,CAAA;EAEA,SAASZ,yBAAyBA,CAChCsU,OAAe,EACfC,WAAqC,EACrC;IACA,IAAI;MACF,IAAIC,gBAAgB,GAAGF,OAAO,CAACG,cAAc,CAACC,OAAO,CACnD3Z,uBACF,CAAC,CAAA;EACD,IAAA,IAAIyZ,gBAAgB,EAAE;EACpB,MAAA,IAAIlf,IAAI,GAAGlO,IAAI,CAACqnB,KAAK,CAAC+F,gBAAgB,CAAC,CAAA;EACvC,MAAA,KAAK,IAAI,CAACjc,CAAC,EAAEpF,CAAC,CAAC,IAAIxB,MAAM,CAAC/L,OAAO,CAAC0P,IAAI,IAAI,EAAE,CAAC,EAAE;UAC7C,IAAInC,CAAC,IAAIoD,KAAK,CAACC,OAAO,CAACrD,CAAC,CAAC,EAAE;EACzBohB,UAAAA,WAAW,CAAC1e,GAAG,CAAC0C,CAAC,EAAE,IAAInM,GAAG,CAAC+G,CAAC,IAAI,EAAE,CAAC,CAAC,CAAA;EACtC,SAAA;EACF,OAAA;EACF,KAAA;KACD,CAAC,OAAO3I,CAAC,EAAE;EACV;EAAA,GAAA;EAEJ,CAAA;EAEA,SAAS0V,yBAAyBA,CAChCoU,OAAe,EACfC,WAAqC,EACrC;EACA,EAAA,IAAIA,WAAW,CAAC7b,IAAI,GAAG,CAAC,EAAE;MACxB,IAAIpD,IAA8B,GAAG,EAAE,CAAA;MACvC,KAAK,IAAI,CAACiD,CAAC,EAAEpF,CAAC,CAAC,IAAIohB,WAAW,EAAE;EAC9Bjf,MAAAA,IAAI,CAACiD,CAAC,CAAC,GAAG,CAAC,GAAGpF,CAAC,CAAC,CAAA;EAClB,KAAA;MACA,IAAI;EACFmhB,MAAAA,OAAO,CAACG,cAAc,CAACE,OAAO,CAC5B5Z,uBAAuB,EACvB3T,IAAI,CAACC,SAAS,CAACiO,IAAI,CACrB,CAAC,CAAA;OACF,CAAC,OAAO3J,KAAK,EAAE;EACdzE,MAAAA,OAAO,CACL,KAAK,EACyDyE,6DAAAA,GAAAA,KAAK,OACrE,CAAC,CAAA;EACH,KAAA;EACF,GAAA;EACF,CAAA;EACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}