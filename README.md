# برنامج الأرشفة الإلكترونية

## معلومات البرنامج
- **اسم البرنامج**: برنامج الأرشفة الإلكترونية
- **الإصدار**: 1.0.0
- **المطور**: المحاسب المبرمج علي عاجل خشان المحنة
- **التقنيات المستخدمة**: React, Electron, Node.js, Express

## وصف البرنامج
برنامج أرشفة إلكتروني متطور مصمم لإدارة وتنظيم الوثائق والسجلات بطريقة عصرية وفعالة. يتميز البرنامج بواجهة مستخدم حديثة وألوان زاهية وتأثيرات بصرية جذابة.

## الميزات الرئيسية

### 1. الواجهة الترحيبية
- تصميم عصري بألوان براقة وزاهية
- عناصر تفاعلية مع أزرار مشعة
- رسوم متحركة وتأثيرات بصرية

### 2. إدخال البيانات
- نموذج شامل لإدخال معلومات السجلات
- حقول مخصصة لكل نوع من البيانات:
  - رقم الكتاب
  - تاريخ الكتاب (يوم، شهر، سنة)
  - الموضوع
  - الفحوى أو اسم الموظف المعني
  - الدائرة المعنية (قائمة منسدلة)
  - الهامش (اختياري)

### 3. عرض التقرير
- جدول عصري يشبه جداول Excel
- عرض جميع السجلات بتنسيق منظم
- إمكانية البحث والترتيب
- تصدير البيانات بصيغة CSV

### 4. الأرشيف العام
- تصفح جميع السجلات مع التنقل بين الصفحات
- إمكانية التعديل والحذف
- بحث سريع في السجلات
- واجهة تفاعلية للإدارة

### 5. التصفية المتقدمة
- تصفية حسب التاريخ (من وإلى)
- تصفية حسب الموضوع
- تصفية حسب اسم الموظف
- تصفية حسب الدائرة المعنية
- تصفية حسب وجود الهامش

### 6. الإعدادات
- تخصيص ثيم البرنامج
- اختيار من بين عدة ألوان:
  - الأزرق الكلاسيكي
  - البنفسجي العصري
  - الفيروزي المنعش
  - الأخضر الطبيعي

## كيفية التشغيل

### الطريقة الأولى: تشغيل الملف التنفيذي
1. انتقل إلى مجلد `dist-electron`
2. شغل الملف `برنامج الأرشفة الإلكترونية Setup 1.0.0.exe`
3. اتبع تعليمات التثبيت
4. شغل البرنامج من قائمة ابدأ أو سطح المكتب

### الطريقة الثانية: تشغيل من الكود المصدري
```bash
# تثبيت التبعيات
npm install

# تشغيل البرنامج في وضع التطوير
npm run electron-dev

# أو بناء وتشغيل النسخة النهائية
npm run build
npm run electron
```

## بنية المشروع
```
├── src/                    # ملفات React
│   ├── components/         # مكونات الواجهة
│   ├── styles/            # ملفات التنسيق
│   └── main.jsx           # نقطة البداية
├── server/                # خادم Express
│   ├── routes/            # مسارات API
│   ├── database.js        # إدارة قاعدة البيانات
│   └── server.js          # الخادم الرئيسي
├── dist/                  # ملفات البناء
├── dist-electron/         # الملفات التنفيذية
└── electron.js            # إعدادات Electron
```

## قاعدة البيانات
يستخدم البرنامج قاعدة بيانات JSON محلية لتخزين السجلات، مما يضمن:
- سهولة النسخ الاحتياطي
- عدم الحاجة لخادم قاعدة بيانات منفصل
- سرعة في الأداء
- بساطة في الصيانة

## المتطلبات التقنية
- نظام التشغيل: Windows 10 أو أحدث
- ذاكرة الوصول العشوائي: 4 جيجابايت على الأقل
- مساحة القرص الصلب: 500 ميجابايت
- دقة الشاشة: 1280x720 على الأقل

## الدعم والصيانة
للحصول على الدعم التقني أو الإبلاغ عن مشاكل، يرجى التواصل مع المطور:
**المحاسب المبرمج علي عاجل خشان المحنة**

## الترخيص
هذا البرنامج مرخص تحت رخصة MIT License.

## ملاحظات مهمة
- يتم حفظ البيانات في ملف `server/archive.json`
- يُنصح بعمل نسخة احتياطية دورية من ملف البيانات
- البرنامج يدعم اللغة العربية بالكامل
- جميع التواريخ تُحفظ بالتقويم الميلادي

---
**تم تطوير هذا البرنامج بعناية فائقة لضمان أفضل تجربة مستخدم ممكنة**
