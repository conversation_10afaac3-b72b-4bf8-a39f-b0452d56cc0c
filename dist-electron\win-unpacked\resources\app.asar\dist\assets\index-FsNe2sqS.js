(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();function zm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Gd={exports:{}},Ts={},Qd={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qr=Symbol.for("react.element"),Bm=Symbol.for("react.portal"),Um=Symbol.for("react.fragment"),$m=Symbol.for("react.strict_mode"),Hm=Symbol.for("react.profiler"),Wm=Symbol.for("react.provider"),Km=Symbol.for("react.context"),Gm=Symbol.for("react.forward_ref"),Qm=Symbol.for("react.suspense"),Ym=Symbol.for("react.memo"),Xm=Symbol.for("react.lazy"),Su=Symbol.iterator;function Zm(e){return e===null||typeof e!="object"?null:(e=Su&&e[Su]||e["@@iterator"],typeof e=="function"?e:null)}var Yd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Xd=Object.assign,Zd={};function Qn(e,t,n){this.props=e,this.context=t,this.refs=Zd,this.updater=n||Yd}Qn.prototype.isReactComponent={};Qn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Qn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function qd(){}qd.prototype=Qn.prototype;function Wl(e,t,n){this.props=e,this.context=t,this.refs=Zd,this.updater=n||Yd}var Kl=Wl.prototype=new qd;Kl.constructor=Wl;Xd(Kl,Qn.prototype);Kl.isPureReactComponent=!0;var ku=Array.isArray,Jd=Object.prototype.hasOwnProperty,Gl={current:null},ef={key:!0,ref:!0,__self:!0,__source:!0};function tf(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)Jd.call(t,r)&&!ef.hasOwnProperty(r)&&(i[r]=t[r]);var l=arguments.length-2;if(l===1)i.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];i.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)i[r]===void 0&&(i[r]=l[r]);return{$$typeof:qr,type:e,key:s,ref:o,props:i,_owner:Gl.current}}function qm(e,t){return{$$typeof:qr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ql(e){return typeof e=="object"&&e!==null&&e.$$typeof===qr}function Jm(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Cu=/\/+/g;function Xs(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Jm(""+e.key):t.toString(36)}function Li(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case qr:case Bm:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+Xs(o,0):r,ku(i)?(n="",e!=null&&(n=e.replace(Cu,"$&/")+"/"),Li(i,t,n,"",function(u){return u})):i!=null&&(Ql(i)&&(i=qm(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(Cu,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",ku(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+Xs(s,l);o+=Li(s,t,n,a,i)}else if(a=Zm(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+Xs(s,l++),o+=Li(s,t,n,a,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function fi(e,t,n){if(e==null)return e;var r=[],i=0;return Li(e,r,"","",function(s){return t.call(n,s,i++)}),r}function ey(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Se={current:null},Ri={transition:null},ty={ReactCurrentDispatcher:Se,ReactCurrentBatchConfig:Ri,ReactCurrentOwner:Gl};function nf(){throw Error("act(...) is not supported in production builds of React.")}F.Children={map:fi,forEach:function(e,t,n){fi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return fi(e,function(){t++}),t},toArray:function(e){return fi(e,function(t){return t})||[]},only:function(e){if(!Ql(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};F.Component=Qn;F.Fragment=Um;F.Profiler=Hm;F.PureComponent=Wl;F.StrictMode=$m;F.Suspense=Qm;F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ty;F.act=nf;F.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Xd({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=Gl.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)Jd.call(t,a)&&!ef.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:qr,type:e.type,key:i,ref:s,props:r,_owner:o}};F.createContext=function(e){return e={$$typeof:Km,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Wm,_context:e},e.Consumer=e};F.createElement=tf;F.createFactory=function(e){var t=tf.bind(null,e);return t.type=e,t};F.createRef=function(){return{current:null}};F.forwardRef=function(e){return{$$typeof:Gm,render:e}};F.isValidElement=Ql;F.lazy=function(e){return{$$typeof:Xm,_payload:{_status:-1,_result:e},_init:ey}};F.memo=function(e,t){return{$$typeof:Ym,type:e,compare:t===void 0?null:t}};F.startTransition=function(e){var t=Ri.transition;Ri.transition={};try{e()}finally{Ri.transition=t}};F.unstable_act=nf;F.useCallback=function(e,t){return Se.current.useCallback(e,t)};F.useContext=function(e){return Se.current.useContext(e)};F.useDebugValue=function(){};F.useDeferredValue=function(e){return Se.current.useDeferredValue(e)};F.useEffect=function(e,t){return Se.current.useEffect(e,t)};F.useId=function(){return Se.current.useId()};F.useImperativeHandle=function(e,t,n){return Se.current.useImperativeHandle(e,t,n)};F.useInsertionEffect=function(e,t){return Se.current.useInsertionEffect(e,t)};F.useLayoutEffect=function(e,t){return Se.current.useLayoutEffect(e,t)};F.useMemo=function(e,t){return Se.current.useMemo(e,t)};F.useReducer=function(e,t,n){return Se.current.useReducer(e,t,n)};F.useRef=function(e){return Se.current.useRef(e)};F.useState=function(e){return Se.current.useState(e)};F.useSyncExternalStore=function(e,t,n){return Se.current.useSyncExternalStore(e,t,n)};F.useTransition=function(){return Se.current.useTransition()};F.version="18.3.1";Qd.exports=F;var P=Qd.exports;const ny=zm(P);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ry=P,iy=Symbol.for("react.element"),sy=Symbol.for("react.fragment"),oy=Object.prototype.hasOwnProperty,ly=ry.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ay={key:!0,ref:!0,__self:!0,__source:!0};function rf(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)oy.call(t,r)&&!ay.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:iy,type:e,key:s,ref:o,props:i,_owner:ly.current}}Ts.Fragment=sy;Ts.jsx=rf;Ts.jsxs=rf;Gd.exports=Ts;var c=Gd.exports,Vo={},sf={exports:{}},Re={},of={exports:{}},lf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(E,L){var R=E.length;E.push(L);e:for(;0<R;){var q=R-1>>>1,oe=E[q];if(0<i(oe,L))E[q]=L,E[R]=oe,R=q;else break e}}function n(E){return E.length===0?null:E[0]}function r(E){if(E.length===0)return null;var L=E[0],R=E.pop();if(R!==L){E[0]=R;e:for(var q=0,oe=E.length,ci=oe>>>1;q<ci;){var Ht=2*(q+1)-1,Ys=E[Ht],Wt=Ht+1,di=E[Wt];if(0>i(Ys,R))Wt<oe&&0>i(di,Ys)?(E[q]=di,E[Wt]=R,q=Wt):(E[q]=Ys,E[Ht]=R,q=Ht);else if(Wt<oe&&0>i(di,R))E[q]=di,E[Wt]=R,q=Wt;else break e}}return L}function i(E,L){var R=E.sortIndex-L.sortIndex;return R!==0?R:E.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,l=o.now();e.unstable_now=function(){return o.now()-l}}var a=[],u=[],d=1,f=null,h=3,y=!1,v=!1,x=!1,S=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(E){for(var L=n(u);L!==null;){if(L.callback===null)r(u);else if(L.startTime<=E)r(u),L.sortIndex=L.expirationTime,t(a,L);else break;L=n(u)}}function w(E){if(x=!1,g(E),!v)if(n(a)!==null)v=!0,ui(k);else{var L=n(u);L!==null&&re(w,L.startTime-E)}}function k(E,L){v=!1,x&&(x=!1,m(C),C=-1),y=!0;var R=h;try{for(g(L),f=n(a);f!==null&&(!(f.expirationTime>L)||E&&!te());){var q=f.callback;if(typeof q=="function"){f.callback=null,h=f.priorityLevel;var oe=q(f.expirationTime<=L);L=e.unstable_now(),typeof oe=="function"?f.callback=oe:f===n(a)&&r(a),g(L)}else r(a);f=n(a)}if(f!==null)var ci=!0;else{var Ht=n(u);Ht!==null&&re(w,Ht.startTime-L),ci=!1}return ci}finally{f=null,h=R,y=!1}}var j=!1,N=null,C=-1,V=5,D=-1;function te(){return!(e.unstable_now()-D<V)}function qe(){if(N!==null){var E=e.unstable_now();D=E;var L=!0;try{L=N(!0,E)}finally{L?b():(j=!1,N=null)}}else j=!1}var b;if(typeof p=="function")b=function(){p(qe)};else if(typeof MessageChannel<"u"){var ot=new MessageChannel,ai=ot.port2;ot.port1.onmessage=qe,b=function(){ai.postMessage(null)}}else b=function(){S(qe,0)};function ui(E){N=E,j||(j=!0,b())}function re(E,L){C=S(function(){E(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(E){E.callback=null},e.unstable_continueExecution=function(){v||y||(v=!0,ui(k))},e.unstable_forceFrameRate=function(E){0>E||125<E?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):V=0<E?Math.floor(1e3/E):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(E){switch(h){case 1:case 2:case 3:var L=3;break;default:L=h}var R=h;h=L;try{return E()}finally{h=R}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(E,L){switch(E){case 1:case 2:case 3:case 4:case 5:break;default:E=3}var R=h;h=E;try{return L()}finally{h=R}},e.unstable_scheduleCallback=function(E,L,R){var q=e.unstable_now();switch(typeof R=="object"&&R!==null?(R=R.delay,R=typeof R=="number"&&0<R?q+R:q):R=q,E){case 1:var oe=-1;break;case 2:oe=250;break;case 5:oe=**********;break;case 4:oe=1e4;break;default:oe=5e3}return oe=R+oe,E={id:d++,callback:L,priorityLevel:E,startTime:R,expirationTime:oe,sortIndex:-1},R>q?(E.sortIndex=R,t(u,E),n(a)===null&&E===n(u)&&(x?(m(C),C=-1):x=!0,re(w,R-q))):(E.sortIndex=oe,t(a,E),v||y||(v=!0,ui(k))),E},e.unstable_shouldYield=te,e.unstable_wrapCallback=function(E){var L=h;return function(){var R=h;h=L;try{return E.apply(this,arguments)}finally{h=R}}}})(lf);of.exports=lf;var uy=of.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cy=P,Ae=uy;function T(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var af=new Set,Mr={};function cn(e,t){Fn(e,t),Fn(e+"Capture",t)}function Fn(e,t){for(Mr[e]=t,e=0;e<t.length;e++)af.add(t[e])}var pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),bo=Object.prototype.hasOwnProperty,dy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ju={},Tu={};function fy(e){return bo.call(Tu,e)?!0:bo.call(ju,e)?!1:dy.test(e)?Tu[e]=!0:(ju[e]=!0,!1)}function hy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function py(e,t,n,r){if(t===null||typeof t>"u"||hy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ke(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new ke(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new ke(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new ke(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new ke(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new ke(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new ke(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new ke(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new ke(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new ke(e,5,!1,e.toLowerCase(),null,!1,!1)});var Yl=/[\-:]([a-z])/g;function Xl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Yl,Xl);fe[t]=new ke(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Yl,Xl);fe[t]=new ke(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Yl,Xl);fe[t]=new ke(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new ke(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new ke("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new ke(e,1,!1,e.toLowerCase(),null,!0,!0)});function Zl(e,t,n,r){var i=fe.hasOwnProperty(t)?fe[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(py(t,n,i,r)&&(n=null),r||i===null?fy(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var xt=cy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,hi=Symbol.for("react.element"),pn=Symbol.for("react.portal"),mn=Symbol.for("react.fragment"),ql=Symbol.for("react.strict_mode"),Fo=Symbol.for("react.profiler"),uf=Symbol.for("react.provider"),cf=Symbol.for("react.context"),Jl=Symbol.for("react.forward_ref"),Oo=Symbol.for("react.suspense"),Io=Symbol.for("react.suspense_list"),ea=Symbol.for("react.memo"),kt=Symbol.for("react.lazy"),df=Symbol.for("react.offscreen"),Pu=Symbol.iterator;function Jn(e){return e===null||typeof e!="object"?null:(e=Pu&&e[Pu]||e["@@iterator"],typeof e=="function"?e:null)}var Y=Object.assign,Zs;function ar(e){if(Zs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Zs=t&&t[1]||""}return`
`+Zs+e}var qs=!1;function Js(e,t){if(!e||qs)return"";qs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,l=s.length-1;1<=o&&0<=l&&i[o]!==s[l];)l--;for(;1<=o&&0<=l;o--,l--)if(i[o]!==s[l]){if(o!==1||l!==1)do if(o--,l--,0>l||i[o]!==s[l]){var a=`
`+i[o].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=o&&0<=l);break}}}finally{qs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ar(e):""}function my(e){switch(e.tag){case 5:return ar(e.type);case 16:return ar("Lazy");case 13:return ar("Suspense");case 19:return ar("SuspenseList");case 0:case 2:case 15:return e=Js(e.type,!1),e;case 11:return e=Js(e.type.render,!1),e;case 1:return e=Js(e.type,!0),e;default:return""}}function zo(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case mn:return"Fragment";case pn:return"Portal";case Fo:return"Profiler";case ql:return"StrictMode";case Oo:return"Suspense";case Io:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case cf:return(e.displayName||"Context")+".Consumer";case uf:return(e._context.displayName||"Context")+".Provider";case Jl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ea:return t=e.displayName||null,t!==null?t:zo(e.type)||"Memo";case kt:t=e._payload,e=e._init;try{return zo(e(t))}catch{}}return null}function yy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return zo(t);case 8:return t===ql?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function bt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ff(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function gy(e){var t=ff(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function pi(e){e._valueTracker||(e._valueTracker=gy(e))}function hf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ff(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Qi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Bo(e,t){var n=t.checked;return Y({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Nu(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=bt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function pf(e,t){t=t.checked,t!=null&&Zl(e,"checked",t,!1)}function Uo(e,t){pf(e,t);var n=bt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?$o(e,t.type,n):t.hasOwnProperty("defaultValue")&&$o(e,t.type,bt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Eu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function $o(e,t,n){(t!=="number"||Qi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var ur=Array.isArray;function Dn(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+bt(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Ho(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(T(91));return Y({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Mu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(T(92));if(ur(n)){if(1<n.length)throw Error(T(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:bt(n)}}function mf(e,t){var n=bt(t.value),r=bt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function _u(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function yf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Wo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?yf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var mi,gf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(mi=mi||document.createElement("div"),mi.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=mi.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function _r(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var mr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},vy=["Webkit","ms","Moz","O"];Object.keys(mr).forEach(function(e){vy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),mr[t]=mr[e]})});function vf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||mr.hasOwnProperty(e)&&mr[e]?(""+t).trim():t+"px"}function xf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=vf(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var xy=Y({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ko(e,t){if(t){if(xy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(T(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(T(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(T(61))}if(t.style!=null&&typeof t.style!="object")throw Error(T(62))}}function Go(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Qo=null;function ta(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Yo=null,An=null,Ln=null;function Du(e){if(e=ti(e)){if(typeof Yo!="function")throw Error(T(280));var t=e.stateNode;t&&(t=_s(t),Yo(e.stateNode,e.type,t))}}function wf(e){An?Ln?Ln.push(e):Ln=[e]:An=e}function Sf(){if(An){var e=An,t=Ln;if(Ln=An=null,Du(e),t)for(e=0;e<t.length;e++)Du(t[e])}}function kf(e,t){return e(t)}function Cf(){}var eo=!1;function jf(e,t,n){if(eo)return e(t,n);eo=!0;try{return kf(e,t,n)}finally{eo=!1,(An!==null||Ln!==null)&&(Cf(),Sf())}}function Dr(e,t){var n=e.stateNode;if(n===null)return null;var r=_s(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(T(231,t,typeof n));return n}var Xo=!1;if(pt)try{var er={};Object.defineProperty(er,"passive",{get:function(){Xo=!0}}),window.addEventListener("test",er,er),window.removeEventListener("test",er,er)}catch{Xo=!1}function wy(e,t,n,r,i,s,o,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(d){this.onError(d)}}var yr=!1,Yi=null,Xi=!1,Zo=null,Sy={onError:function(e){yr=!0,Yi=e}};function ky(e,t,n,r,i,s,o,l,a){yr=!1,Yi=null,wy.apply(Sy,arguments)}function Cy(e,t,n,r,i,s,o,l,a){if(ky.apply(this,arguments),yr){if(yr){var u=Yi;yr=!1,Yi=null}else throw Error(T(198));Xi||(Xi=!0,Zo=u)}}function dn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Tf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Au(e){if(dn(e)!==e)throw Error(T(188))}function jy(e){var t=e.alternate;if(!t){if(t=dn(e),t===null)throw Error(T(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return Au(i),e;if(s===r)return Au(i),t;s=s.sibling}throw Error(T(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,l=i.child;l;){if(l===n){o=!0,n=i,r=s;break}if(l===r){o=!0,r=i,n=s;break}l=l.sibling}if(!o){for(l=s.child;l;){if(l===n){o=!0,n=s,r=i;break}if(l===r){o=!0,r=s,n=i;break}l=l.sibling}if(!o)throw Error(T(189))}}if(n.alternate!==r)throw Error(T(190))}if(n.tag!==3)throw Error(T(188));return n.stateNode.current===n?e:t}function Pf(e){return e=jy(e),e!==null?Nf(e):null}function Nf(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Nf(e);if(t!==null)return t;e=e.sibling}return null}var Ef=Ae.unstable_scheduleCallback,Lu=Ae.unstable_cancelCallback,Ty=Ae.unstable_shouldYield,Py=Ae.unstable_requestPaint,ee=Ae.unstable_now,Ny=Ae.unstable_getCurrentPriorityLevel,na=Ae.unstable_ImmediatePriority,Mf=Ae.unstable_UserBlockingPriority,Zi=Ae.unstable_NormalPriority,Ey=Ae.unstable_LowPriority,_f=Ae.unstable_IdlePriority,Ps=null,nt=null;function My(e){if(nt&&typeof nt.onCommitFiberRoot=="function")try{nt.onCommitFiberRoot(Ps,e,void 0,(e.current.flags&128)===128)}catch{}}var Ye=Math.clz32?Math.clz32:Ay,_y=Math.log,Dy=Math.LN2;function Ay(e){return e>>>=0,e===0?32:31-(_y(e)/Dy|0)|0}var yi=64,gi=4194304;function cr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function qi(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var l=o&~i;l!==0?r=cr(l):(s&=o,s!==0&&(r=cr(s)))}else o=n&~i,o!==0?r=cr(o):s!==0&&(r=cr(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Ye(t),i=1<<n,r|=e[n],t&=~i;return r}function Ly(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ry(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-Ye(s),l=1<<o,a=i[o];a===-1?(!(l&n)||l&r)&&(i[o]=Ly(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function qo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Df(){var e=yi;return yi<<=1,!(yi&4194240)&&(yi=64),e}function to(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Jr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Ye(t),e[t]=n}function Vy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Ye(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function ra(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Ye(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var I=0;function Af(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Lf,ia,Rf,Vf,bf,Jo=!1,vi=[],Et=null,Mt=null,_t=null,Ar=new Map,Lr=new Map,jt=[],by="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ru(e,t){switch(e){case"focusin":case"focusout":Et=null;break;case"dragenter":case"dragleave":Mt=null;break;case"mouseover":case"mouseout":_t=null;break;case"pointerover":case"pointerout":Ar.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Lr.delete(t.pointerId)}}function tr(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=ti(t),t!==null&&ia(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function Fy(e,t,n,r,i){switch(t){case"focusin":return Et=tr(Et,e,t,n,r,i),!0;case"dragenter":return Mt=tr(Mt,e,t,n,r,i),!0;case"mouseover":return _t=tr(_t,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Ar.set(s,tr(Ar.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Lr.set(s,tr(Lr.get(s)||null,e,t,n,r,i)),!0}return!1}function Ff(e){var t=Zt(e.target);if(t!==null){var n=dn(t);if(n!==null){if(t=n.tag,t===13){if(t=Tf(n),t!==null){e.blockedOn=t,bf(e.priority,function(){Rf(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Vi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=el(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Qo=r,n.target.dispatchEvent(r),Qo=null}else return t=ti(n),t!==null&&ia(t),e.blockedOn=n,!1;t.shift()}return!0}function Vu(e,t,n){Vi(e)&&n.delete(t)}function Oy(){Jo=!1,Et!==null&&Vi(Et)&&(Et=null),Mt!==null&&Vi(Mt)&&(Mt=null),_t!==null&&Vi(_t)&&(_t=null),Ar.forEach(Vu),Lr.forEach(Vu)}function nr(e,t){e.blockedOn===t&&(e.blockedOn=null,Jo||(Jo=!0,Ae.unstable_scheduleCallback(Ae.unstable_NormalPriority,Oy)))}function Rr(e){function t(i){return nr(i,e)}if(0<vi.length){nr(vi[0],e);for(var n=1;n<vi.length;n++){var r=vi[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Et!==null&&nr(Et,e),Mt!==null&&nr(Mt,e),_t!==null&&nr(_t,e),Ar.forEach(t),Lr.forEach(t),n=0;n<jt.length;n++)r=jt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<jt.length&&(n=jt[0],n.blockedOn===null);)Ff(n),n.blockedOn===null&&jt.shift()}var Rn=xt.ReactCurrentBatchConfig,Ji=!0;function Iy(e,t,n,r){var i=I,s=Rn.transition;Rn.transition=null;try{I=1,sa(e,t,n,r)}finally{I=i,Rn.transition=s}}function zy(e,t,n,r){var i=I,s=Rn.transition;Rn.transition=null;try{I=4,sa(e,t,n,r)}finally{I=i,Rn.transition=s}}function sa(e,t,n,r){if(Ji){var i=el(e,t,n,r);if(i===null)fo(e,t,r,es,n),Ru(e,r);else if(Fy(i,e,t,n,r))r.stopPropagation();else if(Ru(e,r),t&4&&-1<by.indexOf(e)){for(;i!==null;){var s=ti(i);if(s!==null&&Lf(s),s=el(e,t,n,r),s===null&&fo(e,t,r,es,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else fo(e,t,r,null,n)}}var es=null;function el(e,t,n,r){if(es=null,e=ta(r),e=Zt(e),e!==null)if(t=dn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Tf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return es=e,null}function Of(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ny()){case na:return 1;case Mf:return 4;case Zi:case Ey:return 16;case _f:return 536870912;default:return 16}default:return 16}}var Pt=null,oa=null,bi=null;function If(){if(bi)return bi;var e,t=oa,n=t.length,r,i="value"in Pt?Pt.value:Pt.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return bi=i.slice(e,1<r?1-r:void 0)}function Fi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function xi(){return!0}function bu(){return!1}function Ve(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?xi:bu,this.isPropagationStopped=bu,this}return Y(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=xi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=xi)},persist:function(){},isPersistent:xi}),t}var Yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},la=Ve(Yn),ei=Y({},Yn,{view:0,detail:0}),By=Ve(ei),no,ro,rr,Ns=Y({},ei,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:aa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==rr&&(rr&&e.type==="mousemove"?(no=e.screenX-rr.screenX,ro=e.screenY-rr.screenY):ro=no=0,rr=e),no)},movementY:function(e){return"movementY"in e?e.movementY:ro}}),Fu=Ve(Ns),Uy=Y({},Ns,{dataTransfer:0}),$y=Ve(Uy),Hy=Y({},ei,{relatedTarget:0}),io=Ve(Hy),Wy=Y({},Yn,{animationName:0,elapsedTime:0,pseudoElement:0}),Ky=Ve(Wy),Gy=Y({},Yn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Qy=Ve(Gy),Yy=Y({},Yn,{data:0}),Ou=Ve(Yy),Xy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Zy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},qy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Jy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=qy[e])?!!t[e]:!1}function aa(){return Jy}var eg=Y({},ei,{key:function(e){if(e.key){var t=Xy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Fi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Zy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:aa,charCode:function(e){return e.type==="keypress"?Fi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Fi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),tg=Ve(eg),ng=Y({},Ns,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Iu=Ve(ng),rg=Y({},ei,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:aa}),ig=Ve(rg),sg=Y({},Yn,{propertyName:0,elapsedTime:0,pseudoElement:0}),og=Ve(sg),lg=Y({},Ns,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),ag=Ve(lg),ug=[9,13,27,32],ua=pt&&"CompositionEvent"in window,gr=null;pt&&"documentMode"in document&&(gr=document.documentMode);var cg=pt&&"TextEvent"in window&&!gr,zf=pt&&(!ua||gr&&8<gr&&11>=gr),zu=" ",Bu=!1;function Bf(e,t){switch(e){case"keyup":return ug.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Uf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yn=!1;function dg(e,t){switch(e){case"compositionend":return Uf(t);case"keypress":return t.which!==32?null:(Bu=!0,zu);case"textInput":return e=t.data,e===zu&&Bu?null:e;default:return null}}function fg(e,t){if(yn)return e==="compositionend"||!ua&&Bf(e,t)?(e=If(),bi=oa=Pt=null,yn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return zf&&t.locale!=="ko"?null:t.data;default:return null}}var hg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Uu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!hg[e.type]:t==="textarea"}function $f(e,t,n,r){wf(r),t=ts(t,"onChange"),0<t.length&&(n=new la("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var vr=null,Vr=null;function pg(e){eh(e,0)}function Es(e){var t=xn(e);if(hf(t))return e}function mg(e,t){if(e==="change")return t}var Hf=!1;if(pt){var so;if(pt){var oo="oninput"in document;if(!oo){var $u=document.createElement("div");$u.setAttribute("oninput","return;"),oo=typeof $u.oninput=="function"}so=oo}else so=!1;Hf=so&&(!document.documentMode||9<document.documentMode)}function Hu(){vr&&(vr.detachEvent("onpropertychange",Wf),Vr=vr=null)}function Wf(e){if(e.propertyName==="value"&&Es(Vr)){var t=[];$f(t,Vr,e,ta(e)),jf(pg,t)}}function yg(e,t,n){e==="focusin"?(Hu(),vr=t,Vr=n,vr.attachEvent("onpropertychange",Wf)):e==="focusout"&&Hu()}function gg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Es(Vr)}function vg(e,t){if(e==="click")return Es(t)}function xg(e,t){if(e==="input"||e==="change")return Es(t)}function wg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ze=typeof Object.is=="function"?Object.is:wg;function br(e,t){if(Ze(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!bo.call(t,i)||!Ze(e[i],t[i]))return!1}return!0}function Wu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ku(e,t){var n=Wu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Wu(n)}}function Kf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Kf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Gf(){for(var e=window,t=Qi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Qi(e.document)}return t}function ca(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Sg(e){var t=Gf(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Kf(n.ownerDocument.documentElement,n)){if(r!==null&&ca(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=Ku(n,s);var o=Ku(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var kg=pt&&"documentMode"in document&&11>=document.documentMode,gn=null,tl=null,xr=null,nl=!1;function Gu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;nl||gn==null||gn!==Qi(r)||(r=gn,"selectionStart"in r&&ca(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),xr&&br(xr,r)||(xr=r,r=ts(tl,"onSelect"),0<r.length&&(t=new la("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gn)))}function wi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var vn={animationend:wi("Animation","AnimationEnd"),animationiteration:wi("Animation","AnimationIteration"),animationstart:wi("Animation","AnimationStart"),transitionend:wi("Transition","TransitionEnd")},lo={},Qf={};pt&&(Qf=document.createElement("div").style,"AnimationEvent"in window||(delete vn.animationend.animation,delete vn.animationiteration.animation,delete vn.animationstart.animation),"TransitionEvent"in window||delete vn.transitionend.transition);function Ms(e){if(lo[e])return lo[e];if(!vn[e])return e;var t=vn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Qf)return lo[e]=t[n];return e}var Yf=Ms("animationend"),Xf=Ms("animationiteration"),Zf=Ms("animationstart"),qf=Ms("transitionend"),Jf=new Map,Qu="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function zt(e,t){Jf.set(e,t),cn(t,[e])}for(var ao=0;ao<Qu.length;ao++){var uo=Qu[ao],Cg=uo.toLowerCase(),jg=uo[0].toUpperCase()+uo.slice(1);zt(Cg,"on"+jg)}zt(Yf,"onAnimationEnd");zt(Xf,"onAnimationIteration");zt(Zf,"onAnimationStart");zt("dblclick","onDoubleClick");zt("focusin","onFocus");zt("focusout","onBlur");zt(qf,"onTransitionEnd");Fn("onMouseEnter",["mouseout","mouseover"]);Fn("onMouseLeave",["mouseout","mouseover"]);Fn("onPointerEnter",["pointerout","pointerover"]);Fn("onPointerLeave",["pointerout","pointerover"]);cn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));cn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));cn("onBeforeInput",["compositionend","keypress","textInput","paste"]);cn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));cn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));cn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Tg=new Set("cancel close invalid load scroll toggle".split(" ").concat(dr));function Yu(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Cy(r,t,void 0,e),e.currentTarget=null}function eh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var l=r[o],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==s&&i.isPropagationStopped())break e;Yu(i,l,u),s=a}else for(o=0;o<r.length;o++){if(l=r[o],a=l.instance,u=l.currentTarget,l=l.listener,a!==s&&i.isPropagationStopped())break e;Yu(i,l,u),s=a}}}if(Xi)throw e=Zo,Xi=!1,Zo=null,e}function U(e,t){var n=t[ll];n===void 0&&(n=t[ll]=new Set);var r=e+"__bubble";n.has(r)||(th(t,e,2,!1),n.add(r))}function co(e,t,n){var r=0;t&&(r|=4),th(n,e,r,t)}var Si="_reactListening"+Math.random().toString(36).slice(2);function Fr(e){if(!e[Si]){e[Si]=!0,af.forEach(function(n){n!=="selectionchange"&&(Tg.has(n)||co(n,!1,e),co(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Si]||(t[Si]=!0,co("selectionchange",!1,t))}}function th(e,t,n,r){switch(Of(t)){case 1:var i=Iy;break;case 4:i=zy;break;default:i=sa}n=i.bind(null,t,n,e),i=void 0,!Xo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function fo(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var l=r.stateNode.containerInfo;if(l===i||l.nodeType===8&&l.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===i||a.nodeType===8&&a.parentNode===i))return;o=o.return}for(;l!==null;){if(o=Zt(l),o===null)return;if(a=o.tag,a===5||a===6){r=s=o;continue e}l=l.parentNode}}r=r.return}jf(function(){var u=s,d=ta(n),f=[];e:{var h=Jf.get(e);if(h!==void 0){var y=la,v=e;switch(e){case"keypress":if(Fi(n)===0)break e;case"keydown":case"keyup":y=tg;break;case"focusin":v="focus",y=io;break;case"focusout":v="blur",y=io;break;case"beforeblur":case"afterblur":y=io;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=Fu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=$y;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=ig;break;case Yf:case Xf:case Zf:y=Ky;break;case qf:y=og;break;case"scroll":y=By;break;case"wheel":y=ag;break;case"copy":case"cut":case"paste":y=Qy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=Iu}var x=(t&4)!==0,S=!x&&e==="scroll",m=x?h!==null?h+"Capture":null:h;x=[];for(var p=u,g;p!==null;){g=p;var w=g.stateNode;if(g.tag===5&&w!==null&&(g=w,m!==null&&(w=Dr(p,m),w!=null&&x.push(Or(p,w,g)))),S)break;p=p.return}0<x.length&&(h=new y(h,v,null,n,d),f.push({event:h,listeners:x}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",h&&n!==Qo&&(v=n.relatedTarget||n.fromElement)&&(Zt(v)||v[mt]))break e;if((y||h)&&(h=d.window===d?d:(h=d.ownerDocument)?h.defaultView||h.parentWindow:window,y?(v=n.relatedTarget||n.toElement,y=u,v=v?Zt(v):null,v!==null&&(S=dn(v),v!==S||v.tag!==5&&v.tag!==6)&&(v=null)):(y=null,v=u),y!==v)){if(x=Fu,w="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(x=Iu,w="onPointerLeave",m="onPointerEnter",p="pointer"),S=y==null?h:xn(y),g=v==null?h:xn(v),h=new x(w,p+"leave",y,n,d),h.target=S,h.relatedTarget=g,w=null,Zt(d)===u&&(x=new x(m,p+"enter",v,n,d),x.target=g,x.relatedTarget=S,w=x),S=w,y&&v)t:{for(x=y,m=v,p=0,g=x;g;g=hn(g))p++;for(g=0,w=m;w;w=hn(w))g++;for(;0<p-g;)x=hn(x),p--;for(;0<g-p;)m=hn(m),g--;for(;p--;){if(x===m||m!==null&&x===m.alternate)break t;x=hn(x),m=hn(m)}x=null}else x=null;y!==null&&Xu(f,h,y,x,!1),v!==null&&S!==null&&Xu(f,S,v,x,!0)}}e:{if(h=u?xn(u):window,y=h.nodeName&&h.nodeName.toLowerCase(),y==="select"||y==="input"&&h.type==="file")var k=mg;else if(Uu(h))if(Hf)k=xg;else{k=gg;var j=yg}else(y=h.nodeName)&&y.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(k=vg);if(k&&(k=k(e,u))){$f(f,k,n,d);break e}j&&j(e,h,u),e==="focusout"&&(j=h._wrapperState)&&j.controlled&&h.type==="number"&&$o(h,"number",h.value)}switch(j=u?xn(u):window,e){case"focusin":(Uu(j)||j.contentEditable==="true")&&(gn=j,tl=u,xr=null);break;case"focusout":xr=tl=gn=null;break;case"mousedown":nl=!0;break;case"contextmenu":case"mouseup":case"dragend":nl=!1,Gu(f,n,d);break;case"selectionchange":if(kg)break;case"keydown":case"keyup":Gu(f,n,d)}var N;if(ua)e:{switch(e){case"compositionstart":var C="onCompositionStart";break e;case"compositionend":C="onCompositionEnd";break e;case"compositionupdate":C="onCompositionUpdate";break e}C=void 0}else yn?Bf(e,n)&&(C="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(C="onCompositionStart");C&&(zf&&n.locale!=="ko"&&(yn||C!=="onCompositionStart"?C==="onCompositionEnd"&&yn&&(N=If()):(Pt=d,oa="value"in Pt?Pt.value:Pt.textContent,yn=!0)),j=ts(u,C),0<j.length&&(C=new Ou(C,e,null,n,d),f.push({event:C,listeners:j}),N?C.data=N:(N=Uf(n),N!==null&&(C.data=N)))),(N=cg?dg(e,n):fg(e,n))&&(u=ts(u,"onBeforeInput"),0<u.length&&(d=new Ou("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:u}),d.data=N))}eh(f,t)})}function Or(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ts(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Dr(e,n),s!=null&&r.unshift(Or(e,s,i)),s=Dr(e,t),s!=null&&r.push(Or(e,s,i))),e=e.return}return r}function hn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Xu(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,i?(a=Dr(n,s),a!=null&&o.unshift(Or(n,a,l))):i||(a=Dr(n,s),a!=null&&o.push(Or(n,a,l)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Pg=/\r\n?/g,Ng=/\u0000|\uFFFD/g;function Zu(e){return(typeof e=="string"?e:""+e).replace(Pg,`
`).replace(Ng,"")}function ki(e,t,n){if(t=Zu(t),Zu(e)!==t&&n)throw Error(T(425))}function ns(){}var rl=null,il=null;function sl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ol=typeof setTimeout=="function"?setTimeout:void 0,Eg=typeof clearTimeout=="function"?clearTimeout:void 0,qu=typeof Promise=="function"?Promise:void 0,Mg=typeof queueMicrotask=="function"?queueMicrotask:typeof qu<"u"?function(e){return qu.resolve(null).then(e).catch(_g)}:ol;function _g(e){setTimeout(function(){throw e})}function ho(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),Rr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);Rr(t)}function Dt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ju(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Xn=Math.random().toString(36).slice(2),tt="__reactFiber$"+Xn,Ir="__reactProps$"+Xn,mt="__reactContainer$"+Xn,ll="__reactEvents$"+Xn,Dg="__reactListeners$"+Xn,Ag="__reactHandles$"+Xn;function Zt(e){var t=e[tt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mt]||n[tt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ju(e);e!==null;){if(n=e[tt])return n;e=Ju(e)}return t}e=n,n=e.parentNode}return null}function ti(e){return e=e[tt]||e[mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function xn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(T(33))}function _s(e){return e[Ir]||null}var al=[],wn=-1;function Bt(e){return{current:e}}function $(e){0>wn||(e.current=al[wn],al[wn]=null,wn--)}function B(e,t){wn++,al[wn]=e.current,e.current=t}var Ft={},ve=Bt(Ft),Te=Bt(!1),sn=Ft;function On(e,t){var n=e.type.contextTypes;if(!n)return Ft;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Pe(e){return e=e.childContextTypes,e!=null}function rs(){$(Te),$(ve)}function ec(e,t,n){if(ve.current!==Ft)throw Error(T(168));B(ve,t),B(Te,n)}function nh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(T(108,yy(e)||"Unknown",i));return Y({},n,r)}function is(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ft,sn=ve.current,B(ve,e),B(Te,Te.current),!0}function tc(e,t,n){var r=e.stateNode;if(!r)throw Error(T(169));n?(e=nh(e,t,sn),r.__reactInternalMemoizedMergedChildContext=e,$(Te),$(ve),B(ve,e)):$(Te),B(Te,n)}var at=null,Ds=!1,po=!1;function rh(e){at===null?at=[e]:at.push(e)}function Lg(e){Ds=!0,rh(e)}function Ut(){if(!po&&at!==null){po=!0;var e=0,t=I;try{var n=at;for(I=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}at=null,Ds=!1}catch(i){throw at!==null&&(at=at.slice(e+1)),Ef(na,Ut),i}finally{I=t,po=!1}}return null}var Sn=[],kn=0,ss=null,os=0,Oe=[],Ie=0,on=null,ut=1,ct="";function Gt(e,t){Sn[kn++]=os,Sn[kn++]=ss,ss=e,os=t}function ih(e,t,n){Oe[Ie++]=ut,Oe[Ie++]=ct,Oe[Ie++]=on,on=e;var r=ut;e=ct;var i=32-Ye(r)-1;r&=~(1<<i),n+=1;var s=32-Ye(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,ut=1<<32-Ye(t)+i|n<<i|r,ct=s+e}else ut=1<<s|n<<i|r,ct=e}function da(e){e.return!==null&&(Gt(e,1),ih(e,1,0))}function fa(e){for(;e===ss;)ss=Sn[--kn],Sn[kn]=null,os=Sn[--kn],Sn[kn]=null;for(;e===on;)on=Oe[--Ie],Oe[Ie]=null,ct=Oe[--Ie],Oe[Ie]=null,ut=Oe[--Ie],Oe[Ie]=null}var _e=null,Me=null,W=!1,Qe=null;function sh(e,t){var n=ze(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function nc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,_e=e,Me=Dt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,_e=e,Me=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=on!==null?{id:ut,overflow:ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ze(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,_e=e,Me=null,!0):!1;default:return!1}}function ul(e){return(e.mode&1)!==0&&(e.flags&128)===0}function cl(e){if(W){var t=Me;if(t){var n=t;if(!nc(e,t)){if(ul(e))throw Error(T(418));t=Dt(n.nextSibling);var r=_e;t&&nc(e,t)?sh(r,n):(e.flags=e.flags&-4097|2,W=!1,_e=e)}}else{if(ul(e))throw Error(T(418));e.flags=e.flags&-4097|2,W=!1,_e=e}}}function rc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;_e=e}function Ci(e){if(e!==_e)return!1;if(!W)return rc(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!sl(e.type,e.memoizedProps)),t&&(t=Me)){if(ul(e))throw oh(),Error(T(418));for(;t;)sh(e,t),t=Dt(t.nextSibling)}if(rc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(T(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Me=Dt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Me=null}}else Me=_e?Dt(e.stateNode.nextSibling):null;return!0}function oh(){for(var e=Me;e;)e=Dt(e.nextSibling)}function In(){Me=_e=null,W=!1}function ha(e){Qe===null?Qe=[e]:Qe.push(e)}var Rg=xt.ReactCurrentBatchConfig;function ir(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(T(309));var r=n.stateNode}if(!r)throw Error(T(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var l=i.refs;o===null?delete l[s]:l[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(T(284));if(!n._owner)throw Error(T(290,e))}return e}function ji(e,t){throw e=Object.prototype.toString.call(t),Error(T(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ic(e){var t=e._init;return t(e._payload)}function lh(e){function t(m,p){if(e){var g=m.deletions;g===null?(m.deletions=[p],m.flags|=16):g.push(p)}}function n(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function r(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function i(m,p){return m=Vt(m,p),m.index=0,m.sibling=null,m}function s(m,p,g){return m.index=g,e?(g=m.alternate,g!==null?(g=g.index,g<p?(m.flags|=2,p):g):(m.flags|=2,p)):(m.flags|=1048576,p)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function l(m,p,g,w){return p===null||p.tag!==6?(p=So(g,m.mode,w),p.return=m,p):(p=i(p,g),p.return=m,p)}function a(m,p,g,w){var k=g.type;return k===mn?d(m,p,g.props.children,w,g.key):p!==null&&(p.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===kt&&ic(k)===p.type)?(w=i(p,g.props),w.ref=ir(m,p,g),w.return=m,w):(w=Hi(g.type,g.key,g.props,null,m.mode,w),w.ref=ir(m,p,g),w.return=m,w)}function u(m,p,g,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=ko(g,m.mode,w),p.return=m,p):(p=i(p,g.children||[]),p.return=m,p)}function d(m,p,g,w,k){return p===null||p.tag!==7?(p=nn(g,m.mode,w,k),p.return=m,p):(p=i(p,g),p.return=m,p)}function f(m,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=So(""+p,m.mode,g),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case hi:return g=Hi(p.type,p.key,p.props,null,m.mode,g),g.ref=ir(m,null,p),g.return=m,g;case pn:return p=ko(p,m.mode,g),p.return=m,p;case kt:var w=p._init;return f(m,w(p._payload),g)}if(ur(p)||Jn(p))return p=nn(p,m.mode,g,null),p.return=m,p;ji(m,p)}return null}function h(m,p,g,w){var k=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return k!==null?null:l(m,p,""+g,w);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case hi:return g.key===k?a(m,p,g,w):null;case pn:return g.key===k?u(m,p,g,w):null;case kt:return k=g._init,h(m,p,k(g._payload),w)}if(ur(g)||Jn(g))return k!==null?null:d(m,p,g,w,null);ji(m,g)}return null}function y(m,p,g,w,k){if(typeof w=="string"&&w!==""||typeof w=="number")return m=m.get(g)||null,l(p,m,""+w,k);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case hi:return m=m.get(w.key===null?g:w.key)||null,a(p,m,w,k);case pn:return m=m.get(w.key===null?g:w.key)||null,u(p,m,w,k);case kt:var j=w._init;return y(m,p,g,j(w._payload),k)}if(ur(w)||Jn(w))return m=m.get(g)||null,d(p,m,w,k,null);ji(p,w)}return null}function v(m,p,g,w){for(var k=null,j=null,N=p,C=p=0,V=null;N!==null&&C<g.length;C++){N.index>C?(V=N,N=null):V=N.sibling;var D=h(m,N,g[C],w);if(D===null){N===null&&(N=V);break}e&&N&&D.alternate===null&&t(m,N),p=s(D,p,C),j===null?k=D:j.sibling=D,j=D,N=V}if(C===g.length)return n(m,N),W&&Gt(m,C),k;if(N===null){for(;C<g.length;C++)N=f(m,g[C],w),N!==null&&(p=s(N,p,C),j===null?k=N:j.sibling=N,j=N);return W&&Gt(m,C),k}for(N=r(m,N);C<g.length;C++)V=y(N,m,C,g[C],w),V!==null&&(e&&V.alternate!==null&&N.delete(V.key===null?C:V.key),p=s(V,p,C),j===null?k=V:j.sibling=V,j=V);return e&&N.forEach(function(te){return t(m,te)}),W&&Gt(m,C),k}function x(m,p,g,w){var k=Jn(g);if(typeof k!="function")throw Error(T(150));if(g=k.call(g),g==null)throw Error(T(151));for(var j=k=null,N=p,C=p=0,V=null,D=g.next();N!==null&&!D.done;C++,D=g.next()){N.index>C?(V=N,N=null):V=N.sibling;var te=h(m,N,D.value,w);if(te===null){N===null&&(N=V);break}e&&N&&te.alternate===null&&t(m,N),p=s(te,p,C),j===null?k=te:j.sibling=te,j=te,N=V}if(D.done)return n(m,N),W&&Gt(m,C),k;if(N===null){for(;!D.done;C++,D=g.next())D=f(m,D.value,w),D!==null&&(p=s(D,p,C),j===null?k=D:j.sibling=D,j=D);return W&&Gt(m,C),k}for(N=r(m,N);!D.done;C++,D=g.next())D=y(N,m,C,D.value,w),D!==null&&(e&&D.alternate!==null&&N.delete(D.key===null?C:D.key),p=s(D,p,C),j===null?k=D:j.sibling=D,j=D);return e&&N.forEach(function(qe){return t(m,qe)}),W&&Gt(m,C),k}function S(m,p,g,w){if(typeof g=="object"&&g!==null&&g.type===mn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case hi:e:{for(var k=g.key,j=p;j!==null;){if(j.key===k){if(k=g.type,k===mn){if(j.tag===7){n(m,j.sibling),p=i(j,g.props.children),p.return=m,m=p;break e}}else if(j.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===kt&&ic(k)===j.type){n(m,j.sibling),p=i(j,g.props),p.ref=ir(m,j,g),p.return=m,m=p;break e}n(m,j);break}else t(m,j);j=j.sibling}g.type===mn?(p=nn(g.props.children,m.mode,w,g.key),p.return=m,m=p):(w=Hi(g.type,g.key,g.props,null,m.mode,w),w.ref=ir(m,p,g),w.return=m,m=w)}return o(m);case pn:e:{for(j=g.key;p!==null;){if(p.key===j)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(m,p.sibling),p=i(p,g.children||[]),p.return=m,m=p;break e}else{n(m,p);break}else t(m,p);p=p.sibling}p=ko(g,m.mode,w),p.return=m,m=p}return o(m);case kt:return j=g._init,S(m,p,j(g._payload),w)}if(ur(g))return v(m,p,g,w);if(Jn(g))return x(m,p,g,w);ji(m,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(m,p.sibling),p=i(p,g),p.return=m,m=p):(n(m,p),p=So(g,m.mode,w),p.return=m,m=p),o(m)):n(m,p)}return S}var zn=lh(!0),ah=lh(!1),ls=Bt(null),as=null,Cn=null,pa=null;function ma(){pa=Cn=as=null}function ya(e){var t=ls.current;$(ls),e._currentValue=t}function dl(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Vn(e,t){as=e,pa=Cn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(je=!0),e.firstContext=null)}function Ue(e){var t=e._currentValue;if(pa!==e)if(e={context:e,memoizedValue:t,next:null},Cn===null){if(as===null)throw Error(T(308));Cn=e,as.dependencies={lanes:0,firstContext:e}}else Cn=Cn.next=e;return t}var qt=null;function ga(e){qt===null?qt=[e]:qt.push(e)}function uh(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,ga(t)):(n.next=i.next,i.next=n),t.interleaved=n,yt(e,r)}function yt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ct=!1;function va(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function ch(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function dt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function At(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,O&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,yt(e,n)}return i=r.interleaved,i===null?(t.next=t,ga(r)):(t.next=i.next,i.next=t),r.interleaved=t,yt(e,n)}function Oi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ra(e,n)}}function sc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function us(e,t,n,r){var i=e.updateQueue;Ct=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,l=i.shared.pending;if(l!==null){i.shared.pending=null;var a=l,u=a.next;a.next=null,o===null?s=u:o.next=u,o=a;var d=e.alternate;d!==null&&(d=d.updateQueue,l=d.lastBaseUpdate,l!==o&&(l===null?d.firstBaseUpdate=u:l.next=u,d.lastBaseUpdate=a))}if(s!==null){var f=i.baseState;o=0,d=u=a=null,l=s;do{var h=l.lane,y=l.eventTime;if((r&h)===h){d!==null&&(d=d.next={eventTime:y,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var v=e,x=l;switch(h=t,y=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){f=v.call(y,f,h);break e}f=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,h=typeof v=="function"?v.call(y,f,h):v,h==null)break e;f=Y({},f,h);break e;case 2:Ct=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,h=i.effects,h===null?i.effects=[l]:h.push(l))}else y={eventTime:y,lane:h,tag:l.tag,payload:l.payload,callback:l.callback,next:null},d===null?(u=d=y,a=f):d=d.next=y,o|=h;if(l=l.next,l===null){if(l=i.shared.pending,l===null)break;h=l,l=h.next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}while(!0);if(d===null&&(a=f),i.baseState=a,i.firstBaseUpdate=u,i.lastBaseUpdate=d,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);an|=o,e.lanes=o,e.memoizedState=f}}function oc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(T(191,i));i.call(r)}}}var ni={},rt=Bt(ni),zr=Bt(ni),Br=Bt(ni);function Jt(e){if(e===ni)throw Error(T(174));return e}function xa(e,t){switch(B(Br,t),B(zr,e),B(rt,ni),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Wo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Wo(t,e)}$(rt),B(rt,t)}function Bn(){$(rt),$(zr),$(Br)}function dh(e){Jt(Br.current);var t=Jt(rt.current),n=Wo(t,e.type);t!==n&&(B(zr,e),B(rt,n))}function wa(e){zr.current===e&&($(rt),$(zr))}var K=Bt(0);function cs(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var mo=[];function Sa(){for(var e=0;e<mo.length;e++)mo[e]._workInProgressVersionPrimary=null;mo.length=0}var Ii=xt.ReactCurrentDispatcher,yo=xt.ReactCurrentBatchConfig,ln=0,Q=null,ie=null,le=null,ds=!1,wr=!1,Ur=0,Vg=0;function he(){throw Error(T(321))}function ka(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ze(e[n],t[n]))return!1;return!0}function Ca(e,t,n,r,i,s){if(ln=s,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ii.current=e===null||e.memoizedState===null?Ig:zg,e=n(r,i),wr){s=0;do{if(wr=!1,Ur=0,25<=s)throw Error(T(301));s+=1,le=ie=null,t.updateQueue=null,Ii.current=Bg,e=n(r,i)}while(wr)}if(Ii.current=fs,t=ie!==null&&ie.next!==null,ln=0,le=ie=Q=null,ds=!1,t)throw Error(T(300));return e}function ja(){var e=Ur!==0;return Ur=0,e}function et(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return le===null?Q.memoizedState=le=e:le=le.next=e,le}function $e(){if(ie===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=ie.next;var t=le===null?Q.memoizedState:le.next;if(t!==null)le=t,ie=e;else{if(e===null)throw Error(T(310));ie=e,e={memoizedState:ie.memoizedState,baseState:ie.baseState,baseQueue:ie.baseQueue,queue:ie.queue,next:null},le===null?Q.memoizedState=le=e:le=le.next=e}return le}function $r(e,t){return typeof t=="function"?t(e):t}function go(e){var t=$e(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=ie,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var l=o=null,a=null,u=s;do{var d=u.lane;if((ln&d)===d)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=f,o=r):a=a.next=f,Q.lanes|=d,an|=d}u=u.next}while(u!==null&&u!==s);a===null?o=r:a.next=l,Ze(r,t.memoizedState)||(je=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,Q.lanes|=s,an|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function vo(e){var t=$e(),n=t.queue;if(n===null)throw Error(T(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);Ze(s,t.memoizedState)||(je=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function fh(){}function hh(e,t){var n=Q,r=$e(),i=t(),s=!Ze(r.memoizedState,i);if(s&&(r.memoizedState=i,je=!0),r=r.queue,Ta(yh.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||le!==null&&le.memoizedState.tag&1){if(n.flags|=2048,Hr(9,mh.bind(null,n,r,i,t),void 0,null),ae===null)throw Error(T(349));ln&30||ph(n,t,i)}return i}function ph(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function mh(e,t,n,r){t.value=n,t.getSnapshot=r,gh(t)&&vh(e)}function yh(e,t,n){return n(function(){gh(t)&&vh(e)})}function gh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ze(e,n)}catch{return!0}}function vh(e){var t=yt(e,1);t!==null&&Xe(t,e,1,-1)}function lc(e){var t=et();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:$r,lastRenderedState:e},t.queue=e,e=e.dispatch=Og.bind(null,Q,e),[t.memoizedState,e]}function Hr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function xh(){return $e().memoizedState}function zi(e,t,n,r){var i=et();Q.flags|=e,i.memoizedState=Hr(1|t,n,void 0,r===void 0?null:r)}function As(e,t,n,r){var i=$e();r=r===void 0?null:r;var s=void 0;if(ie!==null){var o=ie.memoizedState;if(s=o.destroy,r!==null&&ka(r,o.deps)){i.memoizedState=Hr(t,n,s,r);return}}Q.flags|=e,i.memoizedState=Hr(1|t,n,s,r)}function ac(e,t){return zi(8390656,8,e,t)}function Ta(e,t){return As(2048,8,e,t)}function wh(e,t){return As(4,2,e,t)}function Sh(e,t){return As(4,4,e,t)}function kh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Ch(e,t,n){return n=n!=null?n.concat([e]):null,As(4,4,kh.bind(null,t,e),n)}function Pa(){}function jh(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ka(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Th(e,t){var n=$e();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&ka(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Ph(e,t,n){return ln&21?(Ze(n,t)||(n=Df(),Q.lanes|=n,an|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,je=!0),e.memoizedState=n)}function bg(e,t){var n=I;I=n!==0&&4>n?n:4,e(!0);var r=yo.transition;yo.transition={};try{e(!1),t()}finally{I=n,yo.transition=r}}function Nh(){return $e().memoizedState}function Fg(e,t,n){var r=Rt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Eh(e))Mh(t,n);else if(n=uh(e,t,n,r),n!==null){var i=we();Xe(n,e,r,i),_h(n,t,r)}}function Og(e,t,n){var r=Rt(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Eh(e))Mh(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,l=s(o,n);if(i.hasEagerState=!0,i.eagerState=l,Ze(l,o)){var a=t.interleaved;a===null?(i.next=i,ga(t)):(i.next=a.next,a.next=i),t.interleaved=i;return}}catch{}finally{}n=uh(e,t,i,r),n!==null&&(i=we(),Xe(n,e,r,i),_h(n,t,r))}}function Eh(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function Mh(e,t){wr=ds=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function _h(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ra(e,n)}}var fs={readContext:Ue,useCallback:he,useContext:he,useEffect:he,useImperativeHandle:he,useInsertionEffect:he,useLayoutEffect:he,useMemo:he,useReducer:he,useRef:he,useState:he,useDebugValue:he,useDeferredValue:he,useTransition:he,useMutableSource:he,useSyncExternalStore:he,useId:he,unstable_isNewReconciler:!1},Ig={readContext:Ue,useCallback:function(e,t){return et().memoizedState=[e,t===void 0?null:t],e},useContext:Ue,useEffect:ac,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,zi(4194308,4,kh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return zi(4194308,4,e,t)},useInsertionEffect:function(e,t){return zi(4,2,e,t)},useMemo:function(e,t){var n=et();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=et();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Fg.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=et();return e={current:e},t.memoizedState=e},useState:lc,useDebugValue:Pa,useDeferredValue:function(e){return et().memoizedState=e},useTransition:function(){var e=lc(!1),t=e[0];return e=bg.bind(null,e[1]),et().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,i=et();if(W){if(n===void 0)throw Error(T(407));n=n()}else{if(n=t(),ae===null)throw Error(T(349));ln&30||ph(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,ac(yh.bind(null,r,s,e),[e]),r.flags|=2048,Hr(9,mh.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=et(),t=ae.identifierPrefix;if(W){var n=ct,r=ut;n=(r&~(1<<32-Ye(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ur++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Vg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},zg={readContext:Ue,useCallback:jh,useContext:Ue,useEffect:Ta,useImperativeHandle:Ch,useInsertionEffect:wh,useLayoutEffect:Sh,useMemo:Th,useReducer:go,useRef:xh,useState:function(){return go($r)},useDebugValue:Pa,useDeferredValue:function(e){var t=$e();return Ph(t,ie.memoizedState,e)},useTransition:function(){var e=go($r)[0],t=$e().memoizedState;return[e,t]},useMutableSource:fh,useSyncExternalStore:hh,useId:Nh,unstable_isNewReconciler:!1},Bg={readContext:Ue,useCallback:jh,useContext:Ue,useEffect:Ta,useImperativeHandle:Ch,useInsertionEffect:wh,useLayoutEffect:Sh,useMemo:Th,useReducer:vo,useRef:xh,useState:function(){return vo($r)},useDebugValue:Pa,useDeferredValue:function(e){var t=$e();return ie===null?t.memoizedState=e:Ph(t,ie.memoizedState,e)},useTransition:function(){var e=vo($r)[0],t=$e().memoizedState;return[e,t]},useMutableSource:fh,useSyncExternalStore:hh,useId:Nh,unstable_isNewReconciler:!1};function Ke(e,t){if(e&&e.defaultProps){t=Y({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function fl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Y({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ls={isMounted:function(e){return(e=e._reactInternals)?dn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=we(),i=Rt(e),s=dt(r,i);s.payload=t,n!=null&&(s.callback=n),t=At(e,s,i),t!==null&&(Xe(t,e,i,r),Oi(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=we(),i=Rt(e),s=dt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=At(e,s,i),t!==null&&(Xe(t,e,i,r),Oi(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=we(),r=Rt(e),i=dt(n,r);i.tag=2,t!=null&&(i.callback=t),t=At(e,i,r),t!==null&&(Xe(t,e,r,n),Oi(t,e,r))}};function uc(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!br(n,r)||!br(i,s):!0}function Dh(e,t,n){var r=!1,i=Ft,s=t.contextType;return typeof s=="object"&&s!==null?s=Ue(s):(i=Pe(t)?sn:ve.current,r=t.contextTypes,s=(r=r!=null)?On(e,i):Ft),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ls,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function cc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ls.enqueueReplaceState(t,t.state,null)}function hl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},va(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=Ue(s):(s=Pe(t)?sn:ve.current,i.context=On(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(fl(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Ls.enqueueReplaceState(i,i.state,null),us(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Un(e,t){try{var n="",r=t;do n+=my(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function xo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function pl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Ug=typeof WeakMap=="function"?WeakMap:Map;function Ah(e,t,n){n=dt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){ps||(ps=!0,jl=r),pl(e,t)},n}function Lh(e,t,n){n=dt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){pl(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){pl(e,t),typeof r!="function"&&(Lt===null?Lt=new Set([this]):Lt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function dc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Ug;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=n0.bind(null,e,t,n),t.then(e,e))}function fc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function hc(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=dt(-1,1),t.tag=2,At(n,t,1))),n.lanes|=1),e)}var $g=xt.ReactCurrentOwner,je=!1;function xe(e,t,n,r){t.child=e===null?ah(t,null,n,r):zn(t,e.child,n,r)}function pc(e,t,n,r,i){n=n.render;var s=t.ref;return Vn(t,i),r=Ca(e,t,n,r,s,i),n=ja(),e!==null&&!je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(W&&n&&da(t),t.flags|=1,xe(e,t,r,i),t.child)}function mc(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!Ra(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Rh(e,t,s,r,i)):(e=Hi(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:br,n(o,r)&&e.ref===t.ref)return gt(e,t,i)}return t.flags|=1,e=Vt(s,r),e.ref=t.ref,e.return=t,t.child=e}function Rh(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(br(s,r)&&e.ref===t.ref)if(je=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(je=!0);else return t.lanes=e.lanes,gt(e,t,i)}return ml(e,t,n,r,i)}function Vh(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(Tn,Ee),Ee|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(Tn,Ee),Ee|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,B(Tn,Ee),Ee|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,B(Tn,Ee),Ee|=r;return xe(e,t,i,n),t.child}function bh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ml(e,t,n,r,i){var s=Pe(n)?sn:ve.current;return s=On(t,s),Vn(t,i),n=Ca(e,t,n,r,s,i),r=ja(),e!==null&&!je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,gt(e,t,i)):(W&&r&&da(t),t.flags|=1,xe(e,t,n,i),t.child)}function yc(e,t,n,r,i){if(Pe(n)){var s=!0;is(t)}else s=!1;if(Vn(t,i),t.stateNode===null)Bi(e,t),Dh(t,n,r),hl(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,l=t.memoizedProps;o.props=l;var a=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=Ue(u):(u=Pe(n)?sn:ve.current,u=On(t,u));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";f||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==r||a!==u)&&cc(t,o,r,u),Ct=!1;var h=t.memoizedState;o.state=h,us(t,r,o,i),a=t.memoizedState,l!==r||h!==a||Te.current||Ct?(typeof d=="function"&&(fl(t,n,d,r),a=t.memoizedState),(l=Ct||uc(t,n,l,r,h,a,u))?(f||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),o.props=r,o.state=a,o.context=u,r=l):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,ch(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Ke(t.type,l),o.props=u,f=t.pendingProps,h=o.context,a=n.contextType,typeof a=="object"&&a!==null?a=Ue(a):(a=Pe(n)?sn:ve.current,a=On(t,a));var y=n.getDerivedStateFromProps;(d=typeof y=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(l!==f||h!==a)&&cc(t,o,r,a),Ct=!1,h=t.memoizedState,o.state=h,us(t,r,o,i);var v=t.memoizedState;l!==f||h!==v||Te.current||Ct?(typeof y=="function"&&(fl(t,n,y,r),v=t.memoizedState),(u=Ct||uc(t,n,u,r,h,v,a)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,a)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),o.props=r,o.state=v,o.context=a,r=u):(typeof o.componentDidUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return yl(e,t,n,r,s,i)}function yl(e,t,n,r,i,s){bh(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&tc(t,n,!1),gt(e,t,s);r=t.stateNode,$g.current=t;var l=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=zn(t,e.child,null,s),t.child=zn(t,null,l,s)):xe(e,t,l,s),t.memoizedState=r.state,i&&tc(t,n,!0),t.child}function Fh(e){var t=e.stateNode;t.pendingContext?ec(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ec(e,t.context,!1),xa(e,t.containerInfo)}function gc(e,t,n,r,i){return In(),ha(i),t.flags|=256,xe(e,t,n,r),t.child}var gl={dehydrated:null,treeContext:null,retryLane:0};function vl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Oh(e,t,n){var r=t.pendingProps,i=K.current,s=!1,o=(t.flags&128)!==0,l;if((l=o)||(l=e!==null&&e.memoizedState===null?!1:(i&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),B(K,i&1),e===null)return cl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=bs(o,r,0,null),e=nn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=vl(n),t.memoizedState=gl,e):Na(t,o));if(i=e.memoizedState,i!==null&&(l=i.dehydrated,l!==null))return Hg(e,t,o,r,l,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,l=i.sibling;var a={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Vt(i,a),r.subtreeFlags=i.subtreeFlags&14680064),l!==null?s=Vt(l,s):(s=nn(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?vl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=gl,r}return s=e.child,e=s.sibling,r=Vt(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Na(e,t){return t=bs({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ti(e,t,n,r){return r!==null&&ha(r),zn(t,e.child,null,n),e=Na(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Hg(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=xo(Error(T(422))),Ti(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=bs({mode:"visible",children:r.children},i,0,null),s=nn(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&zn(t,e.child,null,o),t.child.memoizedState=vl(o),t.memoizedState=gl,s);if(!(t.mode&1))return Ti(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(T(419)),r=xo(s,r,void 0),Ti(e,t,o,r)}if(l=(o&e.childLanes)!==0,je||l){if(r=ae,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,yt(e,i),Xe(r,e,i,-1))}return La(),r=xo(Error(T(421))),Ti(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=r0.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,Me=Dt(i.nextSibling),_e=t,W=!0,Qe=null,e!==null&&(Oe[Ie++]=ut,Oe[Ie++]=ct,Oe[Ie++]=on,ut=e.id,ct=e.overflow,on=t),t=Na(t,r.children),t.flags|=4096,t)}function vc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),dl(e.return,t,n)}function wo(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function Ih(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(xe(e,t,r.children,n),r=K.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&vc(e,n,t);else if(e.tag===19)vc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(K,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&cs(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),wo(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&cs(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}wo(t,!0,n,null,s);break;case"together":wo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Bi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function gt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),an|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(T(153));if(t.child!==null){for(e=t.child,n=Vt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Vt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Wg(e,t,n){switch(t.tag){case 3:Fh(t),In();break;case 5:dh(t);break;case 1:Pe(t.type)&&is(t);break;case 4:xa(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;B(ls,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(K,K.current&1),t.flags|=128,null):n&t.child.childLanes?Oh(e,t,n):(B(K,K.current&1),e=gt(e,t,n),e!==null?e.sibling:null);B(K,K.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Ih(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),B(K,K.current),r)break;return null;case 22:case 23:return t.lanes=0,Vh(e,t,n)}return gt(e,t,n)}var zh,xl,Bh,Uh;zh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};xl=function(){};Bh=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Jt(rt.current);var s=null;switch(n){case"input":i=Bo(e,i),r=Bo(e,r),s=[];break;case"select":i=Y({},i,{value:void 0}),r=Y({},r,{value:void 0}),s=[];break;case"textarea":i=Ho(e,i),r=Ho(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ns)}Ko(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var l=i[u];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Mr.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var a=r[u];if(l=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(o in l)!l.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in a)a.hasOwnProperty(o)&&l[o]!==a[o]&&(n||(n={}),n[o]=a[o])}else n||(s||(s=[]),s.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Mr.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&U("scroll",e),s||l===a||(s=[])):(s=s||[]).push(u,a))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};Uh=function(e,t,n,r){n!==r&&(t.flags|=4)};function sr(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Kg(e,t,n){var r=t.pendingProps;switch(fa(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pe(t),null;case 1:return Pe(t.type)&&rs(),pe(t),null;case 3:return r=t.stateNode,Bn(),$(Te),$(ve),Sa(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ci(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Qe!==null&&(Nl(Qe),Qe=null))),xl(e,t),pe(t),null;case 5:wa(t);var i=Jt(Br.current);if(n=t.type,e!==null&&t.stateNode!=null)Bh(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(T(166));return pe(t),null}if(e=Jt(rt.current),Ci(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[tt]=t,r[Ir]=s,e=(t.mode&1)!==0,n){case"dialog":U("cancel",r),U("close",r);break;case"iframe":case"object":case"embed":U("load",r);break;case"video":case"audio":for(i=0;i<dr.length;i++)U(dr[i],r);break;case"source":U("error",r);break;case"img":case"image":case"link":U("error",r),U("load",r);break;case"details":U("toggle",r);break;case"input":Nu(r,s),U("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},U("invalid",r);break;case"textarea":Mu(r,s),U("invalid",r)}Ko(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var l=s[o];o==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&ki(r.textContent,l,e),i=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&ki(r.textContent,l,e),i=["children",""+l]):Mr.hasOwnProperty(o)&&l!=null&&o==="onScroll"&&U("scroll",r)}switch(n){case"input":pi(r),Eu(r,s,!0);break;case"textarea":pi(r),_u(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=ns)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=yf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[tt]=t,e[Ir]=r,zh(e,t,!1,!1),t.stateNode=e;e:{switch(o=Go(n,r),n){case"dialog":U("cancel",e),U("close",e),i=r;break;case"iframe":case"object":case"embed":U("load",e),i=r;break;case"video":case"audio":for(i=0;i<dr.length;i++)U(dr[i],e);i=r;break;case"source":U("error",e),i=r;break;case"img":case"image":case"link":U("error",e),U("load",e),i=r;break;case"details":U("toggle",e),i=r;break;case"input":Nu(e,r),i=Bo(e,r),U("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Y({},r,{value:void 0}),U("invalid",e);break;case"textarea":Mu(e,r),i=Ho(e,r),U("invalid",e);break;default:i=r}Ko(n,i),l=i;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?xf(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&gf(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&_r(e,a):typeof a=="number"&&_r(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Mr.hasOwnProperty(s)?a!=null&&s==="onScroll"&&U("scroll",e):a!=null&&Zl(e,s,a,o))}switch(n){case"input":pi(e),Eu(e,r,!1);break;case"textarea":pi(e),_u(e);break;case"option":r.value!=null&&e.setAttribute("value",""+bt(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Dn(e,!!r.multiple,s,!1):r.defaultValue!=null&&Dn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ns)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pe(t),null;case 6:if(e&&t.stateNode!=null)Uh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(T(166));if(n=Jt(Br.current),Jt(rt.current),Ci(t)){if(r=t.stateNode,n=t.memoizedProps,r[tt]=t,(s=r.nodeValue!==n)&&(e=_e,e!==null))switch(e.tag){case 3:ki(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ki(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[tt]=t,t.stateNode=r}return pe(t),null;case 13:if($(K),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Me!==null&&t.mode&1&&!(t.flags&128))oh(),In(),t.flags|=98560,s=!1;else if(s=Ci(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(T(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(T(317));s[tt]=t}else In(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pe(t),s=!1}else Qe!==null&&(Nl(Qe),Qe=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||K.current&1?se===0&&(se=3):La())),t.updateQueue!==null&&(t.flags|=4),pe(t),null);case 4:return Bn(),xl(e,t),e===null&&Fr(t.stateNode.containerInfo),pe(t),null;case 10:return ya(t.type._context),pe(t),null;case 17:return Pe(t.type)&&rs(),pe(t),null;case 19:if($(K),s=t.memoizedState,s===null)return pe(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)sr(s,!1);else{if(se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=cs(e),o!==null){for(t.flags|=128,sr(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(K,K.current&1|2),t.child}e=e.sibling}s.tail!==null&&ee()>$n&&(t.flags|=128,r=!0,sr(s,!1),t.lanes=4194304)}else{if(!r)if(e=cs(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),sr(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!W)return pe(t),null}else 2*ee()-s.renderingStartTime>$n&&n!==1073741824&&(t.flags|=128,r=!0,sr(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=ee(),t.sibling=null,n=K.current,B(K,r?n&1|2:n&1),t):(pe(t),null);case 22:case 23:return Aa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ee&1073741824&&(pe(t),t.subtreeFlags&6&&(t.flags|=8192)):pe(t),null;case 24:return null;case 25:return null}throw Error(T(156,t.tag))}function Gg(e,t){switch(fa(t),t.tag){case 1:return Pe(t.type)&&rs(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Bn(),$(Te),$(ve),Sa(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return wa(t),null;case 13:if($(K),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(T(340));In()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(K),null;case 4:return Bn(),null;case 10:return ya(t.type._context),null;case 22:case 23:return Aa(),null;case 24:return null;default:return null}}var Pi=!1,ye=!1,Qg=typeof WeakSet=="function"?WeakSet:Set,M=null;function jn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Z(e,t,r)}else n.current=null}function wl(e,t,n){try{n()}catch(r){Z(e,t,r)}}var xc=!1;function Yg(e,t){if(rl=Ji,e=Gf(),ca(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,l=-1,a=-1,u=0,d=0,f=e,h=null;t:for(;;){for(var y;f!==n||i!==0&&f.nodeType!==3||(l=o+i),f!==s||r!==0&&f.nodeType!==3||(a=o+r),f.nodeType===3&&(o+=f.nodeValue.length),(y=f.firstChild)!==null;)h=f,f=y;for(;;){if(f===e)break t;if(h===n&&++u===i&&(l=o),h===s&&++d===r&&(a=o),(y=f.nextSibling)!==null)break;f=h,h=f.parentNode}f=y}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(il={focusedElem:e,selectionRange:n},Ji=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,S=v.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?x:Ke(t.type,x),S);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(T(163))}}catch(w){Z(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return v=xc,xc=!1,v}function Sr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&wl(t,n,s)}i=i.next}while(i!==r)}}function Rs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Sl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function $h(e){var t=e.alternate;t!==null&&(e.alternate=null,$h(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[tt],delete t[Ir],delete t[ll],delete t[Dg],delete t[Ag])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Hh(e){return e.tag===5||e.tag===3||e.tag===4}function wc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Hh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function kl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ns));else if(r!==4&&(e=e.child,e!==null))for(kl(e,t,n),e=e.sibling;e!==null;)kl(e,t,n),e=e.sibling}function Cl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Cl(e,t,n),e=e.sibling;e!==null;)Cl(e,t,n),e=e.sibling}var ue=null,Ge=!1;function wt(e,t,n){for(n=n.child;n!==null;)Wh(e,t,n),n=n.sibling}function Wh(e,t,n){if(nt&&typeof nt.onCommitFiberUnmount=="function")try{nt.onCommitFiberUnmount(Ps,n)}catch{}switch(n.tag){case 5:ye||jn(n,t);case 6:var r=ue,i=Ge;ue=null,wt(e,t,n),ue=r,Ge=i,ue!==null&&(Ge?(e=ue,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ue.removeChild(n.stateNode));break;case 18:ue!==null&&(Ge?(e=ue,n=n.stateNode,e.nodeType===8?ho(e.parentNode,n):e.nodeType===1&&ho(e,n),Rr(e)):ho(ue,n.stateNode));break;case 4:r=ue,i=Ge,ue=n.stateNode.containerInfo,Ge=!0,wt(e,t,n),ue=r,Ge=i;break;case 0:case 11:case 14:case 15:if(!ye&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&wl(n,t,o),i=i.next}while(i!==r)}wt(e,t,n);break;case 1:if(!ye&&(jn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){Z(n,t,l)}wt(e,t,n);break;case 21:wt(e,t,n);break;case 22:n.mode&1?(ye=(r=ye)||n.memoizedState!==null,wt(e,t,n),ye=r):wt(e,t,n);break;default:wt(e,t,n)}}function Sc(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Qg),t.forEach(function(r){var i=i0.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function He(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,l=o;e:for(;l!==null;){switch(l.tag){case 5:ue=l.stateNode,Ge=!1;break e;case 3:ue=l.stateNode.containerInfo,Ge=!0;break e;case 4:ue=l.stateNode.containerInfo,Ge=!0;break e}l=l.return}if(ue===null)throw Error(T(160));Wh(s,o,i),ue=null,Ge=!1;var a=i.alternate;a!==null&&(a.return=null),i.return=null}catch(u){Z(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Kh(t,e),t=t.sibling}function Kh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(He(t,e),Je(e),r&4){try{Sr(3,e,e.return),Rs(3,e)}catch(x){Z(e,e.return,x)}try{Sr(5,e,e.return)}catch(x){Z(e,e.return,x)}}break;case 1:He(t,e),Je(e),r&512&&n!==null&&jn(n,n.return);break;case 5:if(He(t,e),Je(e),r&512&&n!==null&&jn(n,n.return),e.flags&32){var i=e.stateNode;try{_r(i,"")}catch(x){Z(e,e.return,x)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&pf(i,s),Go(l,o);var u=Go(l,s);for(o=0;o<a.length;o+=2){var d=a[o],f=a[o+1];d==="style"?xf(i,f):d==="dangerouslySetInnerHTML"?gf(i,f):d==="children"?_r(i,f):Zl(i,d,f,u)}switch(l){case"input":Uo(i,s);break;case"textarea":mf(i,s);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var y=s.value;y!=null?Dn(i,!!s.multiple,y,!1):h!==!!s.multiple&&(s.defaultValue!=null?Dn(i,!!s.multiple,s.defaultValue,!0):Dn(i,!!s.multiple,s.multiple?[]:"",!1))}i[Ir]=s}catch(x){Z(e,e.return,x)}}break;case 6:if(He(t,e),Je(e),r&4){if(e.stateNode===null)throw Error(T(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(x){Z(e,e.return,x)}}break;case 3:if(He(t,e),Je(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Rr(t.containerInfo)}catch(x){Z(e,e.return,x)}break;case 4:He(t,e),Je(e);break;case 13:He(t,e),Je(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(_a=ee())),r&4&&Sc(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(ye=(u=ye)||d,He(t,e),ye=u):He(t,e),Je(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!d&&e.mode&1)for(M=e,d=e.child;d!==null;){for(f=M=d;M!==null;){switch(h=M,y=h.child,h.tag){case 0:case 11:case 14:case 15:Sr(4,h,h.return);break;case 1:jn(h,h.return);var v=h.stateNode;if(typeof v.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){Z(r,n,x)}}break;case 5:jn(h,h.return);break;case 22:if(h.memoizedState!==null){Cc(f);continue}}y!==null?(y.return=h,M=y):Cc(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{i=f.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=f.stateNode,a=f.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=vf("display",o))}catch(x){Z(e,e.return,x)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(x){Z(e,e.return,x)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:He(t,e),Je(e),r&4&&Sc(e);break;case 21:break;default:He(t,e),Je(e)}}function Je(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Hh(n)){var r=n;break e}n=n.return}throw Error(T(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(_r(i,""),r.flags&=-33);var s=wc(e);Cl(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,l=wc(e);kl(e,l,o);break;default:throw Error(T(161))}}catch(a){Z(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Xg(e,t,n){M=e,Gh(e)}function Gh(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var i=M,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Pi;if(!o){var l=i.alternate,a=l!==null&&l.memoizedState!==null||ye;l=Pi;var u=ye;if(Pi=o,(ye=a)&&!u)for(M=i;M!==null;)o=M,a=o.child,o.tag===22&&o.memoizedState!==null?jc(i):a!==null?(a.return=o,M=a):jc(i);for(;s!==null;)M=s,Gh(s),s=s.sibling;M=i,Pi=l,ye=u}kc(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,M=s):kc(e)}}function kc(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ye||Rs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ye)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:Ke(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&oc(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}oc(t,o,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&Rr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(T(163))}ye||t.flags&512&&Sl(t)}catch(h){Z(t,t.return,h)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Cc(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function jc(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Rs(4,t)}catch(a){Z(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(a){Z(t,i,a)}}var s=t.return;try{Sl(t)}catch(a){Z(t,s,a)}break;case 5:var o=t.return;try{Sl(t)}catch(a){Z(t,o,a)}}}catch(a){Z(t,t.return,a)}if(t===e){M=null;break}var l=t.sibling;if(l!==null){l.return=t.return,M=l;break}M=t.return}}var Zg=Math.ceil,hs=xt.ReactCurrentDispatcher,Ea=xt.ReactCurrentOwner,Be=xt.ReactCurrentBatchConfig,O=0,ae=null,ne=null,de=0,Ee=0,Tn=Bt(0),se=0,Wr=null,an=0,Vs=0,Ma=0,kr=null,Ce=null,_a=0,$n=1/0,lt=null,ps=!1,jl=null,Lt=null,Ni=!1,Nt=null,ms=0,Cr=0,Tl=null,Ui=-1,$i=0;function we(){return O&6?ee():Ui!==-1?Ui:Ui=ee()}function Rt(e){return e.mode&1?O&2&&de!==0?de&-de:Rg.transition!==null?($i===0&&($i=Df()),$i):(e=I,e!==0||(e=window.event,e=e===void 0?16:Of(e.type)),e):1}function Xe(e,t,n,r){if(50<Cr)throw Cr=0,Tl=null,Error(T(185));Jr(e,n,r),(!(O&2)||e!==ae)&&(e===ae&&(!(O&2)&&(Vs|=n),se===4&&Tt(e,de)),Ne(e,r),n===1&&O===0&&!(t.mode&1)&&($n=ee()+500,Ds&&Ut()))}function Ne(e,t){var n=e.callbackNode;Ry(e,t);var r=qi(e,e===ae?de:0);if(r===0)n!==null&&Lu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Lu(n),t===1)e.tag===0?Lg(Tc.bind(null,e)):rh(Tc.bind(null,e)),Mg(function(){!(O&6)&&Ut()}),n=null;else{switch(Af(r)){case 1:n=na;break;case 4:n=Mf;break;case 16:n=Zi;break;case 536870912:n=_f;break;default:n=Zi}n=tp(n,Qh.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Qh(e,t){if(Ui=-1,$i=0,O&6)throw Error(T(327));var n=e.callbackNode;if(bn()&&e.callbackNode!==n)return null;var r=qi(e,e===ae?de:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ys(e,r);else{t=r;var i=O;O|=2;var s=Xh();(ae!==e||de!==t)&&(lt=null,$n=ee()+500,tn(e,t));do try{e0();break}catch(l){Yh(e,l)}while(!0);ma(),hs.current=s,O=i,ne!==null?t=0:(ae=null,de=0,t=se)}if(t!==0){if(t===2&&(i=qo(e),i!==0&&(r=i,t=Pl(e,i))),t===1)throw n=Wr,tn(e,0),Tt(e,r),Ne(e,ee()),n;if(t===6)Tt(e,r);else{if(i=e.current.alternate,!(r&30)&&!qg(i)&&(t=ys(e,r),t===2&&(s=qo(e),s!==0&&(r=s,t=Pl(e,s))),t===1))throw n=Wr,tn(e,0),Tt(e,r),Ne(e,ee()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(T(345));case 2:Qt(e,Ce,lt);break;case 3:if(Tt(e,r),(r&130023424)===r&&(t=_a+500-ee(),10<t)){if(qi(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){we(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=ol(Qt.bind(null,e,Ce,lt),t);break}Qt(e,Ce,lt);break;case 4:if(Tt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-Ye(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=ee()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Zg(r/1960))-r,10<r){e.timeoutHandle=ol(Qt.bind(null,e,Ce,lt),r);break}Qt(e,Ce,lt);break;case 5:Qt(e,Ce,lt);break;default:throw Error(T(329))}}}return Ne(e,ee()),e.callbackNode===n?Qh.bind(null,e):null}function Pl(e,t){var n=kr;return e.current.memoizedState.isDehydrated&&(tn(e,t).flags|=256),e=ys(e,t),e!==2&&(t=Ce,Ce=n,t!==null&&Nl(t)),e}function Nl(e){Ce===null?Ce=e:Ce.push.apply(Ce,e)}function qg(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!Ze(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Tt(e,t){for(t&=~Ma,t&=~Vs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Ye(t),r=1<<n;e[n]=-1,t&=~r}}function Tc(e){if(O&6)throw Error(T(327));bn();var t=qi(e,0);if(!(t&1))return Ne(e,ee()),null;var n=ys(e,t);if(e.tag!==0&&n===2){var r=qo(e);r!==0&&(t=r,n=Pl(e,r))}if(n===1)throw n=Wr,tn(e,0),Tt(e,t),Ne(e,ee()),n;if(n===6)throw Error(T(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Qt(e,Ce,lt),Ne(e,ee()),null}function Da(e,t){var n=O;O|=1;try{return e(t)}finally{O=n,O===0&&($n=ee()+500,Ds&&Ut())}}function un(e){Nt!==null&&Nt.tag===0&&!(O&6)&&bn();var t=O;O|=1;var n=Be.transition,r=I;try{if(Be.transition=null,I=1,e)return e()}finally{I=r,Be.transition=n,O=t,!(O&6)&&Ut()}}function Aa(){Ee=Tn.current,$(Tn)}function tn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Eg(n)),ne!==null)for(n=ne.return;n!==null;){var r=n;switch(fa(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&rs();break;case 3:Bn(),$(Te),$(ve),Sa();break;case 5:wa(r);break;case 4:Bn();break;case 13:$(K);break;case 19:$(K);break;case 10:ya(r.type._context);break;case 22:case 23:Aa()}n=n.return}if(ae=e,ne=e=Vt(e.current,null),de=Ee=t,se=0,Wr=null,Ma=Vs=an=0,Ce=kr=null,qt!==null){for(t=0;t<qt.length;t++)if(n=qt[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}qt=null}return e}function Yh(e,t){do{var n=ne;try{if(ma(),Ii.current=fs,ds){for(var r=Q.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}ds=!1}if(ln=0,le=ie=Q=null,wr=!1,Ur=0,Ea.current=null,n===null||n.return===null){se=1,Wr=t,ne=null;break}e:{var s=e,o=n.return,l=n,a=t;if(t=de,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,d=l,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var y=fc(o);if(y!==null){y.flags&=-257,hc(y,o,l,s,t),y.mode&1&&dc(s,u,t),t=y,a=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(a),t.updateQueue=x}else v.add(a);break e}else{if(!(t&1)){dc(s,u,t),La();break e}a=Error(T(426))}}else if(W&&l.mode&1){var S=fc(o);if(S!==null){!(S.flags&65536)&&(S.flags|=256),hc(S,o,l,s,t),ha(Un(a,l));break e}}s=a=Un(a,l),se!==4&&(se=2),kr===null?kr=[s]:kr.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var m=Ah(s,a,t);sc(s,m);break e;case 1:l=a;var p=s.type,g=s.stateNode;if(!(s.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Lt===null||!Lt.has(g)))){s.flags|=65536,t&=-t,s.lanes|=t;var w=Lh(s,l,t);sc(s,w);break e}}s=s.return}while(s!==null)}qh(n)}catch(k){t=k,ne===n&&n!==null&&(ne=n=n.return);continue}break}while(!0)}function Xh(){var e=hs.current;return hs.current=fs,e===null?fs:e}function La(){(se===0||se===3||se===2)&&(se=4),ae===null||!(an&268435455)&&!(Vs&268435455)||Tt(ae,de)}function ys(e,t){var n=O;O|=2;var r=Xh();(ae!==e||de!==t)&&(lt=null,tn(e,t));do try{Jg();break}catch(i){Yh(e,i)}while(!0);if(ma(),O=n,hs.current=r,ne!==null)throw Error(T(261));return ae=null,de=0,se}function Jg(){for(;ne!==null;)Zh(ne)}function e0(){for(;ne!==null&&!Ty();)Zh(ne)}function Zh(e){var t=ep(e.alternate,e,Ee);e.memoizedProps=e.pendingProps,t===null?qh(e):ne=t,Ea.current=null}function qh(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Gg(n,t),n!==null){n.flags&=32767,ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{se=6,ne=null;return}}else if(n=Kg(n,t,Ee),n!==null){ne=n;return}if(t=t.sibling,t!==null){ne=t;return}ne=t=e}while(t!==null);se===0&&(se=5)}function Qt(e,t,n){var r=I,i=Be.transition;try{Be.transition=null,I=1,t0(e,t,n,r)}finally{Be.transition=i,I=r}return null}function t0(e,t,n,r){do bn();while(Nt!==null);if(O&6)throw Error(T(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(T(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Vy(e,s),e===ae&&(ne=ae=null,de=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ni||(Ni=!0,tp(Zi,function(){return bn(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Be.transition,Be.transition=null;var o=I;I=1;var l=O;O|=4,Ea.current=null,Yg(e,n),Kh(n,e),Sg(il),Ji=!!rl,il=rl=null,e.current=n,Xg(n),Py(),O=l,I=o,Be.transition=s}else e.current=n;if(Ni&&(Ni=!1,Nt=e,ms=i),s=e.pendingLanes,s===0&&(Lt=null),My(n.stateNode),Ne(e,ee()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(ps)throw ps=!1,e=jl,jl=null,e;return ms&1&&e.tag!==0&&bn(),s=e.pendingLanes,s&1?e===Tl?Cr++:(Cr=0,Tl=e):Cr=0,Ut(),null}function bn(){if(Nt!==null){var e=Af(ms),t=Be.transition,n=I;try{if(Be.transition=null,I=16>e?16:e,Nt===null)var r=!1;else{if(e=Nt,Nt=null,ms=0,O&6)throw Error(T(331));var i=O;for(O|=4,M=e.current;M!==null;){var s=M,o=s.child;if(M.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(M=u;M!==null;){var d=M;switch(d.tag){case 0:case 11:case 15:Sr(8,d,s)}var f=d.child;if(f!==null)f.return=d,M=f;else for(;M!==null;){d=M;var h=d.sibling,y=d.return;if($h(d),d===u){M=null;break}if(h!==null){h.return=y,M=h;break}M=y}}}var v=s.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var S=x.sibling;x.sibling=null,x=S}while(x!==null)}}M=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,M=o;else e:for(;M!==null;){if(s=M,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Sr(9,s,s.return)}var m=s.sibling;if(m!==null){m.return=s.return,M=m;break e}M=s.return}}var p=e.current;for(M=p;M!==null;){o=M;var g=o.child;if(o.subtreeFlags&2064&&g!==null)g.return=o,M=g;else e:for(o=p;M!==null;){if(l=M,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Rs(9,l)}}catch(k){Z(l,l.return,k)}if(l===o){M=null;break e}var w=l.sibling;if(w!==null){w.return=l.return,M=w;break e}M=l.return}}if(O=i,Ut(),nt&&typeof nt.onPostCommitFiberRoot=="function")try{nt.onPostCommitFiberRoot(Ps,e)}catch{}r=!0}return r}finally{I=n,Be.transition=t}}return!1}function Pc(e,t,n){t=Un(n,t),t=Ah(e,t,1),e=At(e,t,1),t=we(),e!==null&&(Jr(e,1,t),Ne(e,t))}function Z(e,t,n){if(e.tag===3)Pc(e,e,n);else for(;t!==null;){if(t.tag===3){Pc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Lt===null||!Lt.has(r))){e=Un(n,e),e=Lh(t,e,1),t=At(t,e,1),e=we(),t!==null&&(Jr(t,1,e),Ne(t,e));break}}t=t.return}}function n0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=we(),e.pingedLanes|=e.suspendedLanes&n,ae===e&&(de&n)===n&&(se===4||se===3&&(de&130023424)===de&&500>ee()-_a?tn(e,0):Ma|=n),Ne(e,t)}function Jh(e,t){t===0&&(e.mode&1?(t=gi,gi<<=1,!(gi&130023424)&&(gi=4194304)):t=1);var n=we();e=yt(e,t),e!==null&&(Jr(e,t,n),Ne(e,n))}function r0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Jh(e,n)}function i0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(T(314))}r!==null&&r.delete(t),Jh(e,n)}var ep;ep=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Te.current)je=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return je=!1,Wg(e,t,n);je=!!(e.flags&131072)}else je=!1,W&&t.flags&1048576&&ih(t,os,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Bi(e,t),e=t.pendingProps;var i=On(t,ve.current);Vn(t,n),i=Ca(null,t,r,e,i,n);var s=ja();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Pe(r)?(s=!0,is(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,va(t),i.updater=Ls,t.stateNode=i,i._reactInternals=t,hl(t,r,e,n),t=yl(null,t,r,!0,s,n)):(t.tag=0,W&&s&&da(t),xe(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Bi(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=o0(r),e=Ke(r,e),i){case 0:t=ml(null,t,r,e,n);break e;case 1:t=yc(null,t,r,e,n);break e;case 11:t=pc(null,t,r,e,n);break e;case 14:t=mc(null,t,r,Ke(r.type,e),n);break e}throw Error(T(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),ml(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),yc(e,t,r,i,n);case 3:e:{if(Fh(t),e===null)throw Error(T(387));r=t.pendingProps,s=t.memoizedState,i=s.element,ch(e,t),us(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=Un(Error(T(423)),t),t=gc(e,t,r,n,i);break e}else if(r!==i){i=Un(Error(T(424)),t),t=gc(e,t,r,n,i);break e}else for(Me=Dt(t.stateNode.containerInfo.firstChild),_e=t,W=!0,Qe=null,n=ah(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(In(),r===i){t=gt(e,t,n);break e}xe(e,t,r,n)}t=t.child}return t;case 5:return dh(t),e===null&&cl(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,sl(r,i)?o=null:s!==null&&sl(r,s)&&(t.flags|=32),bh(e,t),xe(e,t,o,n),t.child;case 6:return e===null&&cl(t),null;case 13:return Oh(e,t,n);case 4:return xa(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=zn(t,null,r,n):xe(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),pc(e,t,r,i,n);case 7:return xe(e,t,t.pendingProps,n),t.child;case 8:return xe(e,t,t.pendingProps.children,n),t.child;case 12:return xe(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,B(ls,r._currentValue),r._currentValue=o,s!==null)if(Ze(s.value,o)){if(s.children===i.children&&!Te.current){t=gt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){o=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=dt(-1,n&-n),a.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?a.next=a:(a.next=d.next,d.next=a),u.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),dl(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(T(341));o.lanes|=n,l=o.alternate,l!==null&&(l.lanes|=n),dl(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}xe(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Vn(t,n),i=Ue(i),r=r(i),t.flags|=1,xe(e,t,r,n),t.child;case 14:return r=t.type,i=Ke(r,t.pendingProps),i=Ke(r.type,i),mc(e,t,r,i,n);case 15:return Rh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Ke(r,i),Bi(e,t),t.tag=1,Pe(r)?(e=!0,is(t)):e=!1,Vn(t,n),Dh(t,r,i),hl(t,r,i,n),yl(null,t,r,!0,e,n);case 19:return Ih(e,t,n);case 22:return Vh(e,t,n)}throw Error(T(156,t.tag))};function tp(e,t){return Ef(e,t)}function s0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ze(e,t,n,r){return new s0(e,t,n,r)}function Ra(e){return e=e.prototype,!(!e||!e.isReactComponent)}function o0(e){if(typeof e=="function")return Ra(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Jl)return 11;if(e===ea)return 14}return 2}function Vt(e,t){var n=e.alternate;return n===null?(n=ze(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Hi(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")Ra(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case mn:return nn(n.children,i,s,t);case ql:o=8,i|=8;break;case Fo:return e=ze(12,n,t,i|2),e.elementType=Fo,e.lanes=s,e;case Oo:return e=ze(13,n,t,i),e.elementType=Oo,e.lanes=s,e;case Io:return e=ze(19,n,t,i),e.elementType=Io,e.lanes=s,e;case df:return bs(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case uf:o=10;break e;case cf:o=9;break e;case Jl:o=11;break e;case ea:o=14;break e;case kt:o=16,r=null;break e}throw Error(T(130,e==null?e:typeof e,""))}return t=ze(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function nn(e,t,n,r){return e=ze(7,e,r,t),e.lanes=n,e}function bs(e,t,n,r){return e=ze(22,e,r,t),e.elementType=df,e.lanes=n,e.stateNode={isHidden:!1},e}function So(e,t,n){return e=ze(6,e,null,t),e.lanes=n,e}function ko(e,t,n){return t=ze(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function l0(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=to(0),this.expirationTimes=to(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=to(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Va(e,t,n,r,i,s,o,l,a){return e=new l0(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=ze(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},va(s),e}function a0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:pn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function np(e){if(!e)return Ft;e=e._reactInternals;e:{if(dn(e)!==e||e.tag!==1)throw Error(T(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Pe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(T(171))}if(e.tag===1){var n=e.type;if(Pe(n))return nh(e,n,t)}return t}function rp(e,t,n,r,i,s,o,l,a){return e=Va(n,r,!0,e,i,s,o,l,a),e.context=np(null),n=e.current,r=we(),i=Rt(n),s=dt(r,i),s.callback=t??null,At(n,s,i),e.current.lanes=i,Jr(e,i,r),Ne(e,r),e}function Fs(e,t,n,r){var i=t.current,s=we(),o=Rt(i);return n=np(n),t.context===null?t.context=n:t.pendingContext=n,t=dt(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=At(i,t,o),e!==null&&(Xe(e,i,o,s),Oi(e,i,o)),o}function gs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Nc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function ba(e,t){Nc(e,t),(e=e.alternate)&&Nc(e,t)}function u0(){return null}var ip=typeof reportError=="function"?reportError:function(e){console.error(e)};function Fa(e){this._internalRoot=e}Os.prototype.render=Fa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(T(409));Fs(e,t,null,null)};Os.prototype.unmount=Fa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;un(function(){Fs(null,e,null,null)}),t[mt]=null}};function Os(e){this._internalRoot=e}Os.prototype.unstable_scheduleHydration=function(e){if(e){var t=Vf();e={blockedOn:null,target:e,priority:t};for(var n=0;n<jt.length&&t!==0&&t<jt[n].priority;n++);jt.splice(n,0,e),n===0&&Ff(e)}};function Oa(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Is(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Ec(){}function c0(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=gs(o);s.call(u)}}var o=rp(t,r,e,0,null,!1,!1,"",Ec);return e._reactRootContainer=o,e[mt]=o.current,Fr(e.nodeType===8?e.parentNode:e),un(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var l=r;r=function(){var u=gs(a);l.call(u)}}var a=Va(e,0,!1,null,null,!1,!1,"",Ec);return e._reactRootContainer=a,e[mt]=a.current,Fr(e.nodeType===8?e.parentNode:e),un(function(){Fs(t,a,n,r)}),a}function zs(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var l=i;i=function(){var a=gs(o);l.call(a)}}Fs(t,o,e,i)}else o=c0(n,t,e,i,r);return gs(o)}Lf=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=cr(t.pendingLanes);n!==0&&(ra(t,n|1),Ne(t,ee()),!(O&6)&&($n=ee()+500,Ut()))}break;case 13:un(function(){var r=yt(e,1);if(r!==null){var i=we();Xe(r,e,1,i)}}),ba(e,1)}};ia=function(e){if(e.tag===13){var t=yt(e,134217728);if(t!==null){var n=we();Xe(t,e,134217728,n)}ba(e,134217728)}};Rf=function(e){if(e.tag===13){var t=Rt(e),n=yt(e,t);if(n!==null){var r=we();Xe(n,e,t,r)}ba(e,t)}};Vf=function(){return I};bf=function(e,t){var n=I;try{return I=e,t()}finally{I=n}};Yo=function(e,t,n){switch(t){case"input":if(Uo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=_s(r);if(!i)throw Error(T(90));hf(r),Uo(r,i)}}}break;case"textarea":mf(e,n);break;case"select":t=n.value,t!=null&&Dn(e,!!n.multiple,t,!1)}};kf=Da;Cf=un;var d0={usingClientEntryPoint:!1,Events:[ti,xn,_s,wf,Sf,Da]},or={findFiberByHostInstance:Zt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},f0={bundleType:or.bundleType,version:or.version,rendererPackageName:or.rendererPackageName,rendererConfig:or.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Pf(e),e===null?null:e.stateNode},findFiberByHostInstance:or.findFiberByHostInstance||u0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ei=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ei.isDisabled&&Ei.supportsFiber)try{Ps=Ei.inject(f0),nt=Ei}catch{}}Re.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=d0;Re.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Oa(t))throw Error(T(200));return a0(e,t,null,n)};Re.createRoot=function(e,t){if(!Oa(e))throw Error(T(299));var n=!1,r="",i=ip;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Va(e,1,!1,null,null,n,!1,r,i),e[mt]=t.current,Fr(e.nodeType===8?e.parentNode:e),new Fa(t)};Re.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(T(188)):(e=Object.keys(e).join(","),Error(T(268,e)));return e=Pf(t),e=e===null?null:e.stateNode,e};Re.flushSync=function(e){return un(e)};Re.hydrate=function(e,t,n){if(!Is(t))throw Error(T(200));return zs(null,e,t,!0,n)};Re.hydrateRoot=function(e,t,n){if(!Oa(e))throw Error(T(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=ip;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=rp(t,null,e,1,n??null,i,!1,s,o),e[mt]=t.current,Fr(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Os(t)};Re.render=function(e,t,n){if(!Is(t))throw Error(T(200));return zs(null,e,t,!1,n)};Re.unmountComponentAtNode=function(e){if(!Is(e))throw Error(T(40));return e._reactRootContainer?(un(function(){zs(null,null,e,!1,function(){e._reactRootContainer=null,e[mt]=null})}),!0):!1};Re.unstable_batchedUpdates=Da;Re.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Is(n))throw Error(T(200));if(e==null||e._reactInternals===void 0)throw Error(T(38));return zs(e,t,n,!1,r)};Re.version="18.3.1-next-f1338f8080-20240426";function sp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(sp)}catch(e){console.error(e)}}sp(),sf.exports=Re;var h0=sf.exports,Mc=h0;Vo.createRoot=Mc.createRoot,Vo.hydrateRoot=Mc.hydrateRoot;const Ia=P.createContext({});function za(e){const t=P.useRef(null);return t.current===null&&(t.current=e()),t.current}const Bs=P.createContext(null),Ba=P.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class p0 extends P.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function m0({children:e,isPresent:t}){const n=P.useId(),r=P.useRef(null),i=P.useRef({width:0,height:0,top:0,left:0}),{nonce:s}=P.useContext(Ba);return P.useInsertionEffect(()=>{const{width:o,height:l,top:a,left:u}=i.current;if(t||!r.current||!o||!l)return;r.current.dataset.motionPopId=n;const d=document.createElement("style");return s&&(d.nonce=s),document.head.appendChild(d),d.sheet&&d.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${l}px !important;
            top: ${a}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(d)}},[t]),c.jsx(p0,{isPresent:t,childRef:r,sizeRef:i,children:P.cloneElement(e,{ref:r})})}const y0=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:s,mode:o})=>{const l=za(g0),a=P.useId(),u=P.useCallback(f=>{l.set(f,!0);for(const h of l.values())if(!h)return;r&&r()},[l,r]),d=P.useMemo(()=>({id:a,initial:t,isPresent:n,custom:i,onExitComplete:u,register:f=>(l.set(f,!1),()=>l.delete(f))}),s?[Math.random(),u]:[n,u]);return P.useMemo(()=>{l.forEach((f,h)=>l.set(h,!1))},[n]),P.useEffect(()=>{!n&&!l.size&&r&&r()},[n]),o==="popLayout"&&(e=c.jsx(m0,{isPresent:n,children:e})),c.jsx(Bs.Provider,{value:d,children:e})};function g0(){return new Map}function op(e=!0){const t=P.useContext(Bs);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:i}=t,s=P.useId();P.useEffect(()=>{e&&i(s)},[e]);const o=P.useCallback(()=>e&&r&&r(s),[s,r,e]);return!n&&r?[!1,o]:[!0]}const Mi=e=>e.key||"";function _c(e){const t=[];return P.Children.forEach(e,n=>{P.isValidElement(n)&&t.push(n)}),t}const Ua=typeof window<"u",lp=Ua?P.useLayoutEffect:P.useEffect,v0=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:i=!0,mode:s="sync",propagate:o=!1})=>{const[l,a]=op(o),u=P.useMemo(()=>_c(e),[e]),d=o&&!l?[]:u.map(Mi),f=P.useRef(!0),h=P.useRef(u),y=za(()=>new Map),[v,x]=P.useState(u),[S,m]=P.useState(u);lp(()=>{f.current=!1,h.current=u;for(let w=0;w<S.length;w++){const k=Mi(S[w]);d.includes(k)?y.delete(k):y.get(k)!==!0&&y.set(k,!1)}},[S,d.length,d.join("-")]);const p=[];if(u!==v){let w=[...u];for(let k=0;k<S.length;k++){const j=S[k],N=Mi(j);d.includes(N)||(w.splice(k,0,j),p.push(j))}s==="wait"&&p.length&&(w=p),m(_c(w)),x(u);return}const{forceRender:g}=P.useContext(Ia);return c.jsx(c.Fragment,{children:S.map(w=>{const k=Mi(w),j=o&&!l?!1:u===S||d.includes(k),N=()=>{if(y.has(k))y.set(k,!0);else return;let C=!0;y.forEach(V=>{V||(C=!1)}),C&&(g==null||g(),m(h.current),o&&(a==null||a()),r&&r())};return c.jsx(y0,{isPresent:j,initial:!f.current||n?void 0:!1,custom:j?void 0:t,presenceAffectsLayout:i,mode:s,onExitComplete:j?void 0:N,children:w},k)})})},De=e=>e;let ap=De;function $a(e){let t;return()=>(t===void 0&&(t=e()),t)}const Hn=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},ft=e=>e*1e3,ht=e=>e/1e3,x0={useManualTiming:!1};function w0(e){let t=new Set,n=new Set,r=!1,i=!1;const s=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function l(u){s.has(u)&&(a.schedule(u),e()),u(o)}const a={schedule:(u,d=!1,f=!1)=>{const y=f&&r?t:n;return d&&s.add(u),y.has(u)||y.add(u),u},cancel:u=>{n.delete(u),s.delete(u)},process:u=>{if(o=u,r){i=!0;return}r=!0,[t,n]=[n,t],t.forEach(l),t.clear(),r=!1,i&&(i=!1,a.process(u))}};return a}const _i=["read","resolveKeyframes","update","preRender","render","postRender"],S0=40;function up(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},s=()=>n=!0,o=_i.reduce((m,p)=>(m[p]=w0(s),m),{}),{read:l,resolveKeyframes:a,update:u,preRender:d,render:f,postRender:h}=o,y=()=>{const m=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(m-i.timestamp,S0),1),i.timestamp=m,i.isProcessing=!0,l.process(i),a.process(i),u.process(i),d.process(i),f.process(i),h.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(y))},v=()=>{n=!0,r=!0,i.isProcessing||e(y)};return{schedule:_i.reduce((m,p)=>{const g=o[p];return m[p]=(w,k=!1,j=!1)=>(n||v(),g.schedule(w,k,j)),m},{}),cancel:m=>{for(let p=0;p<_i.length;p++)o[_i[p]].cancel(m)},state:i,steps:o}}const{schedule:H,cancel:Ot,state:ce,steps:Co}=up(typeof requestAnimationFrame<"u"?requestAnimationFrame:De,!0),cp=P.createContext({strict:!1}),Dc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Wn={};for(const e in Dc)Wn[e]={isEnabled:t=>Dc[e].some(n=>!!t[n])};function k0(e){for(const t in e)Wn[t]={...Wn[t],...e[t]}}const C0=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function vs(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||C0.has(e)}let dp=e=>!vs(e);function j0(e){e&&(dp=t=>t.startsWith("on")?!vs(t):e(t))}try{j0(require("@emotion/is-prop-valid").default)}catch{}function T0(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(dp(i)||n===!0&&vs(i)||!t&&!vs(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function P0(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,i)=>i==="create"?e:(t.has(i)||t.set(i,e(i)),t.get(i))})}const Us=P.createContext({});function Kr(e){return typeof e=="string"||Array.isArray(e)}function $s(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Ha=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Wa=["initial",...Ha];function Hs(e){return $s(e.animate)||Wa.some(t=>Kr(e[t]))}function fp(e){return!!(Hs(e)||e.variants)}function N0(e,t){if(Hs(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Kr(n)?n:void 0,animate:Kr(r)?r:void 0}}return e.inherit!==!1?t:{}}function E0(e){const{initial:t,animate:n}=N0(e,P.useContext(Us));return P.useMemo(()=>({initial:t,animate:n}),[Ac(t),Ac(n)])}function Ac(e){return Array.isArray(e)?e.join(" "):e}const M0=Symbol.for("motionComponentSymbol");function Pn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function _0(e,t,n){return P.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Pn(n)&&(n.current=r))},[t])}const Ka=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),D0="framerAppearId",hp="data-"+Ka(D0),{schedule:Ga}=up(queueMicrotask,!1),pp=P.createContext({});function A0(e,t,n,r,i){var s,o;const{visualElement:l}=P.useContext(Us),a=P.useContext(cp),u=P.useContext(Bs),d=P.useContext(Ba).reducedMotion,f=P.useRef(null);r=r||a.renderer,!f.current&&r&&(f.current=r(e,{visualState:t,parent:l,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:d}));const h=f.current,y=P.useContext(pp);h&&!h.projection&&i&&(h.type==="html"||h.type==="svg")&&L0(f.current,n,i,y);const v=P.useRef(!1);P.useInsertionEffect(()=>{h&&v.current&&h.update(n,u)});const x=n[hp],S=P.useRef(!!x&&!(!((s=window.MotionHandoffIsComplete)===null||s===void 0)&&s.call(window,x))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,x)));return lp(()=>{h&&(v.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),Ga.render(h.render),S.current&&h.animationState&&h.animationState.animateChanges())}),P.useEffect(()=>{h&&(!S.current&&h.animationState&&h.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{var m;(m=window.MotionHandoffMarkAsComplete)===null||m===void 0||m.call(window,x)}),S.current=!1))}),h}function L0(e,t,n,r){const{layoutId:i,layout:s,drag:o,dragConstraints:l,layoutScroll:a,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:mp(e.parent)),e.projection.setOptions({layoutId:i,layout:s,alwaysMeasureLayout:!!o||l&&Pn(l),visualElement:e,animationType:typeof s=="string"?s:"both",initialPromotionConfig:r,layoutScroll:a,layoutRoot:u})}function mp(e){if(e)return e.options.allowProjection!==!1?e.projection:mp(e.parent)}function R0({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){var s,o;e&&k0(e);function l(u,d){let f;const h={...P.useContext(Ba),...u,layoutId:V0(u)},{isStatic:y}=h,v=E0(u),x=r(u,y);if(!y&&Ua){b0();const S=F0(h);f=S.MeasureLayout,v.visualElement=A0(i,x,h,t,S.ProjectionNode)}return c.jsxs(Us.Provider,{value:v,children:[f&&v.visualElement?c.jsx(f,{visualElement:v.visualElement,...h}):null,n(i,u,_0(x,v.visualElement,d),x,y,v.visualElement)]})}l.displayName=`motion.${typeof i=="string"?i:`create(${(o=(s=i.displayName)!==null&&s!==void 0?s:i.name)!==null&&o!==void 0?o:""})`}`;const a=P.forwardRef(l);return a[M0]=i,a}function V0({layoutId:e}){const t=P.useContext(Ia).id;return t&&e!==void 0?t+"-"+e:e}function b0(e,t){P.useContext(cp).strict}function F0(e){const{drag:t,layout:n}=Wn;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const O0=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Qa(e){return typeof e!="string"||e.includes("-")?!1:!!(O0.indexOf(e)>-1||/[A-Z]/u.test(e))}function Lc(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function Ya(e,t,n,r){if(typeof t=="function"){const[i,s]=Lc(r);t=t(n!==void 0?n:e.custom,i,s)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[i,s]=Lc(r);t=t(n!==void 0?n:e.custom,i,s)}return t}const El=e=>Array.isArray(e),I0=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),z0=e=>El(e)?e[e.length-1]||0:e,ge=e=>!!(e&&e.getVelocity);function Wi(e){const t=ge(e)?e.get():e;return I0(t)?t.toValue():t}function B0({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,i,s){const o={latestValues:U0(r,i,s,e),renderState:t()};return n&&(o.onMount=l=>n({props:r,current:l,...o}),o.onUpdate=l=>n(l)),o}const yp=e=>(t,n)=>{const r=P.useContext(Us),i=P.useContext(Bs),s=()=>B0(e,t,r,i);return n?s():za(s)};function U0(e,t,n,r){const i={},s=r(e,{});for(const h in s)i[h]=Wi(s[h]);let{initial:o,animate:l}=e;const a=Hs(e),u=fp(e);t&&u&&!a&&e.inherit!==!1&&(o===void 0&&(o=t.initial),l===void 0&&(l=t.animate));let d=n?n.initial===!1:!1;d=d||o===!1;const f=d?l:o;if(f&&typeof f!="boolean"&&!$s(f)){const h=Array.isArray(f)?f:[f];for(let y=0;y<h.length;y++){const v=Ya(e,h[y]);if(v){const{transitionEnd:x,transition:S,...m}=v;for(const p in m){let g=m[p];if(Array.isArray(g)){const w=d?g.length-1:0;g=g[w]}g!==null&&(i[p]=g)}for(const p in x)i[p]=x[p]}}}return i}const Zn=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],fn=new Set(Zn),gp=e=>t=>typeof t=="string"&&t.startsWith(e),vp=gp("--"),$0=gp("var(--"),Xa=e=>$0(e)?H0.test(e.split("/*")[0].trim()):!1,H0=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,xp=(e,t)=>t&&typeof e=="number"?t.transform(e):e,vt=(e,t,n)=>n>t?t:n<e?e:n,qn={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Gr={...qn,transform:e=>vt(0,1,e)},Di={...qn,default:1},ri=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),St=ri("deg"),it=ri("%"),A=ri("px"),W0=ri("vh"),K0=ri("vw"),Rc={...it,parse:e=>it.parse(e)/100,transform:e=>it.transform(e*100)},G0={borderWidth:A,borderTopWidth:A,borderRightWidth:A,borderBottomWidth:A,borderLeftWidth:A,borderRadius:A,radius:A,borderTopLeftRadius:A,borderTopRightRadius:A,borderBottomRightRadius:A,borderBottomLeftRadius:A,width:A,maxWidth:A,height:A,maxHeight:A,top:A,right:A,bottom:A,left:A,padding:A,paddingTop:A,paddingRight:A,paddingBottom:A,paddingLeft:A,margin:A,marginTop:A,marginRight:A,marginBottom:A,marginLeft:A,backgroundPositionX:A,backgroundPositionY:A},Q0={rotate:St,rotateX:St,rotateY:St,rotateZ:St,scale:Di,scaleX:Di,scaleY:Di,scaleZ:Di,skew:St,skewX:St,skewY:St,distance:A,translateX:A,translateY:A,translateZ:A,x:A,y:A,z:A,perspective:A,transformPerspective:A,opacity:Gr,originX:Rc,originY:Rc,originZ:A},Vc={...qn,transform:Math.round},Za={...G0,...Q0,zIndex:Vc,size:A,fillOpacity:Gr,strokeOpacity:Gr,numOctaves:Vc},Y0={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},X0=Zn.length;function Z0(e,t,n){let r="",i=!0;for(let s=0;s<X0;s++){const o=Zn[s],l=e[o];if(l===void 0)continue;let a=!0;if(typeof l=="number"?a=l===(o.startsWith("scale")?1:0):a=parseFloat(l)===0,!a||n){const u=xp(l,Za[o]);if(!a){i=!1;const d=Y0[o]||o;r+=`${d}(${u}) `}n&&(t[o]=u)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}function qa(e,t,n){const{style:r,vars:i,transformOrigin:s}=e;let o=!1,l=!1;for(const a in t){const u=t[a];if(fn.has(a)){o=!0;continue}else if(vp(a)){i[a]=u;continue}else{const d=xp(u,Za[a]);a.startsWith("origin")?(l=!0,s[a]=d):r[a]=d}}if(t.transform||(o||n?r.transform=Z0(t,e.transform,n):r.transform&&(r.transform="none")),l){const{originX:a="50%",originY:u="50%",originZ:d=0}=s;r.transformOrigin=`${a} ${u} ${d}`}}const q0={offset:"stroke-dashoffset",array:"stroke-dasharray"},J0={offset:"strokeDashoffset",array:"strokeDasharray"};function ev(e,t,n=1,r=0,i=!0){e.pathLength=1;const s=i?q0:J0;e[s.offset]=A.transform(-r);const o=A.transform(t),l=A.transform(n);e[s.array]=`${o} ${l}`}function bc(e,t,n){return typeof e=="string"?e:A.transform(t+n*e)}function tv(e,t,n){const r=bc(t,e.x,e.width),i=bc(n,e.y,e.height);return`${r} ${i}`}function Ja(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:s,pathLength:o,pathSpacing:l=1,pathOffset:a=0,...u},d,f){if(qa(e,u,f),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:h,style:y,dimensions:v}=e;h.transform&&(v&&(y.transform=h.transform),delete h.transform),v&&(i!==void 0||s!==void 0||y.transform)&&(y.transformOrigin=tv(v,i!==void 0?i:.5,s!==void 0?s:.5)),t!==void 0&&(h.x=t),n!==void 0&&(h.y=n),r!==void 0&&(h.scale=r),o!==void 0&&ev(h,o,l,a,!1)}const eu=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),wp=()=>({...eu(),attrs:{}}),tu=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Sp(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const s in n)e.style.setProperty(s,n[s])}const kp=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Cp(e,t,n,r){Sp(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(kp.has(i)?i:Ka(i),t.attrs[i])}const xs={};function nv(e){Object.assign(xs,e)}function jp(e,{layout:t,layoutId:n}){return fn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!xs[e]||e==="opacity")}function nu(e,t,n){var r;const{style:i}=e,s={};for(const o in i)(ge(i[o])||t.style&&ge(t.style[o])||jp(o,e)||((r=n==null?void 0:n.getValue(o))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(s[o]=i[o]);return s}function Tp(e,t,n){const r=nu(e,t,n);for(const i in e)if(ge(e[i])||ge(t[i])){const s=Zn.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;r[s]=e[i]}return r}function rv(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const Fc=["x","y","width","height","cx","cy","r"],iv={useVisualState:yp({scrapeMotionValuesFromProps:Tp,createRenderState:wp,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:i})=>{if(!n)return;let s=!!e.drag;if(!s){for(const l in i)if(fn.has(l)){s=!0;break}}if(!s)return;let o=!t;if(t)for(let l=0;l<Fc.length;l++){const a=Fc[l];e[a]!==t[a]&&(o=!0)}o&&H.read(()=>{rv(n,r),H.render(()=>{Ja(r,i,tu(n.tagName),e.transformTemplate),Cp(n,r)})})}})},sv={useVisualState:yp({scrapeMotionValuesFromProps:nu,createRenderState:eu})};function Pp(e,t,n){for(const r in t)!ge(t[r])&&!jp(r,n)&&(e[r]=t[r])}function ov({transformTemplate:e},t){return P.useMemo(()=>{const n=eu();return qa(n,t,e),Object.assign({},n.vars,n.style)},[t])}function lv(e,t){const n=e.style||{},r={};return Pp(r,n,e),Object.assign(r,ov(e,t)),r}function av(e,t){const n={},r=lv(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function uv(e,t,n,r){const i=P.useMemo(()=>{const s=wp();return Ja(s,t,tu(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};Pp(s,e.style,e),i.style={...s,...i.style}}return i}function cv(e=!1){return(n,r,i,{latestValues:s},o)=>{const a=(Qa(n)?uv:av)(r,s,o,n),u=T0(r,typeof n=="string",e),d=n!==P.Fragment?{...u,...a,ref:i}:{},{children:f}=r,h=P.useMemo(()=>ge(f)?f.get():f,[f]);return P.createElement(n,{...d,children:h})}}function dv(e,t){return function(r,{forwardMotionProps:i}={forwardMotionProps:!1}){const o={...Qa(r)?iv:sv,preloadedFeatures:e,useRender:cv(i),createVisualElement:t,Component:r};return R0(o)}}function Np(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Ws(e,t,n){const r=e.getProps();return Ya(r,t,n!==void 0?n:r.custom,e)}const fv=$a(()=>window.ScrollTimeline!==void 0);class hv{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(i=>{if(fv()&&i.attachTimeline)return i.attachTimeline(t);if(typeof n=="function")return n(i)});return()=>{r.forEach((i,s)=>{i&&i(),this.animations[s].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class pv extends hv{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function ru(e,t){return e?e[t]||e.default||e:void 0}const Ml=2e4;function Ep(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Ml;)t+=n,r=e.next(t);return t>=Ml?1/0:t}function iu(e){return typeof e=="function"}function Oc(e,t){e.timeline=t,e.onfinish=null}const su=e=>Array.isArray(e)&&typeof e[0]=="number",mv={linearEasing:void 0};function yv(e,t){const n=$a(e);return()=>{var r;return(r=mv[t])!==null&&r!==void 0?r:n()}}const ws=yv(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Mp=(e,t,n=10)=>{let r="";const i=Math.max(Math.round(t/n),2);for(let s=0;s<i;s++)r+=e(Hn(0,i-1,s))+", ";return`linear(${r.substring(0,r.length-2)})`};function _p(e){return!!(typeof e=="function"&&ws()||!e||typeof e=="string"&&(e in _l||ws())||su(e)||Array.isArray(e)&&e.every(_p))}const fr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,_l={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:fr([0,.65,.55,1]),circOut:fr([.55,0,1,.45]),backIn:fr([.31,.01,.66,-.59]),backOut:fr([.33,1.53,.69,.99])};function Dp(e,t){if(e)return typeof e=="function"&&ws()?Mp(e,t):su(e)?fr(e):Array.isArray(e)?e.map(n=>Dp(n,t)||_l.easeOut):_l[e]}const We={x:!1,y:!1};function Ap(){return We.x||We.y}function gv(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let i=document;const s=(r=void 0)!==null&&r!==void 0?r:i.querySelectorAll(e);return s?Array.from(s):[]}return Array.from(e)}function Lp(e,t){const n=gv(e),r=new AbortController,i={passive:!0,...t,signal:r.signal};return[n,i,()=>r.abort()]}function Ic(e){return t=>{t.pointerType==="touch"||Ap()||e(t)}}function vv(e,t,n={}){const[r,i,s]=Lp(e,n),o=Ic(l=>{const{target:a}=l,u=t(l);if(typeof u!="function"||!a)return;const d=Ic(f=>{u(f),a.removeEventListener("pointerleave",d)});a.addEventListener("pointerleave",d,i)});return r.forEach(l=>{l.addEventListener("pointerenter",o,i)}),s}const Rp=(e,t)=>t?e===t?!0:Rp(e,t.parentElement):!1,ou=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,xv=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function wv(e){return xv.has(e.tagName)||e.tabIndex!==-1}const hr=new WeakSet;function zc(e){return t=>{t.key==="Enter"&&e(t)}}function jo(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const Sv=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=zc(()=>{if(hr.has(n))return;jo(n,"down");const i=zc(()=>{jo(n,"up")}),s=()=>jo(n,"cancel");n.addEventListener("keyup",i,t),n.addEventListener("blur",s,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function Bc(e){return ou(e)&&!Ap()}function kv(e,t,n={}){const[r,i,s]=Lp(e,n),o=l=>{const a=l.currentTarget;if(!Bc(l)||hr.has(a))return;hr.add(a);const u=t(l),d=(y,v)=>{window.removeEventListener("pointerup",f),window.removeEventListener("pointercancel",h),!(!Bc(y)||!hr.has(a))&&(hr.delete(a),typeof u=="function"&&u(y,{success:v}))},f=y=>{d(y,n.useGlobalTarget||Rp(a,y.target))},h=y=>{d(y,!1)};window.addEventListener("pointerup",f,i),window.addEventListener("pointercancel",h,i)};return r.forEach(l=>{!wv(l)&&l.getAttribute("tabindex")===null&&(l.tabIndex=0),(n.useGlobalTarget?window:l).addEventListener("pointerdown",o,i),l.addEventListener("focus",u=>Sv(u,i),i)}),s}function Cv(e){return e==="x"||e==="y"?We[e]?null:(We[e]=!0,()=>{We[e]=!1}):We.x||We.y?null:(We.x=We.y=!0,()=>{We.x=We.y=!1})}const Vp=new Set(["width","height","top","left","right","bottom",...Zn]);let Ki;function jv(){Ki=void 0}const st={now:()=>(Ki===void 0&&st.set(ce.isProcessing||x0.useManualTiming?ce.timestamp:performance.now()),Ki),set:e=>{Ki=e,queueMicrotask(jv)}};function lu(e,t){e.indexOf(t)===-1&&e.push(t)}function au(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class uu{constructor(){this.subscriptions=[]}add(t){return lu(this.subscriptions,t),()=>au(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let s=0;s<i;s++){const o=this.subscriptions[s];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function bp(e,t){return t?e*(1e3/t):0}const Uc=30,Tv=e=>!isNaN(parseFloat(e));class Pv{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,i=!0)=>{const s=st.now();this.updatedAt!==s&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=st.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=Tv(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new uu);const r=this.events[t].add(n);return t==="change"?()=>{r(),H.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=st.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Uc)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Uc);return bp(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Qr(e,t){return new Pv(e,t)}function Nv(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Qr(n))}function Ev(e,t){const n=Ws(e,t);let{transitionEnd:r={},transition:i={},...s}=n||{};s={...s,...r};for(const o in s){const l=z0(s[o]);Nv(e,o,l)}}function Mv(e){return!!(ge(e)&&e.add)}function Dl(e,t){const n=e.getValue("willChange");if(Mv(n))return n.add(t)}function Fp(e){return e.props[hp]}const Op=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,_v=1e-7,Dv=12;function Av(e,t,n,r,i){let s,o,l=0;do o=t+(n-t)/2,s=Op(o,r,i)-e,s>0?n=o:t=o;while(Math.abs(s)>_v&&++l<Dv);return o}function ii(e,t,n,r){if(e===t&&n===r)return De;const i=s=>Av(s,0,1,e,n);return s=>s===0||s===1?s:Op(i(s),t,r)}const Ip=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,zp=e=>t=>1-e(1-t),Bp=ii(.33,1.53,.69,.99),cu=zp(Bp),Up=Ip(cu),$p=e=>(e*=2)<1?.5*cu(e):.5*(2-Math.pow(2,-10*(e-1))),du=e=>1-Math.sin(Math.acos(e)),Hp=zp(du),Wp=Ip(du),Kp=e=>/^0[^.\s]+$/u.test(e);function Lv(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||Kp(e):!0}const jr=e=>Math.round(e*1e5)/1e5,fu=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Rv(e){return e==null}const Vv=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,hu=(e,t)=>n=>!!(typeof n=="string"&&Vv.test(n)&&n.startsWith(e)||t&&!Rv(n)&&Object.prototype.hasOwnProperty.call(n,t)),Gp=(e,t,n)=>r=>{if(typeof r!="string")return r;const[i,s,o,l]=r.match(fu);return{[e]:parseFloat(i),[t]:parseFloat(s),[n]:parseFloat(o),alpha:l!==void 0?parseFloat(l):1}},bv=e=>vt(0,255,e),To={...qn,transform:e=>Math.round(bv(e))},en={test:hu("rgb","red"),parse:Gp("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+To.transform(e)+", "+To.transform(t)+", "+To.transform(n)+", "+jr(Gr.transform(r))+")"};function Fv(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Al={test:hu("#"),parse:Fv,transform:en.transform},Nn={test:hu("hsl","hue"),parse:Gp("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+it.transform(jr(t))+", "+it.transform(jr(n))+", "+jr(Gr.transform(r))+")"},me={test:e=>en.test(e)||Al.test(e)||Nn.test(e),parse:e=>en.test(e)?en.parse(e):Nn.test(e)?Nn.parse(e):Al.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?en.transform(e):Nn.transform(e)},Ov=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Iv(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(fu))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(Ov))===null||n===void 0?void 0:n.length)||0)>0}const Qp="number",Yp="color",zv="var",Bv="var(",$c="${}",Uv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Yr(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[];let s=0;const l=t.replace(Uv,a=>(me.test(a)?(r.color.push(s),i.push(Yp),n.push(me.parse(a))):a.startsWith(Bv)?(r.var.push(s),i.push(zv),n.push(a)):(r.number.push(s),i.push(Qp),n.push(parseFloat(a))),++s,$c)).split($c);return{values:n,split:l,indexes:r,types:i}}function Xp(e){return Yr(e).values}function Zp(e){const{split:t,types:n}=Yr(e),r=t.length;return i=>{let s="";for(let o=0;o<r;o++)if(s+=t[o],i[o]!==void 0){const l=n[o];l===Qp?s+=jr(i[o]):l===Yp?s+=me.transform(i[o]):s+=i[o]}return s}}const $v=e=>typeof e=="number"?0:e;function Hv(e){const t=Xp(e);return Zp(e)(t.map($v))}const It={test:Iv,parse:Xp,createTransformer:Zp,getAnimatableNone:Hv},Wv=new Set(["brightness","contrast","saturate","opacity"]);function Kv(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(fu)||[];if(!r)return e;const i=n.replace(r,"");let s=Wv.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+i+")"}const Gv=/\b([a-z-]*)\(.*?\)/gu,Ll={...It,getAnimatableNone:e=>{const t=e.match(Gv);return t?t.map(Kv).join(" "):e}},Qv={...Za,color:me,backgroundColor:me,outlineColor:me,fill:me,stroke:me,borderColor:me,borderTopColor:me,borderRightColor:me,borderBottomColor:me,borderLeftColor:me,filter:Ll,WebkitFilter:Ll},pu=e=>Qv[e];function qp(e,t){let n=pu(e);return n!==Ll&&(n=It),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Yv=new Set(["auto","none","0"]);function Xv(e,t,n){let r=0,i;for(;r<e.length&&!i;){const s=e[r];typeof s=="string"&&!Yv.has(s)&&Yr(s).values.length&&(i=e[r]),r++}if(i&&n)for(const s of t)e[s]=qp(n,i)}const Hc=e=>e===qn||e===A,Wc=(e,t)=>parseFloat(e.split(", ")[t]),Kc=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/u);if(i)return Wc(i[1],t);{const s=r.match(/^matrix\((.+)\)$/u);return s?Wc(s[1],e):0}},Zv=new Set(["x","y","z"]),qv=Zn.filter(e=>!Zv.has(e));function Jv(e){const t=[];return qv.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const Kn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:Kc(4,13),y:Kc(5,14)};Kn.translateX=Kn.x;Kn.translateY=Kn.y;const rn=new Set;let Rl=!1,Vl=!1;function Jp(){if(Vl){const e=Array.from(rn).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const i=Jv(r);i.length&&(n.set(r,i),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const i=n.get(r);i&&i.forEach(([s,o])=>{var l;(l=r.getValue(s))===null||l===void 0||l.set(o)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}Vl=!1,Rl=!1,rn.forEach(e=>e.complete()),rn.clear()}function em(){rn.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(Vl=!0)})}function ex(){em(),Jp()}class mu{constructor(t,n,r,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(rn.add(this),Rl||(Rl=!0,H.read(em),H.resolveKeyframes(Jp))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:i}=this;for(let s=0;s<t.length;s++)if(t[s]===null)if(s===0){const o=i==null?void 0:i.get(),l=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const a=r.readValue(n,l);a!=null&&(t[0]=a)}t[0]===void 0&&(t[0]=l),i&&o===void 0&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),rn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,rn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const tm=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),tx=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function nx(e){const t=tx.exec(e);if(!t)return[,];const[,n,r,i]=t;return[`--${n??r}`,i]}function nm(e,t,n=1){const[r,i]=nx(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const o=s.trim();return tm(o)?parseFloat(o):o}return Xa(i)?nm(i,t,n+1):i}const rm=e=>t=>t.test(e),rx={test:e=>e==="auto",parse:e=>e},im=[qn,A,it,St,K0,W0,rx],Gc=e=>im.find(rm(e));class sm extends mu{constructor(t,n,r,i,s){super(t,n,r,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="string"&&(u=u.trim(),Xa(u))){const d=nm(u,n.current);d!==void 0&&(t[a]=d),a===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!Vp.has(r)||t.length!==2)return;const[i,s]=t,o=Gc(i),l=Gc(s);if(o!==l)if(Hc(o)&&Hc(l))for(let a=0;a<t.length;a++){const u=t[a];typeof u=="string"&&(t[a]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let i=0;i<t.length;i++)Lv(t[i])&&r.push(i);r.length&&Xv(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Kn[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&t.getValue(r,i).jump(i,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:i}=this;if(!n||!n.current)return;const s=n.getValue(r);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,l=i[o];i[o]=Kn[r](n.measureViewportBox(),window.getComputedStyle(n.current)),l!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=l),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([a,u])=>{n.getValue(a).set(u)}),this.resolveNoneKeyframes()}}const Qc=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(It.test(e)||e==="0")&&!e.startsWith("url("));function ix(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function sx(e,t,n,r){const i=e[0];if(i===null)return!1;if(t==="display"||t==="visibility")return!0;const s=e[e.length-1],o=Qc(i,t),l=Qc(s,t);return!o||!l?!1:ix(e)||(n==="spring"||iu(n))&&r}const ox=e=>e!==null;function Ks(e,{repeat:t,repeatType:n="loop"},r){const i=e.filter(ox),s=t&&n!=="loop"&&t%2===1?0:i.length-1;return!s||r===void 0?i[s]:r}const lx=40;class om{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...l}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=st.now(),this.options={autoplay:t,delay:n,type:r,repeat:i,repeatDelay:s,repeatType:o,...l},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>lx?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&ex(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=st.now(),this.hasAttemptedResolve=!0;const{name:r,type:i,velocity:s,delay:o,onComplete:l,onUpdate:a,isGenerator:u}=this.options;if(!u&&!sx(t,r,i,s))if(o)this.options.duration=0;else{a&&a(Ks(t,this.options,n)),l&&l(),this.resolveFinishedPromise();return}const d=this.initPlayback(t,n);d!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...d},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const G=(e,t,n)=>e+(t-e)*n;function Po(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function ax({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,s=0,o=0;if(!t)i=s=o=n;else{const l=n<.5?n*(1+t):n+t-n*t,a=2*n-l;i=Po(a,l,e+1/3),s=Po(a,l,e),o=Po(a,l,e-1/3)}return{red:Math.round(i*255),green:Math.round(s*255),blue:Math.round(o*255),alpha:r}}function Ss(e,t){return n=>n>0?t:e}const No=(e,t,n)=>{const r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},ux=[Al,en,Nn],cx=e=>ux.find(t=>t.test(e));function Yc(e){const t=cx(e);if(!t)return!1;let n=t.parse(e);return t===Nn&&(n=ax(n)),n}const Xc=(e,t)=>{const n=Yc(e),r=Yc(t);if(!n||!r)return Ss(e,t);const i={...n};return s=>(i.red=No(n.red,r.red,s),i.green=No(n.green,r.green,s),i.blue=No(n.blue,r.blue,s),i.alpha=G(n.alpha,r.alpha,s),en.transform(i))},dx=(e,t)=>n=>t(e(n)),si=(...e)=>e.reduce(dx),bl=new Set(["none","hidden"]);function fx(e,t){return bl.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function hx(e,t){return n=>G(e,t,n)}function yu(e){return typeof e=="number"?hx:typeof e=="string"?Xa(e)?Ss:me.test(e)?Xc:yx:Array.isArray(e)?lm:typeof e=="object"?me.test(e)?Xc:px:Ss}function lm(e,t){const n=[...e],r=n.length,i=e.map((s,o)=>yu(s)(s,t[o]));return s=>{for(let o=0;o<r;o++)n[o]=i[o](s);return n}}function px(e,t){const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=yu(e[i])(e[i],t[i]));return i=>{for(const s in r)n[s]=r[s](i);return n}}function mx(e,t){var n;const r=[],i={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){const o=t.types[s],l=e.indexes[o][i[o]],a=(n=e.values[l])!==null&&n!==void 0?n:0;r[s]=a,i[o]++}return r}const yx=(e,t)=>{const n=It.createTransformer(t),r=Yr(e),i=Yr(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?bl.has(e)&&!i.values.length||bl.has(t)&&!r.values.length?fx(e,t):si(lm(mx(r,i),i.values),n):Ss(e,t)};function am(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?G(e,t,n):yu(e)(e,t)}const gx=5;function um(e,t,n){const r=Math.max(t-gx,0);return bp(n-e(r),t-r)}const X={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Zc=.001;function vx({duration:e=X.duration,bounce:t=X.bounce,velocity:n=X.velocity,mass:r=X.mass}){let i,s,o=1-t;o=vt(X.minDamping,X.maxDamping,o),e=vt(X.minDuration,X.maxDuration,ht(e)),o<1?(i=u=>{const d=u*o,f=d*e,h=d-n,y=Fl(u,o),v=Math.exp(-f);return Zc-h/y*v},s=u=>{const f=u*o*e,h=f*n+n,y=Math.pow(o,2)*Math.pow(u,2)*e,v=Math.exp(-f),x=Fl(Math.pow(u,2),o);return(-i(u)+Zc>0?-1:1)*((h-y)*v)/x}):(i=u=>{const d=Math.exp(-u*e),f=(u-n)*e+1;return-.001+d*f},s=u=>{const d=Math.exp(-u*e),f=(n-u)*(e*e);return d*f});const l=5/e,a=wx(i,s,l);if(e=ft(e),isNaN(a))return{stiffness:X.stiffness,damping:X.damping,duration:e};{const u=Math.pow(a,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const xx=12;function wx(e,t,n){let r=n;for(let i=1;i<xx;i++)r=r-e(r)/t(r);return r}function Fl(e,t){return e*Math.sqrt(1-t*t)}const Sx=["duration","bounce"],kx=["stiffness","damping","mass"];function qc(e,t){return t.some(n=>e[n]!==void 0)}function Cx(e){let t={velocity:X.velocity,stiffness:X.stiffness,damping:X.damping,mass:X.mass,isResolvedFromDuration:!1,...e};if(!qc(e,kx)&&qc(e,Sx))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),i=r*r,s=2*vt(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:X.mass,stiffness:i,damping:s}}else{const n=vx(e);t={...t,...n,mass:X.mass},t.isResolvedFromDuration=!0}return t}function cm(e=X.visualDuration,t=X.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:i}=n;const s=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],l={done:!1,value:s},{stiffness:a,damping:u,mass:d,duration:f,velocity:h,isResolvedFromDuration:y}=Cx({...n,velocity:-ht(n.velocity||0)}),v=h||0,x=u/(2*Math.sqrt(a*d)),S=o-s,m=ht(Math.sqrt(a/d)),p=Math.abs(S)<5;r||(r=p?X.restSpeed.granular:X.restSpeed.default),i||(i=p?X.restDelta.granular:X.restDelta.default);let g;if(x<1){const k=Fl(m,x);g=j=>{const N=Math.exp(-x*m*j);return o-N*((v+x*m*S)/k*Math.sin(k*j)+S*Math.cos(k*j))}}else if(x===1)g=k=>o-Math.exp(-m*k)*(S+(v+m*S)*k);else{const k=m*Math.sqrt(x*x-1);g=j=>{const N=Math.exp(-x*m*j),C=Math.min(k*j,300);return o-N*((v+x*m*S)*Math.sinh(C)+k*S*Math.cosh(C))/k}}const w={calculatedDuration:y&&f||null,next:k=>{const j=g(k);if(y)l.done=k>=f;else{let N=0;x<1&&(N=k===0?ft(v):um(g,k,j));const C=Math.abs(N)<=r,V=Math.abs(o-j)<=i;l.done=C&&V}return l.value=l.done?o:j,l},toString:()=>{const k=Math.min(Ep(w),Ml),j=Mp(N=>w.next(k*N).value,k,30);return k+"ms "+j}};return w}function Jc({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:l,max:a,restDelta:u=.5,restSpeed:d}){const f=e[0],h={done:!1,value:f},y=C=>l!==void 0&&C<l||a!==void 0&&C>a,v=C=>l===void 0?a:a===void 0||Math.abs(l-C)<Math.abs(a-C)?l:a;let x=n*t;const S=f+x,m=o===void 0?S:o(S);m!==S&&(x=m-f);const p=C=>-x*Math.exp(-C/r),g=C=>m+p(C),w=C=>{const V=p(C),D=g(C);h.done=Math.abs(V)<=u,h.value=h.done?m:D};let k,j;const N=C=>{y(h.value)&&(k=C,j=cm({keyframes:[h.value,v(h.value)],velocity:um(g,C,h.value),damping:i,stiffness:s,restDelta:u,restSpeed:d}))};return N(0),{calculatedDuration:null,next:C=>{let V=!1;return!j&&k===void 0&&(V=!0,w(C),N(C)),k!==void 0&&C>=k?j.next(C-k):(!V&&w(C),h)}}}const jx=ii(.42,0,1,1),Tx=ii(0,0,.58,1),dm=ii(.42,0,.58,1),Px=e=>Array.isArray(e)&&typeof e[0]!="number",Nx={linear:De,easeIn:jx,easeInOut:dm,easeOut:Tx,circIn:du,circInOut:Wp,circOut:Hp,backIn:cu,backInOut:Up,backOut:Bp,anticipate:$p},ed=e=>{if(su(e)){ap(e.length===4);const[t,n,r,i]=e;return ii(t,n,r,i)}else if(typeof e=="string")return Nx[e];return e};function Ex(e,t,n){const r=[],i=n||am,s=e.length-1;for(let o=0;o<s;o++){let l=i(e[o],e[o+1]);if(t){const a=Array.isArray(t)?t[o]||De:t;l=si(a,l)}r.push(l)}return r}function Mx(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const s=e.length;if(ap(s===t.length),s===1)return()=>t[0];if(s===2&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const l=Ex(t,r,i),a=l.length,u=d=>{if(o&&d<e[0])return t[0];let f=0;if(a>1)for(;f<e.length-2&&!(d<e[f+1]);f++);const h=Hn(e[f],e[f+1],d);return l[f](h)};return n?d=>u(vt(e[0],e[s-1],d)):u}function _x(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=Hn(0,t,r);e.push(G(n,1,i))}}function Dx(e){const t=[0];return _x(t,e.length-1),t}function Ax(e,t){return e.map(n=>n*t)}function Lx(e,t){return e.map(()=>t||dm).splice(0,e.length-1)}function ks({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=Px(r)?r.map(ed):ed(r),s={done:!1,value:t[0]},o=Ax(n&&n.length===t.length?n:Dx(t),e),l=Mx(o,t,{ease:Array.isArray(i)?i:Lx(t,i)});return{calculatedDuration:e,next:a=>(s.value=l(a),s.done=a>=e,s)}}const Rx=e=>{const t=({timestamp:n})=>e(n);return{start:()=>H.update(t,!0),stop:()=>Ot(t),now:()=>ce.isProcessing?ce.timestamp:st.now()}},Vx={decay:Jc,inertia:Jc,tween:ks,keyframes:ks,spring:cm},bx=e=>e/100;class gu extends om{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:a}=this.options;a&&a()};const{name:n,motionValue:r,element:i,keyframes:s}=this.options,o=(i==null?void 0:i.KeyframeResolver)||mu,l=(a,u)=>this.onKeyframesResolved(a,u);this.resolver=new o(s,l,n,r,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:s,velocity:o=0}=this.options,l=iu(n)?n:Vx[n]||ks;let a,u;l!==ks&&typeof t[0]!="number"&&(a=si(bx,am(t[0],t[1])),t=[0,100]);const d=l({...this.options,keyframes:t});s==="mirror"&&(u=l({...this.options,keyframes:[...t].reverse(),velocity:-o})),d.calculatedDuration===null&&(d.calculatedDuration=Ep(d));const{calculatedDuration:f}=d,h=f+i,y=h*(r+1)-i;return{generator:d,mirroredGenerator:u,mapPercentToKeyframes:a,calculatedDuration:f,resolvedDuration:h,totalDuration:y}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:C}=this.options;return{done:!0,value:C[C.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:l,keyframes:a,calculatedDuration:u,totalDuration:d,resolvedDuration:f}=r;if(this.startTime===null)return s.next(0);const{delay:h,repeat:y,repeatType:v,repeatDelay:x,onUpdate:S}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-d/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const m=this.currentTime-h*(this.speed>=0?1:-1),p=this.speed>=0?m<0:m>d;this.currentTime=Math.max(m,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=d);let g=this.currentTime,w=s;if(y){const C=Math.min(this.currentTime,d)/f;let V=Math.floor(C),D=C%1;!D&&C>=1&&(D=1),D===1&&V--,V=Math.min(V,y+1),!!(V%2)&&(v==="reverse"?(D=1-D,x&&(D-=x/f)):v==="mirror"&&(w=o)),g=vt(0,1,D)*f}const k=p?{done:!1,value:a[0]}:w.next(g);l&&(k.value=l(k.value));let{done:j}=k;!p&&u!==null&&(j=this.speed>=0?this.currentTime>=d:this.currentTime<=0);const N=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&j);return N&&i!==void 0&&(k.value=Ks(a,this.options,i)),S&&S(k.value),N&&this.finish(),k}get duration(){const{resolved:t}=this;return t?ht(t.calculatedDuration):0}get time(){return ht(this.currentTime)}set time(t){t=ft(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=ht(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=Rx,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(s=>this.tick(s))),n&&n();const i=this.driver.now();this.holdTime!==null?this.startTime=i-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=i):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const Fx=new Set(["opacity","clipPath","filter","transform"]);function Ox(e,t,n,{delay:r=0,duration:i=300,repeat:s=0,repeatType:o="loop",ease:l="easeInOut",times:a}={}){const u={[t]:n};a&&(u.offset=a);const d=Dp(l,i);return Array.isArray(d)&&(u.easing=d),e.animate(u,{delay:r,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:o==="reverse"?"alternate":"normal"})}const Ix=$a(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),Cs=10,zx=2e4;function Bx(e){return iu(e.type)||e.type==="spring"||!_p(e.ease)}function Ux(e,t){const n=new gu({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const i=[];let s=0;for(;!r.done&&s<zx;)r=n.sample(s),i.push(r.value),s+=Cs;return{times:void 0,keyframes:i,duration:s-Cs,ease:"linear"}}const fm={anticipate:$p,backInOut:Up,circInOut:Wp};function $x(e){return e in fm}class td extends om{constructor(t){super(t);const{name:n,motionValue:r,element:i,keyframes:s}=this.options;this.resolver=new sm(s,(o,l)=>this.onKeyframesResolved(o,l),n,r,i),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:i,ease:s,type:o,motionValue:l,name:a,startTime:u}=this.options;if(!l.owner||!l.owner.current)return!1;if(typeof s=="string"&&ws()&&$x(s)&&(s=fm[s]),Bx(this.options)){const{onComplete:f,onUpdate:h,motionValue:y,element:v,...x}=this.options,S=Ux(t,x);t=S.keyframes,t.length===1&&(t[1]=t[0]),r=S.duration,i=S.times,s=S.ease,o="keyframes"}const d=Ox(l.owner.current,a,t,{...this.options,duration:r,times:i,ease:s});return d.startTime=u??this.calcStartTime(),this.pendingTimeline?(Oc(d,this.pendingTimeline),this.pendingTimeline=void 0):d.onfinish=()=>{const{onComplete:f}=this.options;l.set(Ks(t,this.options,n)),f&&f(),this.cancel(),this.resolveFinishedPromise()},{animation:d,duration:r,times:i,type:o,ease:s,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return ht(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return ht(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=ft(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return De;const{animation:r}=n;Oc(r,t)}return De}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:i,type:s,ease:o,times:l}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:d,onComplete:f,element:h,...y}=this.options,v=new gu({...y,keyframes:r,duration:i,type:s,ease:o,times:l,isGenerator:!0}),x=ft(this.time);u.setWithVelocity(v.sample(x-Cs).value,v.sample(x).value,Cs)}const{onStop:a}=this.options;a&&a(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:i,repeatType:s,damping:o,type:l}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:u}=n.owner.getProps();return Ix()&&r&&Fx.has(r)&&!a&&!u&&!i&&s!=="mirror"&&o!==0&&l!=="inertia"}}const Hx={type:"spring",stiffness:500,damping:25,restSpeed:10},Wx=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),Kx={type:"keyframes",duration:.8},Gx={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Qx=(e,{keyframes:t})=>t.length>2?Kx:fn.has(e)?e.startsWith("scale")?Wx(t[1]):Hx:Gx;function Yx({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:l,from:a,elapsed:u,...d}){return!!Object.keys(d).length}const vu=(e,t,n,r={},i,s)=>o=>{const l=ru(r,e)||{},a=l.delay||r.delay||0;let{elapsed:u=0}=r;u=u-ft(a);let d={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-u,onUpdate:h=>{t.set(h),l.onUpdate&&l.onUpdate(h)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:s?void 0:i};Yx(l)||(d={...d,...Qx(e,d)}),d.duration&&(d.duration=ft(d.duration)),d.repeatDelay&&(d.repeatDelay=ft(d.repeatDelay)),d.from!==void 0&&(d.keyframes[0]=d.from);let f=!1;if((d.type===!1||d.duration===0&&!d.repeatDelay)&&(d.duration=0,d.delay===0&&(f=!0)),f&&!s&&t.get()!==void 0){const h=Ks(d.keyframes,l);if(h!==void 0)return H.update(()=>{d.onUpdate(h),d.onComplete()}),new pv([])}return!s&&td.supports(d)?new td(d):new gu(d)};function Xx({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function hm(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var s;let{transition:o=e.getDefaultTransition(),transitionEnd:l,...a}=t;r&&(o=r);const u=[],d=i&&e.animationState&&e.animationState.getState()[i];for(const f in a){const h=e.getValue(f,(s=e.latestValues[f])!==null&&s!==void 0?s:null),y=a[f];if(y===void 0||d&&Xx(d,f))continue;const v={delay:n,...ru(o||{},f)};let x=!1;if(window.MotionHandoffAnimation){const m=Fp(e);if(m){const p=window.MotionHandoffAnimation(m,f,H);p!==null&&(v.startTime=p,x=!0)}}Dl(e,f),h.start(vu(f,h,y,e.shouldReduceMotion&&Vp.has(f)?{type:!1}:v,e,x));const S=h.animation;S&&u.push(S)}return l&&Promise.all(u).then(()=>{H.update(()=>{l&&Ev(e,l)})}),u}function Ol(e,t,n={}){var r;const i=Ws(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:s=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(s=n.transitionOverride);const o=i?()=>Promise.all(hm(e,i,n)):()=>Promise.resolve(),l=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:d=0,staggerChildren:f,staggerDirection:h}=s;return Zx(e,t,d+u,f,h,n)}:()=>Promise.resolve(),{when:a}=s;if(a){const[u,d]=a==="beforeChildren"?[o,l]:[l,o];return u().then(()=>d())}else return Promise.all([o(),l(n.delay)])}function Zx(e,t,n=0,r=0,i=1,s){const o=[],l=(e.variantChildren.size-1)*r,a=i===1?(u=0)=>u*r:(u=0)=>l-u*r;return Array.from(e.variantChildren).sort(qx).forEach((u,d)=>{u.notify("AnimationStart",t),o.push(Ol(u,t,{...s,delay:n+a(d)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function qx(e,t){return e.sortNodePosition(t)}function Jx(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(s=>Ol(e,s,n));r=Promise.all(i)}else if(typeof t=="string")r=Ol(e,t,n);else{const i=typeof t=="function"?Ws(e,t,n.custom):t;r=Promise.all(hm(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const e1=Wa.length;function pm(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?pm(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<e1;n++){const r=Wa[n],i=e.props[r];(Kr(i)||i===!1)&&(t[r]=i)}return t}const t1=[...Ha].reverse(),n1=Ha.length;function r1(e){return t=>Promise.all(t.map(({animation:n,options:r})=>Jx(e,n,r)))}function i1(e){let t=r1(e),n=nd(),r=!0;const i=a=>(u,d)=>{var f;const h=Ws(e,d,a==="exit"?(f=e.presenceContext)===null||f===void 0?void 0:f.custom:void 0);if(h){const{transition:y,transitionEnd:v,...x}=h;u={...u,...x,...v}}return u};function s(a){t=a(e)}function o(a){const{props:u}=e,d=pm(e.parent)||{},f=[],h=new Set;let y={},v=1/0;for(let S=0;S<n1;S++){const m=t1[S],p=n[m],g=u[m]!==void 0?u[m]:d[m],w=Kr(g),k=m===a?p.isActive:null;k===!1&&(v=S);let j=g===d[m]&&g!==u[m]&&w;if(j&&r&&e.manuallyAnimateOnMount&&(j=!1),p.protectedKeys={...y},!p.isActive&&k===null||!g&&!p.prevProp||$s(g)||typeof g=="boolean")continue;const N=s1(p.prevProp,g);let C=N||m===a&&p.isActive&&!j&&w||S>v&&w,V=!1;const D=Array.isArray(g)?g:[g];let te=D.reduce(i(m),{});k===!1&&(te={});const{prevResolvedValues:qe={}}=p,b={...qe,...te},ot=re=>{C=!0,h.has(re)&&(V=!0,h.delete(re)),p.needsAnimating[re]=!0;const E=e.getValue(re);E&&(E.liveStyle=!1)};for(const re in b){const E=te[re],L=qe[re];if(y.hasOwnProperty(re))continue;let R=!1;El(E)&&El(L)?R=!Np(E,L):R=E!==L,R?E!=null?ot(re):h.add(re):E!==void 0&&h.has(re)?ot(re):p.protectedKeys[re]=!0}p.prevProp=g,p.prevResolvedValues=te,p.isActive&&(y={...y,...te}),r&&e.blockInitialAnimation&&(C=!1),C&&(!(j&&N)||V)&&f.push(...D.map(re=>({animation:re,options:{type:m}})))}if(h.size){const S={};h.forEach(m=>{const p=e.getBaseTarget(m),g=e.getValue(m);g&&(g.liveStyle=!0),S[m]=p??null}),f.push({animation:S})}let x=!!f.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(f):Promise.resolve()}function l(a,u){var d;if(n[a].isActive===u)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(h=>{var y;return(y=h.animationState)===null||y===void 0?void 0:y.setActive(a,u)}),n[a].isActive=u;const f=o(a);for(const h in n)n[h].protectedKeys={};return f}return{animateChanges:o,setActive:l,setAnimateFunction:s,getState:()=>n,reset:()=>{n=nd(),r=!0}}}function s1(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Np(t,e):!1}function Kt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nd(){return{animate:Kt(!0),whileInView:Kt(),whileHover:Kt(),whileTap:Kt(),whileDrag:Kt(),whileFocus:Kt(),exit:Kt()}}class $t{constructor(t){this.isMounted=!1,this.node=t}update(){}}class o1 extends $t{constructor(t){super(t),t.animationState||(t.animationState=i1(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();$s(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let l1=0;class a1 extends $t{constructor(){super(...arguments),this.id=l1++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const i=this.node.animationState.setActive("exit",!t);n&&!t&&i.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const u1={animation:{Feature:o1},exit:{Feature:a1}};function Xr(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function oi(e){return{point:{x:e.pageX,y:e.pageY}}}const c1=e=>t=>ou(t)&&e(t,oi(t));function Tr(e,t,n,r){return Xr(e,t,c1(n),r)}const rd=(e,t)=>Math.abs(e-t);function d1(e,t){const n=rd(e.x,t.x),r=rd(e.y,t.y);return Math.sqrt(n**2+r**2)}class mm{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const f=Mo(this.lastMoveEventInfo,this.history),h=this.startEvent!==null,y=d1(f.offset,{x:0,y:0})>=3;if(!h&&!y)return;const{point:v}=f,{timestamp:x}=ce;this.history.push({...v,timestamp:x});const{onStart:S,onMove:m}=this.handlers;h||(S&&S(this.lastMoveEvent,f),this.startEvent=this.lastMoveEvent),m&&m(this.lastMoveEvent,f)},this.handlePointerMove=(f,h)=>{this.lastMoveEvent=f,this.lastMoveEventInfo=Eo(h,this.transformPagePoint),H.update(this.updatePoint,!0)},this.handlePointerUp=(f,h)=>{this.end();const{onEnd:y,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const S=Mo(f.type==="pointercancel"?this.lastMoveEventInfo:Eo(h,this.transformPagePoint),this.history);this.startEvent&&y&&y(f,S),v&&v(f,S)},!ou(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const o=oi(t),l=Eo(o,this.transformPagePoint),{point:a}=l,{timestamp:u}=ce;this.history=[{...a,timestamp:u}];const{onSessionStart:d}=n;d&&d(t,Mo(l,this.history)),this.removeListeners=si(Tr(this.contextWindow,"pointermove",this.handlePointerMove),Tr(this.contextWindow,"pointerup",this.handlePointerUp),Tr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Ot(this.updatePoint)}}function Eo(e,t){return t?{point:t(e.point)}:e}function id(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Mo({point:e},t){return{point:e,delta:id(e,ym(t)),offset:id(e,f1(t)),velocity:h1(t,.1)}}function f1(e){return e[0]}function ym(e){return e[e.length-1]}function h1(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=ym(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>ft(t)));)n--;if(!r)return{x:0,y:0};const s=ht(i.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const o={x:(i.x-r.x)/s,y:(i.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}const gm=1e-4,p1=1-gm,m1=1+gm,vm=.01,y1=0-vm,g1=0+vm;function Le(e){return e.max-e.min}function v1(e,t,n){return Math.abs(e-t)<=n}function sd(e,t,n,r=.5){e.origin=r,e.originPoint=G(t.min,t.max,e.origin),e.scale=Le(n)/Le(t),e.translate=G(n.min,n.max,e.origin)-e.originPoint,(e.scale>=p1&&e.scale<=m1||isNaN(e.scale))&&(e.scale=1),(e.translate>=y1&&e.translate<=g1||isNaN(e.translate))&&(e.translate=0)}function Pr(e,t,n,r){sd(e.x,t.x,n.x,r?r.originX:void 0),sd(e.y,t.y,n.y,r?r.originY:void 0)}function od(e,t,n){e.min=n.min+t.min,e.max=e.min+Le(t)}function x1(e,t,n){od(e.x,t.x,n.x),od(e.y,t.y,n.y)}function ld(e,t,n){e.min=t.min-n.min,e.max=e.min+Le(t)}function Nr(e,t,n){ld(e.x,t.x,n.x),ld(e.y,t.y,n.y)}function w1(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?G(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?G(n,e,r.max):Math.min(e,n)),e}function ad(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function S1(e,{top:t,left:n,bottom:r,right:i}){return{x:ad(e.x,n,i),y:ad(e.y,t,r)}}function ud(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function k1(e,t){return{x:ud(e.x,t.x),y:ud(e.y,t.y)}}function C1(e,t){let n=.5;const r=Le(e),i=Le(t);return i>r?n=Hn(t.min,t.max-r,e.min):r>i&&(n=Hn(e.min,e.max-i,t.min)),vt(0,1,n)}function j1(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Il=.35;function T1(e=Il){return e===!1?e=0:e===!0&&(e=Il),{x:cd(e,"left","right"),y:cd(e,"top","bottom")}}function cd(e,t,n){return{min:dd(e,t),max:dd(e,n)}}function dd(e,t){return typeof e=="number"?e:e[t]||0}const fd=()=>({translate:0,scale:1,origin:0,originPoint:0}),En=()=>({x:fd(),y:fd()}),hd=()=>({min:0,max:0}),J=()=>({x:hd(),y:hd()});function Fe(e){return[e("x"),e("y")]}function xm({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function P1({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function N1(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function _o(e){return e===void 0||e===1}function zl({scale:e,scaleX:t,scaleY:n}){return!_o(e)||!_o(t)||!_o(n)}function Yt(e){return zl(e)||wm(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function wm(e){return pd(e.x)||pd(e.y)}function pd(e){return e&&e!=="0%"}function js(e,t,n){const r=e-n,i=t*r;return n+i}function md(e,t,n,r,i){return i!==void 0&&(e=js(e,i,r)),js(e,n,r)+t}function Bl(e,t=0,n=1,r,i){e.min=md(e.min,t,n,r,i),e.max=md(e.max,t,n,r,i)}function Sm(e,{x:t,y:n}){Bl(e.x,t.translate,t.scale,t.originPoint),Bl(e.y,n.translate,n.scale,n.originPoint)}const yd=.999999999999,gd=1.0000000000001;function E1(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let s,o;for(let l=0;l<i;l++){s=n[l],o=s.projectionDelta;const{visualElement:a}=s.options;a&&a.props.style&&a.props.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&_n(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Sm(e,o)),r&&Yt(s.latestValues)&&_n(e,s.latestValues))}t.x<gd&&t.x>yd&&(t.x=1),t.y<gd&&t.y>yd&&(t.y=1)}function Mn(e,t){e.min=e.min+t,e.max=e.max+t}function vd(e,t,n,r,i=.5){const s=G(e.min,e.max,i);Bl(e,t,n,s,r)}function _n(e,t){vd(e.x,t.x,t.scaleX,t.scale,t.originX),vd(e.y,t.y,t.scaleY,t.scale,t.originY)}function km(e,t){return xm(N1(e.getBoundingClientRect(),t))}function M1(e,t,n){const r=km(e,n),{scroll:i}=t;return i&&(Mn(r.x,i.offset.x),Mn(r.y,i.offset.y)),r}const Cm=({current:e})=>e?e.ownerDocument.defaultView:null,_1=new WeakMap;class D1{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=J(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=d=>{const{dragSnapToOrigin:f}=this.getProps();f?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(oi(d).point)},s=(d,f)=>{const{drag:h,dragPropagation:y,onDragStart:v}=this.getProps();if(h&&!y&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Cv(h),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Fe(S=>{let m=this.getAxisMotionValue(S).get()||0;if(it.test(m)){const{projection:p}=this.visualElement;if(p&&p.layout){const g=p.layout.layoutBox[S];g&&(m=Le(g)*(parseFloat(m)/100))}}this.originPoint[S]=m}),v&&H.postRender(()=>v(d,f)),Dl(this.visualElement,"transform");const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},o=(d,f)=>{const{dragPropagation:h,dragDirectionLock:y,onDirectionLock:v,onDrag:x}=this.getProps();if(!h&&!this.openDragLock)return;const{offset:S}=f;if(y&&this.currentDirection===null){this.currentDirection=A1(S),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",f.point,S),this.updateAxis("y",f.point,S),this.visualElement.render(),x&&x(d,f)},l=(d,f)=>this.stop(d,f),a=()=>Fe(d=>{var f;return this.getAnimationState(d)==="paused"&&((f=this.getAxisMotionValue(d).animation)===null||f===void 0?void 0:f.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new mm(t,{onSessionStart:i,onStart:s,onMove:o,onSessionEnd:l,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Cm(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&H.postRender(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Ai(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=w1(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,s=this.constraints;n&&Pn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=S1(i.layoutBox,n):this.constraints=!1,this.elastic=T1(r),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Fe(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=j1(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Pn(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=M1(r,i.root,this.visualElement.getTransformPagePoint());let o=k1(i.layout.layoutBox,s);if(n){const l=n(P1(o));this.hasMutatedConstraints=!!l,l&&(o=xm(l))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:l}=this.getProps(),a=this.constraints||{},u=Fe(d=>{if(!Ai(d,n,this.currentDirection))return;let f=a&&a[d]||{};o&&(f={min:0,max:0});const h=i?200:1e6,y=i?40:1e7,v={type:"inertia",velocity:r?t[d]:0,bounceStiffness:h,bounceDamping:y,timeConstant:750,restDelta:1,restSpeed:10,...s,...f};return this.startAxisValueAnimation(d,v)});return Promise.all(u).then(l)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return Dl(this.visualElement,t),r.start(vu(t,r,0,n,this.visualElement,!1))}stopAnimation(){Fe(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Fe(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Fe(n=>{const{drag:r}=this.getProps();if(!Ai(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:l}=i.layout.layoutBox[n];s.set(t[n]-G(o,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Pn(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Fe(o=>{const l=this.getAxisMotionValue(o);if(l&&this.constraints!==!1){const a=l.get();i[o]=C1({min:a,max:a},this.constraints[o])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Fe(o=>{if(!Ai(o,t,null))return;const l=this.getAxisMotionValue(o),{min:a,max:u}=this.constraints[o];l.set(G(a,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;_1.set(this.visualElement,this);const t=this.visualElement.current,n=Tr(t,"pointerdown",a=>{const{drag:u,dragListener:d=!0}=this.getProps();u&&d&&this.start(a)}),r=()=>{const{dragConstraints:a}=this.getProps();Pn(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),H.read(r);const o=Xr(window,"resize",()=>this.scalePositionWithinConstraints()),l=i.addEventListener("didUpdate",({delta:a,hasLayoutChanged:u})=>{this.isDragging&&u&&(Fe(d=>{const f=this.getAxisMotionValue(d);f&&(this.originPoint[d]+=a[d].translate,f.set(f.get()+a[d].translate))}),this.visualElement.render())});return()=>{o(),n(),s(),l&&l()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Il,dragMomentum:l=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:l}}}function Ai(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function A1(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class L1 extends $t{constructor(t){super(t),this.removeGroupControls=De,this.removeListeners=De,this.controls=new D1(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||De}unmount(){this.removeGroupControls(),this.removeListeners()}}const xd=e=>(t,n)=>{e&&H.postRender(()=>e(t,n))};class R1 extends $t{constructor(){super(...arguments),this.removePointerDownListener=De}onPointerDown(t){this.session=new mm(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Cm(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:xd(t),onStart:xd(n),onMove:r,onEnd:(s,o)=>{delete this.session,i&&H.postRender(()=>i(s,o))}}}mount(){this.removePointerDownListener=Tr(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Gi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function wd(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const lr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(A.test(e))e=parseFloat(e);else return e;const n=wd(e,t.target.x),r=wd(e,t.target.y);return`${n}% ${r}%`}},V1={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=It.parse(e);if(i.length>5)return r;const s=It.createTransformer(e),o=typeof i[0]!="number"?1:0,l=n.x.scale*t.x,a=n.y.scale*t.y;i[0+o]/=l,i[1+o]/=a;const u=G(l,a,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),s(i)}};class b1 extends P.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:s}=t;nv(F1),s&&(n.group&&n.group.add(s),r&&r.register&&i&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Gi.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:s}=this.props,o=r.projection;return o&&(o.isPresent=s,i||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||H.postRender(()=>{const l=o.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Ga.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function jm(e){const[t,n]=op(),r=P.useContext(Ia);return c.jsx(b1,{...e,layoutGroup:r,switchLayoutGroup:P.useContext(pp),isPresent:t,safeToRemove:n})}const F1={borderRadius:{...lr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:lr,borderTopRightRadius:lr,borderBottomLeftRadius:lr,borderBottomRightRadius:lr,boxShadow:V1};function O1(e,t,n){const r=ge(e)?e:Qr(e);return r.start(vu("",r,t,n)),r.animation}function I1(e){return e instanceof SVGElement&&e.tagName!=="svg"}const z1=(e,t)=>e.depth-t.depth;class B1{constructor(){this.children=[],this.isDirty=!1}add(t){lu(this.children,t),this.isDirty=!0}remove(t){au(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(z1),this.isDirty=!1,this.children.forEach(t)}}function U1(e,t){const n=st.now(),r=({timestamp:i})=>{const s=i-n;s>=t&&(Ot(r),e(s-t))};return H.read(r,!0),()=>Ot(r)}const Tm=["TopLeft","TopRight","BottomLeft","BottomRight"],$1=Tm.length,Sd=e=>typeof e=="string"?parseFloat(e):e,kd=e=>typeof e=="number"||A.test(e);function H1(e,t,n,r,i,s){i?(e.opacity=G(0,n.opacity!==void 0?n.opacity:1,W1(r)),e.opacityExit=G(t.opacity!==void 0?t.opacity:1,0,K1(r))):s&&(e.opacity=G(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<$1;o++){const l=`border${Tm[o]}Radius`;let a=Cd(t,l),u=Cd(n,l);if(a===void 0&&u===void 0)continue;a||(a=0),u||(u=0),a===0||u===0||kd(a)===kd(u)?(e[l]=Math.max(G(Sd(a),Sd(u),r),0),(it.test(u)||it.test(a))&&(e[l]+="%")):e[l]=u}(t.rotate||n.rotate)&&(e.rotate=G(t.rotate||0,n.rotate||0,r))}function Cd(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const W1=Pm(0,.5,Hp),K1=Pm(.5,.95,De);function Pm(e,t,n){return r=>r<e?0:r>t?1:n(Hn(e,t,r))}function jd(e,t){e.min=t.min,e.max=t.max}function be(e,t){jd(e.x,t.x),jd(e.y,t.y)}function Td(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Pd(e,t,n,r,i){return e-=t,e=js(e,1/n,r),i!==void 0&&(e=js(e,1/i,r)),e}function G1(e,t=0,n=1,r=.5,i,s=e,o=e){if(it.test(t)&&(t=parseFloat(t),t=G(o.min,o.max,t/100)-o.min),typeof t!="number")return;let l=G(s.min,s.max,r);e===s&&(l-=t),e.min=Pd(e.min,t,n,l,i),e.max=Pd(e.max,t,n,l,i)}function Nd(e,t,[n,r,i],s,o){G1(e,t[n],t[r],t[i],t.scale,s,o)}const Q1=["x","scaleX","originX"],Y1=["y","scaleY","originY"];function Ed(e,t,n,r){Nd(e.x,t,Q1,n?n.x:void 0,r?r.x:void 0),Nd(e.y,t,Y1,n?n.y:void 0,r?r.y:void 0)}function Md(e){return e.translate===0&&e.scale===1}function Nm(e){return Md(e.x)&&Md(e.y)}function _d(e,t){return e.min===t.min&&e.max===t.max}function X1(e,t){return _d(e.x,t.x)&&_d(e.y,t.y)}function Dd(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Em(e,t){return Dd(e.x,t.x)&&Dd(e.y,t.y)}function Ad(e){return Le(e.x)/Le(e.y)}function Ld(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class Z1{constructor(){this.members=[]}add(t){lu(this.members,t),t.scheduleRender()}remove(t){if(au(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const s=this.members[i];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function q1(e,t,n){let r="";const i=e.x.translate/t.x,s=e.y.translate/t.y,o=(n==null?void 0:n.z)||0;if((i||s||o)&&(r=`translate3d(${i}px, ${s}px, ${o}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:d,rotateX:f,rotateY:h,skewX:y,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),d&&(r+=`rotate(${d}deg) `),f&&(r+=`rotateX(${f}deg) `),h&&(r+=`rotateY(${h}deg) `),y&&(r+=`skewX(${y}deg) `),v&&(r+=`skewY(${v}deg) `)}const l=e.x.scale*t.x,a=e.y.scale*t.y;return(l!==1||a!==1)&&(r+=`scale(${l}, ${a})`),r||"none"}const Xt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},pr=typeof window<"u"&&window.MotionDebug!==void 0,Do=["","X","Y","Z"],J1={visibility:"hidden"},Rd=1e3;let ew=0;function Ao(e,t,n,r){const{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Mm(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=Fp(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:s}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",H,!(i||s))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Mm(r)}function _m({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(o={},l=t==null?void 0:t()){this.id=ew++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,pr&&(Xt.totalNodes=Xt.resolvedTargetDeltas=Xt.recalculatedProjection=0),this.nodes.forEach(rw),this.nodes.forEach(aw),this.nodes.forEach(uw),this.nodes.forEach(iw),pr&&window.MotionDebug.record(Xt)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=l?l.root||l:this,this.path=l?[...l.path,l]:[],this.parent=l,this.depth=l?l.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new B1)}addEventListener(o,l){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new uu),this.eventHandlers.get(o).add(l)}notifyListeners(o,...l){const a=this.eventHandlers.get(o);a&&a.notify(...l)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,l=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=I1(o),this.instance=o;const{layoutId:a,layout:u,visualElement:d}=this.options;if(d&&!d.current&&d.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),l&&(u||a)&&(this.isLayoutDirty=!0),e){let f;const h=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,f&&f(),f=U1(h,250),Gi.hasAnimatedSinceResize&&(Gi.hasAnimatedSinceResize=!1,this.nodes.forEach(bd))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&d&&(a||u)&&this.addEventListener("didUpdate",({delta:f,hasLayoutChanged:h,hasRelativeTargetChanged:y,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||d.getDefaultTransition()||pw,{onLayoutAnimationStart:S,onLayoutAnimationComplete:m}=d.getProps(),p=!this.targetLayout||!Em(this.targetLayout,v)||y,g=!h&&y;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||g||h&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(f,g);const w={...ru(x,"layout"),onPlay:S,onComplete:m};(d.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else h||bd(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Ot(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(cw),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Mm(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let d=0;d<this.path.length;d++){const f=this.path[d];f.shouldResetTransform=!0,f.updateScroll("snapshot"),f.options.layoutRoot&&f.willUpdate(!1)}const{layoutId:l,layout:a}=this.options;if(l===void 0&&!a)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Vd);return}this.isUpdating||this.nodes.forEach(ow),this.isUpdating=!1,this.nodes.forEach(lw),this.nodes.forEach(tw),this.nodes.forEach(nw),this.clearAllSnapshots();const l=st.now();ce.delta=vt(0,1e3/60,l-ce.timestamp),ce.timestamp=l,ce.isProcessing=!0,Co.update.process(ce),Co.preRender.process(ce),Co.render.process(ce),ce.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Ga.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sw),this.sharedNodes.forEach(dw)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,H.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){H.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=J(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let l=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(l=!1),l){const a=r(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:a,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:a}}}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,l=this.projectionDelta&&!Nm(this.projectionDelta),a=this.getTransformTemplate(),u=a?a(this.latestValues,""):void 0,d=u!==this.prevTransformTemplateValue;o&&(l||Yt(this.latestValues)||d)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const l=this.measurePageBox();let a=this.removeElementScroll(l);return o&&(a=this.removeTransform(a)),mw(a),{animationId:this.root.animationId,measuredBox:l,layoutBox:a,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:l}=this.options;if(!l)return J();const a=l.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(yw))){const{scroll:d}=this.root;d&&(Mn(a.x,d.offset.x),Mn(a.y,d.offset.y))}return a}removeElementScroll(o){var l;const a=J();if(be(a,o),!((l=this.scroll)===null||l===void 0)&&l.wasRoot)return a;for(let u=0;u<this.path.length;u++){const d=this.path[u],{scroll:f,options:h}=d;d!==this.root&&f&&h.layoutScroll&&(f.wasRoot&&be(a,o),Mn(a.x,f.offset.x),Mn(a.y,f.offset.y))}return a}applyTransform(o,l=!1){const a=J();be(a,o);for(let u=0;u<this.path.length;u++){const d=this.path[u];!l&&d.options.layoutScroll&&d.scroll&&d!==d.root&&_n(a,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),Yt(d.latestValues)&&_n(a,d.latestValues)}return Yt(this.latestValues)&&_n(a,this.latestValues),a}removeTransform(o){const l=J();be(l,o);for(let a=0;a<this.path.length;a++){const u=this.path[a];if(!u.instance||!Yt(u.latestValues))continue;zl(u.latestValues)&&u.updateSnapshot();const d=J(),f=u.measurePageBox();be(d,f),Ed(l,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,d)}return Yt(this.latestValues)&&Ed(l,this.latestValues),l}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ce.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var l;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==a;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:f,layoutId:h}=this.options;if(!(!this.layout||!(f||h))){if(this.resolvedRelativeTargetAt=ce.timestamp,!this.targetDelta&&!this.relativeTarget){const y=this.getClosestProjectingParent();y&&y.layout&&this.animationProgress!==1?(this.relativeParent=y,this.forceRelativeParentToResolveTarget(),this.relativeTarget=J(),this.relativeTargetOrigin=J(),Nr(this.relativeTargetOrigin,this.layout.layoutBox,y.layout.layoutBox),be(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=J(),this.targetWithTransforms=J()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),x1(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):be(this.target,this.layout.layoutBox),Sm(this.target,this.targetDelta)):be(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const y=this.getClosestProjectingParent();y&&!!y.resumingFrom==!!this.resumingFrom&&!y.options.layoutScroll&&y.target&&this.animationProgress!==1?(this.relativeParent=y,this.forceRelativeParentToResolveTarget(),this.relativeTarget=J(),this.relativeTargetOrigin=J(),Nr(this.relativeTargetOrigin,this.target,y.target),be(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}pr&&Xt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||zl(this.parent.latestValues)||wm(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const l=this.getLead(),a=!!this.resumingFrom||this!==l;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===ce.timestamp&&(u=!1),u)return;const{layout:d,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||f))return;be(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,y=this.treeScale.y;E1(this.layoutCorrected,this.treeScale,this.path,a),l.layout&&!l.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(l.target=l.layout.layoutBox,l.targetWithTransforms=J());const{target:v}=l;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Td(this.prevProjectionDelta.x,this.projectionDelta.x),Td(this.prevProjectionDelta.y,this.projectionDelta.y)),Pr(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==y||!Ld(this.projectionDelta.x,this.prevProjectionDelta.x)||!Ld(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),pr&&Xt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var l;if((l=this.options.visualElement)===null||l===void 0||l.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=En(),this.projectionDelta=En(),this.projectionDeltaWithTransform=En()}setAnimationOrigin(o,l=!1){const a=this.snapshot,u=a?a.latestValues:{},d={...this.latestValues},f=En();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;const h=J(),y=a?a.source:void 0,v=this.layout?this.layout.source:void 0,x=y!==v,S=this.getStack(),m=!S||S.members.length<=1,p=!!(x&&!m&&this.options.crossfade===!0&&!this.path.some(hw));this.animationProgress=0;let g;this.mixTargetDelta=w=>{const k=w/1e3;Fd(f.x,o.x,k),Fd(f.y,o.y,k),this.setTargetDelta(f),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Nr(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),fw(this.relativeTarget,this.relativeTargetOrigin,h,k),g&&X1(this.relativeTarget,g)&&(this.isProjectionDirty=!1),g||(g=J()),be(g,this.relativeTarget)),x&&(this.animationValues=d,H1(d,u,this.latestValues,k,p,m)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=k},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Ot(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=H.update(()=>{Gi.hasAnimatedSinceResize=!0,this.currentAnimation=O1(0,Rd,{...o,onUpdate:l=>{this.mixTargetDelta(l),o.onUpdate&&o.onUpdate(l)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Rd),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:l,target:a,layout:u,latestValues:d}=o;if(!(!l||!a||!u)){if(this!==o&&this.layout&&u&&Dm(this.options.animationType,this.layout.layoutBox,u.layoutBox)){a=this.target||J();const f=Le(this.layout.layoutBox.x);a.x.min=o.target.x.min,a.x.max=a.x.min+f;const h=Le(this.layout.layoutBox.y);a.y.min=o.target.y.min,a.y.max=a.y.min+h}be(l,a),_n(l,d),Pr(this.projectionDeltaWithTransform,this.layoutCorrected,l,d)}}registerSharedNode(o,l){this.sharedNodes.has(o)||this.sharedNodes.set(o,new Z1),this.sharedNodes.get(o).add(l);const u=l.options.initialPromotionConfig;l.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(l):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:l}=this.options;return l?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:l}=this.options;return l?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:l,preserveFollowOpacity:a}={}){const u=this.getStack();u&&u.promote(this,a),o&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let l=!1;const{latestValues:a}=o;if((a.z||a.rotate||a.rotateX||a.rotateY||a.rotateZ||a.skewX||a.skewY)&&(l=!0),!l)return;const u={};a.z&&Ao("z",o,u,this.animationValues);for(let d=0;d<Do.length;d++)Ao(`rotate${Do[d]}`,o,u,this.animationValues),Ao(`skew${Do[d]}`,o,u,this.animationValues);o.render();for(const d in u)o.setStaticValue(d,u[d]),this.animationValues&&(this.animationValues[d]=u[d]);o.scheduleRender()}getProjectionStyles(o){var l,a;if(!this.instance||this.isSVG)return;if(!this.isVisible)return J1;const u={visibility:""},d=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Wi(o==null?void 0:o.pointerEvents)||"",u.transform=d?d(this.latestValues,""):"none",u;const f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=Wi(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Yt(this.latestValues)&&(x.transform=d?d({},""):"none",this.hasProjected=!1),x}const h=f.animationValues||f.latestValues;this.applyTransformsToTarget(),u.transform=q1(this.projectionDeltaWithTransform,this.treeScale,h),d&&(u.transform=d(h,u.transform));const{x:y,y:v}=this.projectionDelta;u.transformOrigin=`${y.origin*100}% ${v.origin*100}% 0`,f.animationValues?u.opacity=f===this?(a=(l=h.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&a!==void 0?a:1:this.preserveOpacity?this.latestValues.opacity:h.opacityExit:u.opacity=f===this?h.opacity!==void 0?h.opacity:"":h.opacityExit!==void 0?h.opacityExit:0;for(const x in xs){if(h[x]===void 0)continue;const{correct:S,applyTo:m}=xs[x],p=u.transform==="none"?h[x]:S(h[x],f);if(m){const g=m.length;for(let w=0;w<g;w++)u[m[w]]=p}else u[x]=p}return this.options.layoutId&&(u.pointerEvents=f===this?Wi(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var l;return(l=o.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(Vd),this.root.sharedNodes.clear()}}}function tw(e){e.updateLayout()}function nw(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:s}=e.options,o=n.source!==e.layout.source;s==="size"?Fe(f=>{const h=o?n.measuredBox[f]:n.layoutBox[f],y=Le(h);h.min=r[f].min,h.max=h.min+y}):Dm(s,n.layoutBox,r)&&Fe(f=>{const h=o?n.measuredBox[f]:n.layoutBox[f],y=Le(r[f]);h.max=h.min+y,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[f].max=e.relativeTarget[f].min+y)});const l=En();Pr(l,r,n.layoutBox);const a=En();o?Pr(a,e.applyTransform(i,!0),n.measuredBox):Pr(a,r,n.layoutBox);const u=!Nm(l);let d=!1;if(!e.resumeFrom){const f=e.getClosestProjectingParent();if(f&&!f.resumeFrom){const{snapshot:h,layout:y}=f;if(h&&y){const v=J();Nr(v,n.layoutBox,h.layoutBox);const x=J();Nr(x,r,y.layoutBox),Em(v,x)||(d=!0),f.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=f)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:a,layoutDelta:l,hasLayoutChanged:u,hasRelativeTargetChanged:d})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function rw(e){pr&&Xt.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function iw(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function sw(e){e.clearSnapshot()}function Vd(e){e.clearMeasurements()}function ow(e){e.isLayoutDirty=!1}function lw(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function bd(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function aw(e){e.resolveTargetDelta()}function uw(e){e.calcProjection()}function cw(e){e.resetSkewAndRotation()}function dw(e){e.removeLeadSnapshot()}function Fd(e,t,n){e.translate=G(t.translate,0,n),e.scale=G(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function Od(e,t,n,r){e.min=G(t.min,n.min,r),e.max=G(t.max,n.max,r)}function fw(e,t,n,r){Od(e.x,t.x,n.x,r),Od(e.y,t.y,n.y,r)}function hw(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const pw={duration:.45,ease:[.4,0,.1,1]},Id=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),zd=Id("applewebkit/")&&!Id("chrome/")?Math.round:De;function Bd(e){e.min=zd(e.min),e.max=zd(e.max)}function mw(e){Bd(e.x),Bd(e.y)}function Dm(e,t,n){return e==="position"||e==="preserve-aspect"&&!v1(Ad(t),Ad(n),.2)}function yw(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const gw=_m({attachResizeListener:(e,t)=>Xr(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Lo={current:void 0},Am=_m({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Lo.current){const e=new gw({});e.mount(window),e.setOptions({layoutScroll:!0}),Lo.current=e}return Lo.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),vw={pan:{Feature:R1},drag:{Feature:L1,ProjectionNode:Am,MeasureLayout:jm}};function Ud(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,s=r[i];s&&H.postRender(()=>s(t,oi(t)))}class xw extends $t{mount(){const{current:t}=this.node;t&&(this.unmount=vv(t,n=>(Ud(this.node,n,"Start"),r=>Ud(this.node,r,"End"))))}unmount(){}}class ww extends $t{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=si(Xr(this.node.current,"focus",()=>this.onFocus()),Xr(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function $d(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),s=r[i];s&&H.postRender(()=>s(t,oi(t)))}class Sw extends $t{mount(){const{current:t}=this.node;t&&(this.unmount=kv(t,n=>($d(this.node,n,"Start"),(r,{success:i})=>$d(this.node,r,i?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Ul=new WeakMap,Ro=new WeakMap,kw=e=>{const t=Ul.get(e.target);t&&t(e)},Cw=e=>{e.forEach(kw)};function jw({root:e,...t}){const n=e||document;Ro.has(n)||Ro.set(n,{});const r=Ro.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(Cw,{root:e,...t})),r[i]}function Tw(e,t,n){const r=jw(t);return Ul.set(e,n),r.observe(e),()=>{Ul.delete(e),r.unobserve(e)}}const Pw={some:0,all:1};class Nw extends $t{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:s}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:Pw[i]},l=a=>{const{isIntersecting:u}=a;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:d,onViewportLeave:f}=this.node.getProps(),h=u?d:f;h&&h(a)};return Tw(this.node.current,o,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Ew(t,n))&&this.startObserver()}unmount(){}}function Ew({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const Mw={inView:{Feature:Nw},tap:{Feature:Sw},focus:{Feature:ww},hover:{Feature:xw}},_w={layout:{ProjectionNode:Am,MeasureLayout:jm}},$l={current:null},Lm={current:!1};function Dw(){if(Lm.current=!0,!!Ua)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>$l.current=e.matches;e.addListener(t),t()}else $l.current=!1}const Aw=[...im,me,It],Lw=e=>Aw.find(rm(e)),Hd=new WeakMap;function Rw(e,t,n){for(const r in t){const i=t[r],s=n[r];if(ge(i))e.addValue(r,i);else if(ge(s))e.addValue(r,Qr(i,{owner:e}));else if(s!==i)if(e.hasValue(r)){const o=e.getValue(r);o.liveStyle===!0?o.jump(i):o.hasAnimated||o.set(i)}else{const o=e.getStaticValue(r);e.addValue(r,Qr(o!==void 0?o:i,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Wd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Vw{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},l={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=mu,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const y=st.now();this.renderScheduledAt<y&&(this.renderScheduledAt=y,H.render(this.render,!1,!0))};const{latestValues:a,renderState:u,onUpdate:d}=o;this.onUpdate=d,this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=l,this.blockInitialAnimation=!!s,this.isControllingVariants=Hs(n),this.isVariantNode=fp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:f,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const y in h){const v=h[y];a[y]!==void 0&&ge(v)&&v.set(a[y],!1)}}mount(t){this.current=t,Hd.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Lm.current||Dw(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:$l.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Hd.delete(this.current),this.projection&&this.projection.unmount(),Ot(this.notifyUpdate),Ot(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=fn.has(t),i=n.on("change",l=>{this.latestValues[t]=l,this.props.onUpdate&&H.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{i(),s(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Wn){const n=Wn[t];if(!n)continue;const{isEnabled:r,Feature:i}=n;if(!this.features[t]&&i&&r(this.props)&&(this.features[t]=new i(this)),this.features[t]){const s=this.features[t];s.isMounted?s.update():(s.mount(),s.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):J()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Wd.length;r++){const i=Wd[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const s="on"+i,o=t[s];o&&(this.propEventSubscriptions[i]=this.on(i,o))}this.prevMotionValues=Rw(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Qr(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let i=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return i!=null&&(typeof i=="string"&&(tm(i)||Kp(i))?i=parseFloat(i):!Lw(i)&&It.test(n)&&(i=qp(t,n)),this.setBaseTarget(t,ge(i)?i.get():i)),ge(i)?i.get():i}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let i;if(typeof r=="string"||typeof r=="object"){const o=Ya(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(i=o[t])}if(r&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!ge(s)?s:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new uu),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class Rm extends Vw{constructor(){super(...arguments),this.KeyframeResolver=sm}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ge(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function bw(e){return window.getComputedStyle(e)}class Fw extends Rm{constructor(){super(...arguments),this.type="html",this.renderInstance=Sp}readValueFromInstance(t,n){if(fn.has(n)){const r=pu(n);return r&&r.default||0}else{const r=bw(t),i=(vp(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return km(t,n)}build(t,n,r){qa(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return nu(t,n,r)}}class Ow extends Rm{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=J}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(fn.has(n)){const r=pu(n);return r&&r.default||0}return n=kp.has(n)?n:Ka(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return Tp(t,n,r)}build(t,n,r){Ja(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,i){Cp(t,n,r,i)}mount(t){this.isSVGTag=tu(t.tagName),super.mount(t)}}const Iw=(e,t)=>Qa(e)?new Ow(t):new Fw(t,{allowProjection:e!==P.Fragment}),zw=dv({...u1,...Mw,...vw,..._w},Iw),_=P0(zw);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Bw={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uw=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase();/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=(e,t)=>{const n=P.forwardRef(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:l="",children:a,...u},d)=>P.createElement("svg",{ref:d,...Bw,width:i,height:i,stroke:r,strokeWidth:o?Number(s)*24/Number(i):s,className:["lucide",`lucide-${Uw(e)}`,l].join(" "),...u},[...t.map(([f,h])=>P.createElement(f,h)),...Array.isArray(a)?a:[a]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hl=z("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vm=z("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gs=z("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zr=z("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $w=z("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hw=z("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ww=z("ChevronsLeft",[["path",{d:"m11 17-5-5 5-5",key:"13zhaf"}],["path",{d:"m18 17-5-5 5-5",key:"h8a8et"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kw=z("ChevronsRight",[["path",{d:"m6 17 5-5-5-5",key:"xnjwq"}],["path",{d:"m13 17 5-5-5-5",key:"17xmmf"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Er=z("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gw=z("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qw=z("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yw=z("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gn=z("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bm=z("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const li=z("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xw=z("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qs=z("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zw=z("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qw=z("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jw=z("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e2=z("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t2=z("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xu=z("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fm=z("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Om=z("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Im=z("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n2=z("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r2=z("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.363.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wu=z("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),Kd=({onNavigate:e})=>{const t=[{id:"dataEntry",title:"إدخال البيانات",description:"إضافة سجل جديد إلى الأرشيف",icon:Jw,color:"from-blue-500 to-blue-600",hoverColor:"from-blue-600 to-blue-700"},{id:"report",title:"عرض التقرير",description:"عرض جميع السجلات في جدول",icon:Vm,color:"from-green-500 to-green-600",hoverColor:"from-green-600 to-green-700"},{id:"archive",title:"الأرشيف العام",description:"تصفح وإدارة جميع السجلات",icon:Hl,color:"from-purple-500 to-purple-600",hoverColor:"from-purple-600 to-purple-700"},{id:"filter",title:"تصفية البيانات",description:"البحث والتصفية المتقدمة",icon:bm,color:"from-orange-500 to-orange-600",hoverColor:"from-orange-600 to-orange-700"},{id:"settings",title:"الإعدادات",description:"تخصيص المظهر والثيم",icon:Fm,color:"from-gray-500 to-gray-600",hoverColor:"from-gray-600 to-gray-700"}];return c.jsxs("div",{className:"min-h-screen flex flex-col items-center justify-center p-8",children:[c.jsxs(_.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},transition:{duration:.8},className:"text-center mb-12",children:[c.jsx(_.div,{animate:{rotate:360},transition:{duration:20,repeat:1/0,ease:"linear"},className:"inline-block mb-6",children:c.jsxs("div",{className:"relative",children:[c.jsx(Qw,{className:"w-24 h-24 text-white mx-auto drop-shadow-2xl"}),c.jsx(Om,{className:"w-8 h-8 text-yellow-300 absolute -top-2 -right-2 animate-pulse"})]})}),c.jsx(_.h1,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.3,duration:.8},className:"text-6xl font-bold text-white mb-4 text-glow float-animation",children:"برنامج الأرشفة الإلكترونية"}),c.jsx(_.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5,duration:.8},className:"text-2xl text-white/90 mb-2",children:"الإصدار 1.0.0"}),c.jsx(_.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.7,duration:.8},className:"text-lg text-white/80",children:"إعداد: المحاسب المبرمج علي عاجل خشان المحنة"})]}),c.jsx(_.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},transition:{delay:.9,duration:.8},className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl w-full",children:t.map((n,r)=>{const i=n.icon;return c.jsxs(_.button,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:1+r*.1,duration:.5},whileHover:{scale:1.05,transition:{duration:.2}},whileTap:{scale:.95},onClick:()=>e(n.id),className:`
                relative overflow-hidden
                bg-gradient-to-br ${n.color} hover:bg-gradient-to-br hover:${n.hoverColor}
                text-white p-8 rounded-2xl shadow-2xl
                glow-button card-hover
                group
              `,children:[c.jsxs("div",{className:"relative z-10",children:[c.jsx(i,{className:"w-12 h-12 mx-auto mb-4 group-hover:animate-bounce"}),c.jsx("h3",{className:"text-xl font-bold mb-2",children:n.title}),c.jsx("p",{className:"text-white/90 text-sm",children:n.description})]}),c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"})]},n.id)})}),c.jsx(_.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:1.5,duration:.8},className:"mt-12 text-center",children:c.jsxs("div",{className:"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20",children:[c.jsx(Gn,{className:"w-8 h-8 text-white mx-auto mb-3"}),c.jsx("p",{className:"text-white/90 text-lg",children:"نظام أرشفة إلكتروني متطور لإدارة الوثائق والسجلات"}),c.jsx("p",{className:"text-white/70 text-sm mt-2",children:"تصميم عصري • واجهة تفاعلية • أداء عالي"})]})})]})},i2=({onNavigate:e,onAddRecord:t,onUpdateRecord:n,currentRecord:r,setCurrentRecord:i})=>{const[s,o]=P.useState({book_number:"",date_day:"",date_month:"",date_year:new Date().getFullYear(),subject:"",content:"",department:"",notes:""}),[l,a]=P.useState({type:"",text:""}),[u,d]=P.useState(!1);P.useEffect(()=>{r&&o({book_number:r.book_number||"",date_day:r.date_day||"",date_month:r.date_month||"",date_year:r.date_year||new Date().getFullYear(),subject:r.subject||"",content:r.content||"",department:r.department||"",notes:r.notes||""})},[r]);const f=x=>{const{name:S,value:m}=x.target;o(p=>({...p,[S]:m}))},h=async x=>{if(x.preventDefault(),d(!0),a({type:"",text:""}),!s.book_number||!s.date_day||!s.date_month||!s.date_year||!s.subject||!s.content||!s.department){a({type:"error",text:"جميع الحقول مطلوبة باستثناء الهامش"}),d(!1);return}try{let S;r?S=await n(r.id,s):S=await t(s),S.success?(a({type:"success",text:S.message}),r||o({book_number:"",date_day:"",date_month:"",date_year:new Date().getFullYear(),subject:"",content:"",department:"",notes:""})):a({type:"error",text:S.message})}catch{a({type:"error",text:"حدث خطأ غير متوقع"})}d(!1)},y=()=>{i(null),o({book_number:"",date_day:"",date_month:"",date_year:new Date().getFullYear(),subject:"",content:"",department:"",notes:""}),e("welcome")},v=["الإدارة العامة","الموارد البشرية","المالية والمحاسبة","تقنية المعلومات","الشؤون القانونية","العلاقات العامة","المشتريات والمخازن","الأمن والسلامة","الجودة والتطوير","خدمة العملاء"];return c.jsx("div",{className:"min-h-screen p-8",children:c.jsxs("div",{className:"max-w-4xl mx-auto",children:[c.jsxs(_.div,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[c.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[c.jsx("div",{className:"bg-white/20 backdrop-blur-md rounded-full p-3",children:r?c.jsx(Im,{className:"w-8 h-8 text-white"}):c.jsx(Gn,{className:"w-8 h-8 text-white"})}),c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-white",children:r?"تعديل السجل":"إدخال البيانات"}),c.jsx("p",{className:"text-white/80",children:r?"تعديل بيانات السجل الموجود":"إضافة سجل جديد إلى الأرشيف"})]})]}),c.jsxs(_.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:y,className:"bg-white/20 backdrop-blur-md text-white px-6 py-3 rounded-xl hover:bg-white/30 transition-all duration-300 flex items-center space-x-2 space-x-reverse glow-button",children:[c.jsx(li,{className:"w-5 h-5"}),c.jsx("span",{children:"العودة للرئيسية"})]})]}),c.jsx(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 form-container",children:c.jsxs("form",{onSubmit:h,className:"space-y-6",children:[c.jsxs("div",{children:[c.jsxs("label",{className:"block text-white font-semibold mb-2 flex items-center space-x-2 space-x-reverse",children:[c.jsx(Gn,{className:"w-5 h-5"}),c.jsx("span",{children:"رقم الكتاب"})]}),c.jsx("input",{type:"text",name:"book_number",value:s.book_number,onChange:f,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"أدخل رقم الكتاب",required:!0})]}),c.jsxs("div",{children:[c.jsxs("label",{className:"block text-white font-semibold mb-2 flex items-center space-x-2 space-x-reverse",children:[c.jsx(Zr,{className:"w-5 h-5"}),c.jsx("span",{children:"تاريخ الكتاب"})]}),c.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[c.jsx("div",{children:c.jsx("input",{type:"number",name:"date_day",value:s.date_day,onChange:f,min:"1",max:"31",className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"اليوم",required:!0})}),c.jsx("div",{children:c.jsx("input",{type:"number",name:"date_month",value:s.date_month,onChange:f,min:"1",max:"12",className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"الشهر",required:!0})}),c.jsx("div",{children:c.jsx("input",{type:"number",name:"date_year",value:s.date_year,onChange:f,min:"1900",max:"2100",className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"السنة",required:!0})})]})]}),c.jsxs("div",{children:[c.jsx("label",{className:"block text-white font-semibold mb-2",children:"الموضوع"}),c.jsx("input",{type:"text",name:"subject",value:s.subject,onChange:f,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"أدخل موضوع الكتاب",required:!0})]}),c.jsxs("div",{children:[c.jsxs("label",{className:"block text-white font-semibold mb-2 flex items-center space-x-2 space-x-reverse",children:[c.jsx(wu,{className:"w-5 h-5"}),c.jsx("span",{children:"الفحوى أو اسم الموظف المعني"})]}),c.jsx("textarea",{name:"content",value:s.content,onChange:f,rows:"4",className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow resize-none",placeholder:"أدخل فحوى الكتاب أو اسم الموظف المعني",required:!0})]}),c.jsxs("div",{children:[c.jsxs("label",{className:"block text-white font-semibold mb-2 flex items-center space-x-2 space-x-reverse",children:[c.jsx(Gs,{className:"w-5 h-5"}),c.jsx("span",{children:"الدائرة المعنية"})]}),c.jsxs("select",{name:"department",value:s.department,onChange:f,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white focus:outline-none input-glow",required:!0,children:[c.jsx("option",{value:"",className:"bg-gray-800",children:"اختر الدائرة المعنية"}),v.map((x,S)=>c.jsx("option",{value:x,className:"bg-gray-800",children:x},S))]})]}),c.jsxs("div",{children:[c.jsxs("label",{className:"block text-white font-semibold mb-2 flex items-center space-x-2 space-x-reverse",children:[c.jsx(Qs,{className:"w-5 h-5"}),c.jsx("span",{children:"الهامش (اختياري)"})]}),c.jsx("textarea",{name:"notes",value:s.notes,onChange:f,rows:"3",className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow resize-none",placeholder:"أدخل أي ملاحظات إضافية"})]}),l.text&&c.jsxs(_.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:`p-4 rounded-xl flex items-center space-x-2 space-x-reverse ${l.type==="success"?"alert-success":"alert-error"}`,children:[l.type==="success"?c.jsx(Er,{className:"w-5 h-5"}):c.jsx(Gw,{className:"w-5 h-5"}),c.jsx("span",{children:l.text})]}),c.jsxs("div",{className:"flex space-x-4 space-x-reverse pt-4",children:[c.jsxs(_.button,{type:"submit",disabled:u,whileHover:{scale:1.02},whileTap:{scale:.98},className:"flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-4 rounded-xl font-semibold glow-button flex items-center justify-center space-x-2 space-x-reverse disabled:opacity-50",children:[c.jsx(t2,{className:"w-5 h-5"}),c.jsx("span",{children:u?"جاري الحفظ...":r?"تحديث السجل":"حفظ السجل"})]}),c.jsx(_.button,{type:"button",onClick:y,whileHover:{scale:1.02},whileTap:{scale:.98},className:"px-8 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white py-4 rounded-xl font-semibold glow-button",children:"إلغاء"})]})]})})]})})},s2=({onNavigate:e,records:t})=>{const[n,r]=P.useState(""),[i,s]=P.useState("date"),[o,l]=P.useState("desc"),u=[...t.filter(y=>y.subject.toLowerCase().includes(n.toLowerCase())||y.content.toLowerCase().includes(n.toLowerCase())||y.department.toLowerCase().includes(n.toLowerCase())||y.book_number.toLowerCase().includes(n.toLowerCase()))].sort((y,v)=>{let x,S;switch(i){case"date":x=new Date(y.date_year,y.date_month-1,y.date_day),S=new Date(v.date_year,v.date_month-1,v.date_day);break;case"book_number":x=y.book_number,S=v.book_number;break;case"subject":x=y.subject,S=v.subject;break;case"department":x=y.department,S=v.department;break;default:x=y.id,S=v.id}return o==="asc"?x>S?1:-1:x<S?1:-1}),d=y=>{i===y?l(o==="asc"?"desc":"asc"):(s(y),l("asc"))},f=(y,v,x)=>`${y}/${v}/${x}`,h=()=>{const v=[["رقم الكتاب","التاريخ","الموضوع","الفحوى","الدائرة","الهامش"].join(","),...u.map(p=>[p.book_number,f(p.date_day,p.date_month,p.date_year),p.subject,p.content.replace(/,/g,"؛"),p.department,(p.notes||"").replace(/,/g,"؛")].join(","))].join(`
`),x=new Blob([v],{type:"text/csv;charset=utf-8;"}),S=document.createElement("a"),m=URL.createObjectURL(x);S.setAttribute("href",m),S.setAttribute("download",`تقرير_الأرشيف_${new Date().toISOString().split("T")[0]}.csv`),S.style.visibility="hidden",document.body.appendChild(S),S.click(),document.body.removeChild(S)};return c.jsx("div",{className:"min-h-screen p-8",children:c.jsxs("div",{className:"max-w-7xl mx-auto",children:[c.jsxs(_.div,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[c.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[c.jsx("div",{className:"bg-white/20 backdrop-blur-md rounded-full p-3",children:c.jsx(Vm,{className:"w-8 h-8 text-white"})}),c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-white",children:"تقرير الأرشيف"}),c.jsxs("p",{className:"text-white/80",children:["عرض جميع السجلات (",u.length," سجل)"]})]})]}),c.jsxs("div",{className:"flex space-x-4 space-x-reverse",children:[c.jsxs(_.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:h,className:"bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-xl transition-all duration-300 flex items-center space-x-2 space-x-reverse glow-button",children:[c.jsx(Yw,{className:"w-5 h-5"}),c.jsx("span",{children:"تصدير CSV"})]}),c.jsxs(_.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("welcome"),className:"bg-white/20 backdrop-blur-md text-white px-6 py-3 rounded-xl hover:bg-white/30 transition-all duration-300 flex items-center space-x-2 space-x-reverse glow-button",children:[c.jsx(li,{className:"w-5 h-5"}),c.jsx("span",{children:"العودة للرئيسية"})]})]})]}),c.jsx(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 mb-8 border border-white/20",children:c.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[c.jsxs("div",{className:"flex-1 relative",children:[c.jsx(xu,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5"}),c.jsx("input",{type:"text",value:n,onChange:y=>r(y.target.value),placeholder:"البحث في السجلات...",className:"w-full pr-12 pl-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow"})]}),c.jsxs("div",{className:"flex space-x-2 space-x-reverse",children:[c.jsxs("select",{value:i,onChange:y=>s(y.target.value),className:"px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white focus:outline-none",children:[c.jsx("option",{value:"date",className:"bg-gray-800",children:"ترتيب حسب التاريخ"}),c.jsx("option",{value:"book_number",className:"bg-gray-800",children:"ترتيب حسب رقم الكتاب"}),c.jsx("option",{value:"subject",className:"bg-gray-800",children:"ترتيب حسب الموضوع"}),c.jsx("option",{value:"department",className:"bg-gray-800",children:"ترتيب حسب الدائرة"})]}),c.jsx("button",{onClick:()=>l(o==="asc"?"desc":"asc"),className:"px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white hover:bg-white/30 transition-all",children:o==="asc"?"↑":"↓"})]})]})}),c.jsxs(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.4},className:"bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden",children:[c.jsx("div",{className:"overflow-x-auto",children:c.jsxs("table",{className:"w-full",children:[c.jsx("thead",{children:c.jsxs("tr",{className:"bg-white/20 border-b border-white/20",children:[c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold cursor-pointer hover:bg-white/10 transition-all",onClick:()=>d("book_number"),children:c.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[c.jsx(Gn,{className:"w-4 h-4"}),c.jsx("span",{children:"رقم الكتاب"})]})}),c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold cursor-pointer hover:bg-white/10 transition-all",onClick:()=>d("date"),children:c.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[c.jsx(Zr,{className:"w-4 h-4"}),c.jsx("span",{children:"التاريخ"})]})}),c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold cursor-pointer hover:bg-white/10 transition-all",onClick:()=>d("subject"),children:"الموضوع"}),c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold",children:c.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[c.jsx(wu,{className:"w-4 h-4"}),c.jsx("span",{children:"الفحوى"})]})}),c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold cursor-pointer hover:bg-white/10 transition-all",onClick:()=>d("department"),children:c.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[c.jsx(Gs,{className:"w-4 h-4"}),c.jsx("span",{children:"الدائرة"})]})}),c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold",children:c.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[c.jsx(Qs,{className:"w-4 h-4"}),c.jsx("span",{children:"الهامش"})]})})]})}),c.jsx("tbody",{children:u.map((y,v)=>c.jsxs(_.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:v*.05},className:"border-b border-white/10 hover:bg-white/5 table-row",children:[c.jsx("td",{className:"px-6 py-4 text-white font-medium",children:y.book_number}),c.jsx("td",{className:"px-6 py-4 text-white",children:f(y.date_day,y.date_month,y.date_year)}),c.jsx("td",{className:"px-6 py-4 text-white",children:c.jsx("div",{className:"max-w-xs truncate",title:y.subject,children:y.subject})}),c.jsx("td",{className:"px-6 py-4 text-white",children:c.jsx("div",{className:"max-w-sm truncate",title:y.content,children:y.content})}),c.jsx("td",{className:"px-6 py-4 text-white",children:c.jsx("span",{className:"bg-blue-500/20 text-blue-200 px-3 py-1 rounded-full text-sm",children:y.department})}),c.jsx("td",{className:"px-6 py-4 text-white",children:y.notes?c.jsx("div",{className:"max-w-xs truncate",title:y.notes,children:y.notes}):c.jsx("span",{className:"text-white/50",children:"-"})})]},y.id))})]})}),u.length===0&&c.jsxs("div",{className:"text-center py-12",children:[c.jsx(Gn,{className:"w-16 h-16 text-white/30 mx-auto mb-4"}),c.jsx("p",{className:"text-white/60 text-lg",children:n?"لا توجد نتائج للبحث":"لا توجد سجلات"})]})]})]})})},o2=({onNavigate:e,records:t,onLoadRecords:n,onDeleteRecord:r,onEditRecord:i})=>{const[s,o]=P.useState(1),[l]=P.useState(10),[a,u]=P.useState(""),[d,f]=P.useState(!1),[h,y]=P.useState(null),[v,x]=P.useState(!1),S=t.filter(b=>b.subject.toLowerCase().includes(a.toLowerCase())||b.content.toLowerCase().includes(a.toLowerCase())||b.department.toLowerCase().includes(a.toLowerCase())||b.book_number.toLowerCase().includes(a.toLowerCase())),m=Math.ceil(S.length/l),p=(s-1)*l,g=p+l,w=S.slice(p,g),k=()=>o(1),j=()=>o(m),N=()=>o(b=>Math.min(b+1,m)),C=()=>o(b=>Math.max(b-1,1)),V=(b,ot,ai)=>`${b}/${ot}/${ai}`,D=b=>{y(b),f(!0)},te=async()=>{if(!h)return;x(!0),(await r(h.id)).success&&(f(!1),y(null),await n()),x(!1)},qe=()=>{f(!1),y(null)};return P.useEffect(()=>{o(1)},[a]),c.jsxs("div",{className:"min-h-screen p-8",children:[c.jsxs("div",{className:"max-w-7xl mx-auto",children:[c.jsxs(_.div,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[c.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[c.jsx("div",{className:"bg-white/20 backdrop-blur-md rounded-full p-3",children:c.jsx(Hl,{className:"w-8 h-8 text-white"})}),c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-white",children:"الأرشيف العام"}),c.jsxs("p",{className:"text-white/80",children:["إدارة وتصفح جميع السجلات (",S.length," سجل)"]})]})]}),c.jsxs(_.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("welcome"),className:"bg-white/20 backdrop-blur-md text-white px-6 py-3 rounded-xl hover:bg-white/30 transition-all duration-300 flex items-center space-x-2 space-x-reverse glow-button",children:[c.jsx(li,{className:"w-5 h-5"}),c.jsx("span",{children:"العودة للرئيسية"})]})]}),c.jsx(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 mb-8 border border-white/20",children:c.jsxs("div",{className:"relative",children:[c.jsx(xu,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5"}),c.jsx("input",{type:"text",value:a,onChange:b=>u(b.target.value),placeholder:"البحث في السجلات...",className:"w-full pr-12 pl-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow"})]})}),c.jsxs(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.4},className:"bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden mb-8",children:[c.jsx("div",{className:"overflow-x-auto",children:c.jsxs("table",{className:"w-full",children:[c.jsx("thead",{children:c.jsxs("tr",{className:"bg-white/20 border-b border-white/20",children:[c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold",children:c.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[c.jsx(Gn,{className:"w-4 h-4"}),c.jsx("span",{children:"رقم الكتاب"})]})}),c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold",children:c.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[c.jsx(Zr,{className:"w-4 h-4"}),c.jsx("span",{children:"التاريخ"})]})}),c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold",children:"الموضوع"}),c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold",children:c.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[c.jsx(Gs,{className:"w-4 h-4"}),c.jsx("span",{children:"الدائرة"})]})}),c.jsx("th",{className:"px-6 py-4 text-right text-white font-semibold",children:c.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[c.jsx(Qs,{className:"w-4 h-4"}),c.jsx("span",{children:"الهامش"})]})}),c.jsx("th",{className:"px-6 py-4 text-center text-white font-semibold",children:"الإجراءات"})]})}),c.jsx("tbody",{children:w.map((b,ot)=>c.jsxs(_.tr,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:ot*.05},className:"border-b border-white/10 hover:bg-white/5 table-row",children:[c.jsx("td",{className:"px-6 py-4 text-white font-medium",children:b.book_number}),c.jsx("td",{className:"px-6 py-4 text-white",children:V(b.date_day,b.date_month,b.date_year)}),c.jsx("td",{className:"px-6 py-4 text-white",children:c.jsx("div",{className:"max-w-xs truncate",title:b.subject,children:b.subject})}),c.jsx("td",{className:"px-6 py-4 text-white",children:c.jsx("span",{className:"bg-blue-500/20 text-blue-200 px-3 py-1 rounded-full text-sm",children:b.department})}),c.jsx("td",{className:"px-6 py-4 text-white",children:b.notes?c.jsx("div",{className:"max-w-xs truncate",title:b.notes,children:b.notes}):c.jsx("span",{className:"text-white/50",children:"-"})}),c.jsx("td",{className:"px-6 py-4",children:c.jsxs("div",{className:"flex items-center justify-center space-x-2 space-x-reverse",children:[c.jsx(_.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>i(b),className:"bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-lg transition-all duration-300 glow-button",title:"تعديل",children:c.jsx(Im,{className:"w-4 h-4"})}),c.jsx(_.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>D(b),className:"bg-red-500 hover:bg-red-600 text-white p-2 rounded-lg transition-all duration-300 glow-button",title:"حذف",children:c.jsx(n2,{className:"w-4 h-4"})})]})})]},b.id))})]})}),w.length===0&&c.jsxs("div",{className:"text-center py-12",children:[c.jsx(Hl,{className:"w-16 h-16 text-white/30 mx-auto mb-4"}),c.jsx("p",{className:"text-white/60 text-lg",children:a?"لا توجد نتائج للبحث":"لا توجد سجلات"})]})]}),m>1&&c.jsx(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.6},className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20",children:c.jsxs("div",{className:"flex items-center justify-between",children:[c.jsxs("div",{className:"text-white",children:["عرض ",p+1," - ",Math.min(g,S.length)," من ",S.length," سجل"]}),c.jsxs("div",{className:"flex items-center space-x-2 space-x-reverse",children:[c.jsx(_.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:k,disabled:s===1,className:"bg-white/20 text-white p-2 rounded-lg hover:bg-white/30 transition-all disabled:opacity-50 disabled:cursor-not-allowed",title:"الصفحة الأولى",children:c.jsx(Kw,{className:"w-5 h-5"})}),c.jsx(_.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:C,disabled:s===1,className:"bg-white/20 text-white p-2 rounded-lg hover:bg-white/30 transition-all disabled:opacity-50 disabled:cursor-not-allowed",title:"السابق",children:c.jsx(Hw,{className:"w-5 h-5"})}),c.jsxs("span",{className:"bg-white/20 text-white px-4 py-2 rounded-lg",children:[s," من ",m]}),c.jsx(_.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:N,disabled:s===m,className:"bg-white/20 text-white p-2 rounded-lg hover:bg-white/30 transition-all disabled:opacity-50 disabled:cursor-not-allowed",title:"التالي",children:c.jsx($w,{className:"w-5 h-5"})}),c.jsx(_.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:j,disabled:s===m,className:"bg-white/20 text-white p-2 rounded-lg hover:bg-white/30 transition-all disabled:opacity-50 disabled:cursor-not-allowed",title:"الصفحة الأخيرة",children:c.jsx(Ww,{className:"w-5 h-5"})})]})]})})]}),d&&c.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50",children:c.jsx(_.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 max-w-md w-full mx-4",children:c.jsxs("div",{className:"text-center",children:[c.jsx(r2,{className:"w-16 h-16 text-red-400 mx-auto mb-4"}),c.jsx("h3",{className:"text-xl font-bold text-white mb-4",children:"تأكيد الحذف"}),c.jsxs("p",{className:"text-white/80 mb-6",children:['هل أنت متأكد من حذف السجل رقم "',h==null?void 0:h.book_number,'"؟',c.jsx("br",{}),"لا يمكن التراجع عن هذا الإجراء."]}),c.jsxs("div",{className:"flex space-x-4 space-x-reverse",children:[c.jsx(_.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:te,disabled:v,className:"flex-1 bg-red-500 hover:bg-red-600 text-white py-3 rounded-xl font-semibold transition-all disabled:opacity-50",children:v?"جاري الحذف...":"حذف"}),c.jsx(_.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:qe,className:"flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-xl font-semibold transition-all",children:"إلغاء"})]})]})})})]})},l2=({onNavigate:e,onApplyFilters:t})=>{const[n,r]=P.useState({subject:"",content:"",department:"",notes_filter:"",date_from_day:"",date_from_month:"",date_from_year:"",date_to_day:"",date_to_month:"",date_to_year:""}),i=["الإدارة العامة","الموارد البشرية","المالية والمحاسبة","تقنية المعلومات","الشؤون القانونية","العلاقات العامة","المشتريات والمخازن","الأمن والسلامة","الجودة والتطوير","خدمة العملاء"],s=u=>{const{name:d,value:f}=u.target;r(h=>({...h,[d]:f}))},o=()=>{const u=Object.fromEntries(Object.entries(n).filter(([d,f])=>f!==""));t(u)},l=()=>{r({subject:"",content:"",department:"",notes_filter:"",date_from_day:"",date_from_month:"",date_from_year:"",date_to_day:"",date_to_month:"",date_to_year:""})},a=Object.values(n).some(u=>u!=="");return c.jsx("div",{className:"min-h-screen p-8",children:c.jsxs("div",{className:"max-w-4xl mx-auto",children:[c.jsxs(_.div,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[c.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[c.jsx("div",{className:"bg-white/20 backdrop-blur-md rounded-full p-3",children:c.jsx(bm,{className:"w-8 h-8 text-white"})}),c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-white",children:"تصفية البيانات"}),c.jsx("p",{className:"text-white/80",children:"البحث والتصفية المتقدمة للسجلات"})]})]}),c.jsxs(_.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("welcome"),className:"bg-white/20 backdrop-blur-md text-white px-6 py-3 rounded-xl hover:bg-white/30 transition-all duration-300 flex items-center space-x-2 space-x-reverse glow-button",children:[c.jsx(li,{className:"w-5 h-5"}),c.jsx("span",{children:"العودة للرئيسية"})]})]}),c.jsx(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 form-container",children:c.jsxs("div",{className:"space-y-6",children:[c.jsxs("div",{children:[c.jsx("label",{className:"block text-white font-semibold mb-2",children:"تصفية حسب الموضوع"}),c.jsx("input",{type:"text",name:"subject",value:n.subject,onChange:s,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"أدخل كلمة أو جزء من الموضوع"})]}),c.jsxs("div",{children:[c.jsxs("label",{className:"block text-white font-semibold mb-2 flex items-center space-x-2 space-x-reverse",children:[c.jsx(wu,{className:"w-5 h-5"}),c.jsx("span",{children:"تصفية حسب اسم الموظف"})]}),c.jsx("input",{type:"text",name:"content",value:n.content,onChange:s,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"أدخل اسم الموظف أو جزء من الفحوى"})]}),c.jsxs("div",{children:[c.jsxs("label",{className:"block text-white font-semibold mb-2 flex items-center space-x-2 space-x-reverse",children:[c.jsx(Gs,{className:"w-5 h-5"}),c.jsx("span",{children:"تصفية حسب الدائرة المعنية"})]}),c.jsxs("select",{name:"department",value:n.department,onChange:s,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white focus:outline-none input-glow",children:[c.jsx("option",{value:"",className:"bg-gray-800",children:"جميع الدوائر"}),i.map((u,d)=>c.jsx("option",{value:u,className:"bg-gray-800",children:u},d))]})]}),c.jsxs("div",{children:[c.jsxs("label",{className:"block text-white font-semibold mb-2 flex items-center space-x-2 space-x-reverse",children:[c.jsx(Qs,{className:"w-5 h-5"}),c.jsx("span",{children:"تصفية حسب الهامش"})]}),c.jsxs("select",{name:"notes_filter",value:n.notes_filter,onChange:s,className:"w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white focus:outline-none input-glow",children:[c.jsx("option",{value:"",className:"bg-gray-800",children:"جميع السجلات"}),c.jsx("option",{value:"has_notes",className:"bg-gray-800",children:"تحتوي على هامش"}),c.jsx("option",{value:"no_notes",className:"bg-gray-800",children:"لا تحتوي على هامش"})]})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[c.jsxs("div",{children:[c.jsxs("label",{className:"block text-white font-semibold mb-2 flex items-center space-x-2 space-x-reverse",children:[c.jsx(Zr,{className:"w-5 h-5"}),c.jsx("span",{children:"من تاريخ"})]}),c.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[c.jsx("input",{type:"number",name:"date_from_day",value:n.date_from_day,onChange:s,min:"1",max:"31",className:"px-3 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"يوم"}),c.jsx("input",{type:"number",name:"date_from_month",value:n.date_from_month,onChange:s,min:"1",max:"12",className:"px-3 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"شهر"}),c.jsx("input",{type:"number",name:"date_from_year",value:n.date_from_year,onChange:s,min:"1900",max:"2100",className:"px-3 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"سنة"})]})]}),c.jsxs("div",{children:[c.jsxs("label",{className:"block text-white font-semibold mb-2 flex items-center space-x-2 space-x-reverse",children:[c.jsx(Zr,{className:"w-5 h-5"}),c.jsx("span",{children:"إلى تاريخ"})]}),c.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[c.jsx("input",{type:"number",name:"date_to_day",value:n.date_to_day,onChange:s,min:"1",max:"31",className:"px-3 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"يوم"}),c.jsx("input",{type:"number",name:"date_to_month",value:n.date_to_month,onChange:s,min:"1",max:"12",className:"px-3 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"شهر"}),c.jsx("input",{type:"number",name:"date_to_year",value:n.date_to_year,onChange:s,min:"1900",max:"2100",className:"px-3 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-white/60 focus:outline-none input-glow",placeholder:"سنة"})]})]})]}),c.jsxs("div",{className:"flex space-x-4 space-x-reverse pt-6",children:[c.jsxs(_.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:o,className:"flex-1 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white py-4 rounded-xl font-semibold glow-button flex items-center justify-center space-x-2 space-x-reverse",children:[c.jsx(xu,{className:"w-5 h-5"}),c.jsx("span",{children:"تطبيق التصفية"})]}),c.jsxs(_.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:l,disabled:!a,className:"px-8 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white py-4 rounded-xl font-semibold glow-button flex items-center space-x-2 space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed",children:[c.jsx(e2,{className:"w-5 h-5"}),c.jsx("span",{children:"إعادة تعيين"})]})]})]})}),c.jsx(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.4},className:"mt-8 bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20",children:c.jsxs("div",{className:"flex items-start space-x-4 space-x-reverse",children:[c.jsx(Er,{className:"w-6 h-6 text-green-400 mt-1 flex-shrink-0"}),c.jsxs("div",{children:[c.jsx("h3",{className:"text-white font-semibold mb-2",children:"نصائح للبحث الفعال:"}),c.jsxs("ul",{className:"text-white/80 space-y-1 text-sm",children:[c.jsx("li",{children:"• يمكنك استخدام جزء من الكلمة للبحث"}),c.jsx("li",{children:"• البحث غير حساس لحالة الأحرف"}),c.jsx("li",{children:"• يمكنك تطبيق عدة فلاتر في نفس الوقت"}),c.jsx("li",{children:"• اتركي الحقول فارغة إذا لم تريدي تطبيق فلتر عليها"}),c.jsx("li",{children:"• استخدمي التاريخ للبحث في فترة زمنية محددة"})]})]})]})})]})})},a2=({onNavigate:e,theme:t,onThemeChange:n})=>{const[r,i]=P.useState(t),[s,o]=P.useState(!1),l=[{id:"blue",name:"الأزرق الكلاسيكي",description:"تدرج أزرق أنيق ومهدئ",gradient:"from-blue-500 to-purple-600",preview:"linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%)"},{id:"purple",name:"البنفسجي العصري",description:"تدرج بنفسجي ووردي جذاب",gradient:"from-purple-500 to-pink-600",preview:"linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%)"},{id:"cyan",name:"الفيروزي المنعش",description:"تدرج أزرق فيروزي منعش",gradient:"from-cyan-500 to-blue-600",preview:"linear-gradient(135deg, #06b6d4 0%, #2563eb 100%)"},{id:"green",name:"الأخضر الطبيعي",description:"تدرج أخضر طبيعي ومريح",gradient:"from-green-500 to-teal-600",preview:"linear-gradient(135deg, #10b981 0%, #0d9488 100%)"}],a=f=>{i(f)},u=()=>{n(r),o(!0),setTimeout(()=>o(!1),3e3)},d=()=>{i("blue"),n("blue"),o(!0),setTimeout(()=>o(!1),3e3)};return c.jsx("div",{className:"min-h-screen p-8",children:c.jsxs("div",{className:"max-w-4xl mx-auto",children:[c.jsxs(_.div,{initial:{opacity:0,y:-30},animate:{opacity:1,y:0},className:"flex items-center justify-between mb-8",children:[c.jsxs("div",{className:"flex items-center space-x-4 space-x-reverse",children:[c.jsx("div",{className:"bg-white/20 backdrop-blur-md rounded-full p-3",children:c.jsx(Fm,{className:"w-8 h-8 text-white"})}),c.jsxs("div",{children:[c.jsx("h1",{className:"text-3xl font-bold text-white",children:"الإعدادات"}),c.jsx("p",{className:"text-white/80",children:"تخصيص مظهر البرنامج والثيم"})]})]}),c.jsxs(_.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>e("welcome"),className:"bg-white/20 backdrop-blur-md text-white px-6 py-3 rounded-xl hover:bg-white/30 transition-all duration-300 flex items-center space-x-2 space-x-reverse glow-button",children:[c.jsx(li,{className:"w-5 h-5"}),c.jsx("span",{children:"العودة للرئيسية"})]})]}),s&&c.jsxs(_.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-xl p-4 flex items-center space-x-3 space-x-reverse",children:[c.jsx(Er,{className:"w-5 h-5 text-green-400"}),c.jsx("span",{className:"text-green-200",children:"تم حفظ الإعدادات بنجاح!"})]}),c.jsxs(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 form-container mb-8",children:[c.jsxs("div",{className:"flex items-center space-x-3 space-x-reverse mb-6",children:[c.jsx(qw,{className:"w-6 h-6 text-white"}),c.jsx("h2",{className:"text-xl font-bold text-white",children:"اختيار الثيم"})]}),c.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:l.map(f=>c.jsxs(_.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.1},whileHover:{scale:1.02},onClick:()=>a(f.id),className:`
                  relative cursor-pointer rounded-2xl p-6 border-2 transition-all duration-300
                  ${r===f.id?"border-white bg-white/20":"border-white/30 bg-white/10 hover:bg-white/15"}
                `,children:[c.jsxs("div",{className:"w-full h-24 rounded-xl mb-4 relative overflow-hidden",style:{background:f.preview},children:[c.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 animate-pulse"}),r===f.id&&c.jsx("div",{className:"absolute top-2 right-2",children:c.jsx(Er,{className:"w-6 h-6 text-white"})})]}),c.jsx("h3",{className:"text-lg font-bold text-white mb-2",children:f.name}),c.jsx("p",{className:"text-white/70 text-sm",children:f.description}),r===f.id&&c.jsx(_.div,{initial:{scale:0},animate:{scale:1},className:"absolute -top-2 -right-2 bg-green-500 text-white rounded-full p-2",children:c.jsx(Er,{className:"w-4 h-4"})})]},f.id))})]}),c.jsx(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.4},className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 mb-8",children:c.jsxs("div",{className:"flex items-start space-x-4 space-x-reverse",children:[c.jsx(Xw,{className:"w-6 h-6 text-blue-400 mt-1 flex-shrink-0"}),c.jsxs("div",{children:[c.jsx("h3",{className:"text-white font-semibold mb-2",children:"معلومات حول الثيمات:"}),c.jsxs("ul",{className:"text-white/80 space-y-1 text-sm",children:[c.jsx("li",{children:"• جميع الثيمات مصممة لتوفير تجربة بصرية مريحة"}),c.jsx("li",{children:"• الألوان متناسقة ومتوافقة مع معايير إمكانية الوصول"}),c.jsx("li",{children:"• يمكنك تغيير الثيم في أي وقت"}),c.jsx("li",{children:"• التغييرات تطبق فوراً على جميع أجزاء البرنامج"})]})]})]})}),c.jsxs(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.6},className:"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 mb-8",children:[c.jsxs("div",{className:"flex items-center space-x-3 space-x-reverse mb-4",children:[c.jsx(Zw,{className:"w-6 h-6 text-white"}),c.jsx("h2",{className:"text-xl font-bold text-white",children:"معلومات البرنامج"})]}),c.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-white/80",children:[c.jsxs("div",{children:[c.jsxs("p",{children:[c.jsx("strong",{children:"اسم البرنامج:"})," برنامج الأرشفة الإلكترونية"]}),c.jsxs("p",{children:[c.jsx("strong",{children:"الإصدار:"})," 1.0.0"]}),c.jsxs("p",{children:[c.jsx("strong",{children:"المطور:"})," علي عاجل خشان المحنة"]})]}),c.jsxs("div",{children:[c.jsxs("p",{children:[c.jsx("strong",{children:"التقنيات المستخدمة:"})," React, Electron, SQLite"]}),c.jsxs("p",{children:[c.jsx("strong",{children:"تاريخ الإصدار:"})," ",new Date().getFullYear()]}),c.jsxs("p",{children:[c.jsx("strong",{children:"الترخيص:"})," MIT License"]})]})]})]}),c.jsxs(_.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.8},className:"flex space-x-4 space-x-reverse",children:[c.jsxs(_.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:u,className:"flex-1 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white py-4 rounded-xl font-semibold glow-button flex items-center justify-center space-x-2 space-x-reverse",children:[c.jsx(Om,{className:"w-5 h-5"}),c.jsx("span",{children:"حفظ الإعدادات"})]}),c.jsx(_.button,{whileHover:{scale:1.02},whileTap:{scale:.98},onClick:d,className:"px-8 bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white py-4 rounded-xl font-semibold glow-button",children:"إعادة تعيين"})]})]})})};function u2(){const[e,t]=P.useState("welcome"),[n,r]=P.useState([]),[i,s]=P.useState([]),[o,l]=P.useState(null),[a,u]=P.useState("blue"),d=async(m={})=>{try{const p=new URLSearchParams(m).toString(),g=await fetch(`http://localhost:3001/api/records?${p}`),w=await g.json();return g.ok?(r(w.records),s(w.records),w):(console.error("خطأ في تحميل السجلات:",w.error),{records:[],pagination:{total:0,pages:0}})}catch(p){return console.error("خطأ في الاتصال بالخادم:",p),{records:[],pagination:{total:0,pages:0}}}},f=async m=>{try{const p=await fetch("http://localhost:3001/api/records",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(m)}),g=await p.json();return p.ok?(await d(),{success:!0,message:g.message}):{success:!1,message:g.error}}catch(p){return console.error("خطأ في إضافة السجل:",p),{success:!1,message:"خطأ في الاتصال بالخادم"}}},h=async(m,p)=>{try{const g=await fetch(`http://localhost:3001/api/records/${m}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(p)}),w=await g.json();return g.ok?(await d(),{success:!0,message:w.message}):{success:!1,message:w.error}}catch(g){return console.error("خطأ في تحديث السجل:",g),{success:!1,message:"خطأ في الاتصال بالخادم"}}},y=async m=>{try{const p=await fetch(`http://localhost:3001/api/records/${m}`,{method:"DELETE"}),g=await p.json();return p.ok?(await d(),{success:!0,message:g.message}):{success:!1,message:g.error}}catch(p){return console.error("خطأ في حذف السجل:",p),{success:!1,message:"خطأ في الاتصال بالخادم"}}};P.useEffect(()=>{d()},[]);const v={initial:{opacity:0,x:100},in:{opacity:1,x:0},out:{opacity:0,x:-100}},x={type:"tween",ease:"anticipate",duration:.5},S=()=>{switch(e){case"welcome":return c.jsx(Kd,{onNavigate:t});case"dataEntry":return c.jsx(i2,{onNavigate:t,onAddRecord:f,onUpdateRecord:h,currentRecord:o,setCurrentRecord:l});case"report":return c.jsx(s2,{onNavigate:t,records:n});case"archive":return c.jsx(o2,{onNavigate:t,records:i,onLoadRecords:d,onDeleteRecord:y,onEditRecord:m=>{l(m),t("dataEntry")}});case"filter":return c.jsx(l2,{onNavigate:t,onApplyFilters:async m=>{const p=await d(m);s(p.records),t("archive")}});case"settings":return c.jsx(a2,{onNavigate:t,theme:a,onThemeChange:u});default:return c.jsx(Kd,{onNavigate:t})}};return c.jsx("div",{className:`min-h-screen gradient-bg ${a==="purple"?"gradient-bg-2":a==="cyan"?"gradient-bg-3":a==="green"?"gradient-bg-4":"gradient-bg"}`,children:c.jsx(v0,{mode:"wait",children:c.jsx(_.div,{initial:"initial",animate:"in",exit:"out",variants:v,transition:x,className:"min-h-screen",children:S()},e)})})}Vo.createRoot(document.getElementById("root")).render(c.jsx(ny.StrictMode,{children:c.jsx(u2,{})}));
